<script setup>
import {defineAsyncComponent, nextTick, onMounted, reactive, ref, watch,onActivated} from "vue";
import TabMenu from "@/views/projectDetail/electronicLabel/components/components/TabMenu.vue";
import { DownOutlined,FrownFilled   } from '@ant-design/icons-vue';
import split from '@/components/split/index.vue';
import { message } from "ant-design-vue";
import api from "@/api/electronicLabel.js";
import { electronicLabel } from '@/views/projectDetail/electronicLabel/store/electronicLabelStore';
import {projectDetailStore} from "@/store/projectDetail.js";
import xeUtils from "xe-utils";
const projectStore = projectDetailStore();
const electronicLabelStore = electronicLabel();
const components = new Map([
    ['bdxx',defineAsyncComponent(() => import('./components/bdxx.vue'))],
    ['fbfx',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['csxm',defineAsyncComponent(() => import('./components/csxm.vue'))],
    ['qtxm',defineAsyncComponent(() => import('./components/qtxm.vue'))],
    ['zlje',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['zygczgj',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['zcbfwf',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['jrg',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['zgjcl',defineAsyncComponent(() => import('./components/fbfx.vue'))],
    ['fbrgyclhsb',defineAsyncComponent(() => import('./components/fbfx.vue'))]
]);
const expandedKeys = ref([]);
const selectedKeys = ref();
// {
//   title: '招标项目1',
//       key: '0-1',
//     levelType:'1',
//     children: [
//   {
//     title: '5#楼',
//     key: '0-1-0',
//     levelType:'2',
//     // isLeaf: true,
//     children: [{
//       title: '建筑工程',
//       key: '0-1-0-1',
//       isLeaf: true,
//       levelType:'3',
//     }, {
//       title: '电气工程',
//       key: '0-0-2',
//       isLeaf: true,
//       levelType:'3',
//     }]
//   },
//   {
//     title: '6#楼',
//     key: '0-1-1',
//     levelType:'2',
//     // isLeaf: true,
//     children: [{
//       title: '建筑工程',
//       key: '0-1-1-1',
//       isLeaf: true,
//       levelType:'3',
//     }, {
//       title: '电气工程',
//       key: '0-1-1-2',
//       isLeaf: true,
//       levelType:'3',
//     }]
//   }
let treeData = ref([]);
const isExpand = ref(true);
let winWidth = ref();
watch(
    () => isExpand.value,
    () => {
      if (!isExpand.value) {
        winWidth.value = window.innerWidth;
      }
    }
);
const refMap = new Map();
const activeKey = ref();//'fbfx'
const componentRef = ref('');
const getActiveKey = (arge) => {
  activeKey.value = arge.key;
  setTimeout(()=>{
    if(!refMap.has(activeKey.value) && !!componentRef.value){
      // 调用动态组件中的方法
      refMap.set(activeKey.value, componentRef.value);
    }
    console.log(refMap);
    refMap.get(activeKey.value)?.initPage(treeActiveData.value)
  },100);
};

const tabMenuRef = ref(null);
const treeActiveData = ref({});

const selectChange = (selectedKeys,e) => {
  treeActiveData.value = e.node.dataRef;
  electronicLabelStore.SET_TREE_ACTIVE(e.node);
  tabMenuRef.value.setFilter(treeActiveData.value)
};
const loading = ref(false);
const getTreeData = async ()=>{
  let apiData = {
    zbConstructId: electronicLabelStore.ppjg?.zbId,
  };
  try {
    loading.value = true;
    console.log(`api-leftTree参数`,apiData);
    const res = await api.leftTree(apiData);
    console.log(`api-leftTree返回值`,res);
    if(res.status === 200){
      treeData.value = xeUtils.toArrayTree(res.result);
      // 有值代表，之前已经操作过
      treeActiveData.value = treeData.value[0];
      //默认选中项效果
      selectedKeys.value = [treeActiveData.value.id];
      electronicLabelStore.SET_TREE_ACTIVE(treeActiveData.value);
      tabMenuRef.value.setFilter(treeActiveData.value);
    }else{
      message.error(res.message);
    }
  }catch (e) {
    console.error('api.leftTree=>catch',e);
    message.error(e.message);
  }finally {
    loading.value = false;
  }
}

onActivated(()=>{
  getTreeData();
})
onMounted(()=>{
  // treeActiveData.value = treeData[0];
  //tabMenuRef.value.setFilter(treeActiveData.value);
})

</script>

<template>
  <div class="common data-matching">
    <split :horizontal="false" :ratio="isExpand ? '1/8' : `47/${winWidth}`"
           :minHorizontalTop="180" :maxHorizontalTop="600"
           onlyPart="all" style="height: 60vh;overflow: auto" mode="vertical">
      <template #one>
        <div class="tree-left common-aside ">
            <a-directory-tree v-if="treeData.length"  show-icon class="table-scrollbar common-aside-tree"
                              v-model:selectedKeys="selectedKeys" :tree-data="treeData" @select="selectChange"
                              :field-names="{title: 'name', children: 'children', key: 'id'}"
                              default-expand-all>
              <template #switcherIcon="{ switcherCls }"><down-outlined :class="switcherCls" /></template>
<!--              <template #icon="{ key, selected, dataRef }">-->
<!--                <template v-if='dataRef.matchId'>-->
<!--                  <icon-font type="icon-duigou"/>-->
<!--                </template>-->
<!--                <template v-else>-->
<!--                  <icon-font  type="icon-zengjia-lan"/>-->
<!--                </template>-->
<!--           icon-zengjia-lan   icon-tishi-hui  icon-tishi-cheng  &ndash;&gt;-->
<!--              </template>-->
            </a-directory-tree>
        </div>
      </template>
      <template #two>
        <TabMenu @getActiveKey="getActiveKey" ref="tabMenuRef"/>
        <div class="edit-right table-content" ref="componentRefP" style="overflow: auto;">
            <keep-alive>
              <component :is="components.get(activeKey)" :key="activeKey"  ref="componentRef" />
            </keep-alive>
        </div>
      </template>
    </split>
  </div>
</template>

<style scoped lang="scss">
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';
.data-matching {
  //height: 80%;

  display: flex;
  .tree-left {
    //width: 150px;
    border: 1px solid #B9B9B9;
    height: 100%;
    width: 100%;
    border-radius: 2px;
  }
  ::v-deep(.edit-right) {
    height: calc( 100% - 26px );
    .right-top {
      font-size: 12px;
      color: #000000;
      margin: 5px 2px;
    }

  }
}
.table-content {
  height: 100%;
  //user-select: none;
  ::v-deep(.vxe-table) {
    // .vxe-cell--tree-node{
    //   // left: 0!important;
    //   padding-left: 0!important;
    // }
  }
  ::v-deep(.vxe-table .row-unit) {
    background: #e6dbeb;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #efe9f2;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #dce6fa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .row-qd .code-color) {
    color: #ce2929;
  }
  ::v-deep(.vxe-table .normal-info .code-color) {
    color: #ce2929;
    .vxe-tree-cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      //max-height: 3.0em; /* 高度为字体大小的两倍 */
      //line-height: 1.5em; /* 行高 */
      //height: auto; /* 高度为行高的两倍 */
    }
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
  ::v-deep(.surely-table-header-cell):hover {
    .icon-close-s {
      opacity: 1;
    }
  }
}
.common-aside {
  width: 100%;
  :deep(.ant-tree) {
    width: 100%;
    background: transparent;
    .ant-tree-node-content-wrapper {
      flex: none !important;
    }
    .ant-tree-switcher {
      display: flex;
      align-items: center;
      width: 10px;
    }
    .ant-tree-switcher-icon {
      display: flex;
      align-items: center;
      margin: 0 0 0 5px;
    }
  }
}
:deep(.leftIsExpand) {
  .ant-tree-switcher {
    width: 13px;
  }
}
</style>