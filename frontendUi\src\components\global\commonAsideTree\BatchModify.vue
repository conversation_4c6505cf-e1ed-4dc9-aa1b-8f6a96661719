<!--
 * @Descripttion: 批量修改名称
 * @Author: renmingming
 * @Date: 2024-07-30 11:02:13
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-13 16:20:17
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="800"
    @close="cancel"
    v-model:modelValue="show"
    title="批量修改名称"
  >
    <div class="batch-modify">
      <div class="left">
        <div class="head-check">
          <a-checkbox-group
            v-model:value="projectType"
            @change="projectTypeChange"
            style="width: 100%"
          >
            <a-row>
              <a-col
                :span="12"
                style="text-align: center; padding: 10px 0"
              >
                <a-checkbox :value="2">全选单项工程</a-checkbox>
              </a-col>
              <a-col
                :span="12"
                style="text-align: center; padding: 10px 0"
              >
                <a-checkbox :value="3">全选单位工程</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
        <div class="tree-content">
          <a-tree
            class="common-aside-tree"
            checkable
            block-node
            :checkStrictly="true"
            :tree-data="copyTreeData"
            v-model:checkedKeys="checkedKeys"
            :fieldNames="{ title: 'name', key: 'id', children: 'children' }"
            :defaultExpandAll="true"
            @check="checkTree"
          >
            <template #switcherIcon="row">
              <div style="display: flex">
                <caret-down-outlined :class="row.switcherCls" />
                <!-- <icon-font style="font-size: 14px" :type="getIconType(row)" /> -->
              </div>
            </template>
            <template #title="item">
              <div v-if="!item.isNameEdit">
                <span :style="nameStyle(item.isEligible)">
                  <icon-font
                    style="font-size: 14px"
                    :type="getIconType(item)"
                  />
                  {{ item.name }}</span>
              </div>
              <a-input
                v-else
                :placeholder="getPlaceholder(item.levelType)"
                v-model:value="findNodeById(gData, item.id).name"
                type="text"
                @keyup="item.name = inputName(item.name)"
                @blur="saveInfo(findNodeById(gData, item.id))"
                class="my-input"
                ref="clickInsideContent"
              />
            </template>
          </a-tree>
        </div>
      </div>
      <div class="right">
        <div class="find-replace">
          <a-divider orientation="left">查找替换</a-divider>
          <div class="opt-form">
            <a-row
              :gutter="10"
              style="padding: 10px 0"
            >
              <a-col :span="18"><a-input
                  v-model:value="findInfo.keyword"
                  placeholder="请输入查找内容"
                /></a-col>
              <a-col :span="6"><a-button @click="findHandler">查找</a-button></a-col>
            </a-row>
            <a-row
              :gutter="10"
              style="padding: 10px 0"
            >
              <a-col :span="18"><a-input
                  v-model:value="findInfo.replace"
                  @change="inputChange('th',$event)"
                  placeholder="请输入替换内容"
                /></a-col>
              <a-col :span="6"><a-button @click="replaceHandler">替换</a-button></a-col>
            </a-row>
          </div>
        </div>
        <div class="add-prefix">
          <a-divider orientation="left">添加前缀</a-divider>
          <div class="opt-form">
            <a-checkbox-group
              v-model:value="prefixType"
              style="width: 100%"
            >
              <a-row>
                <a-col
                  :span="12"
                  style="text-align: center; padding: 10px 0"
                >
                  <a-checkbox value="projectName">项目名称</a-checkbox>
                </a-col>
                <a-col
                  :span="12"
                  style="text-align: center; padding: 10px 0"
                >
                  <a-checkbox value="singleName">单项名称</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
            <a-row
              :gutter="10"
              style="padding: 10px 0"
            >
              <a-col :span="24"><a-input
                  v-model:value="otherPrefix"
                  @change="inputChange('qz',$event)"
                  placeholder="输入其他前缀内容"
                /></a-col>
              <a-col
                :span="24"
                style="text-align: right; margin-top: 10px"
              ><a-button @click="prefixTypeChange">添加前缀</a-button></a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="cancel">取消</a-button>
      <a-button
        type="primary"
        @click="handleOk"
        :loading="submitLoading"
      >确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { CaretDownOutlined } from '@ant-design/icons-vue';
import { inputName } from '@/utils/index';
import api from '@/api/projectDetail.js';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();

let projectType = ref([]);
let copyTreeData = ref([]);
const props = defineProps(['visible', 'treeData']);
const emits = defineEmits(['update:visible']);
const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emits('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      open();
    }
  }
);
const inputChange = (type, e) => {
  //替换和前缀一些限制
  const val = inputName(e.target.value);
  console.log(type, e, val);
  type === 'qz' ? (otherPrefix.value = val) : (findInfo.value.replace = val);
};
/**
 * 初始给单位添加各自得单项名称
 */
const unitAddParentSingleName = () => {
  const projectName = copyTreeData.value[0].name;
  loopTree(copyTreeData.value, item => {
    const child = item?.children || [];
    if (item.levelType === 2 && child.length > 0 && child[0].levelType === 3) {
      item.children.forEach(unitItem => {
        unitItem.singleNamePrefix = item.name;
        unitItem.projectNamePrefix = projectName;
      });
    }
    if (item.levelType === 2) {
      item.projectNamePrefix = projectName;
    }
  });
};
const insertString = (originalString, position, insertionString) => {
  return (
    originalString.slice(0, position) +
    insertionString +
    originalString.slice(position)
  );
};
// 添加前缀
let prefixType = ref([]);
let otherPrefix = ref('');
const prefixTypeChange = () => {
  loopTree(copyTreeData.value, item => {
    if (isCheckedKey(item.id)) {
      let nextPrefixIndex = 0;
      // 查找项目、单项、其他前缀索引位置，判断是否已有
      // 当项目名称、单位名称前缀添加过之后再去名称中查找索引位置，避免添加的索引和本身的名称一致
      const pIndex =
        item.projectNamePrefixIndex >= 0
          ? item.name.indexOf(item.projectNamePrefix)
          : -1;
      const sIndex =
        item.singleNamePrefixIndex >= 0
          ? item.name.indexOf(item.singleNamePrefix)
          : -1;
      let oIndex = -1;
      if (otherPrefix.value) {
        oIndex =
          item.otherPrefixIndex >= 0
            ? item.name.indexOf(otherPrefix.value)
            : -1;
      } else {
        if (item.otherPrefixIndex || item.otherPrefixIndex === 0) {
          oIndex = item.otherPrefixIndex;
        }
      }
      if (
        prefixType.value.includes('projectName') &&
        [2, 3].includes(item.levelType) &&
        pIndex < 0 &&
        pIndex !== item.projectNamePrefixIndex
      ) {
        // 添加项目前缀，必须是单项、单位，并且查找到得位置不能和存储得位置一致，一致不用添加
        item.name = `${item.projectNamePrefix}${item.name}`;
        item.projectNamePrefixIndex = 0;
        item.isChange = true;
        nextPrefixIndex =
          item.projectNamePrefixIndex + item.projectNamePrefix.length;
        if (item.otherPrefixIndex >= 0) {
          // 添加项目名称前缀时，已经添加了其他前缀，则要更新其他前缀的位置
          item.otherPrefixIndex += item.projectNamePrefix.length;
        }
        if (item.singleNamePrefixIndex >= 0) {
          // 添加项目名称前缀时，已经添加了单位前缀，则要更新单位前缀的位置
          item.singleNamePrefixIndex += item.projectNamePrefix.length;
        }
      }
      if (
        prefixType.value.includes('singleName') &&
        [3].includes(item.levelType) &&
        sIndex < 0 &&
        sIndex !== item.singleNamePrefixIndex
      ) {
        // 添加单项前缀，必须是单位，并且查找到得位置不能和存储得位置一致，一致不用添加
        if (pIndex === item.projectNamePrefixIndex) {
          nextPrefixIndex += item.projectNamePrefix.length;
        }
        // 添加单项名称只能给单位添加

        item.name = insertString(
          item.name,
          nextPrefixIndex,
          item.singleNamePrefix
        );
        item.isChange = true;
        item.singleNamePrefixIndex = nextPrefixIndex;
        nextPrefixIndex += item.singleNamePrefix.length;
        if (item.otherPrefixIndex >= 0) {
          // 添加单位名称前缀时，已经添加了其他前缀，则要更新其他前缀的位置
          item.otherPrefixIndex += item.singleNamePrefix.length;
        }
      }
      if (otherPrefix.value && oIndex < 0 && oIndex !== item.otherPrefixIndex) {
        // 添加其他前缀，并且查找到得位置不能和存储得位置一致，一致不用添加
        if (
          pIndex === item.projectNamePrefixIndex &&
          [2, 3].includes(item.levelType)
        ) {
          // 如果项目名称位置一致，则将现有得位置累加到下一个前缀索引
          nextPrefixIndex += item.projectNamePrefix.length;
        }
        if (
          sIndex === item.singleNamePrefixIndex &&
          [3].includes(item.levelType)
        ) {
          // 如果单项名称位置一致，则将现有得位置累加到下一个前缀索引
          nextPrefixIndex += item.singleNamePrefix.length;
        }
        item.name = insertString(item.name, nextPrefixIndex, otherPrefix.value);
        item.isChange = true;
        item.otherPrefix = otherPrefix.value;
        item.otherPrefixIndex = nextPrefixIndex;
      }
      if (
        !otherPrefix.value &&
        oIndex >= 0 &&
        oIndex === item.otherPrefixIndex
      ) {
        // 清楚其他前缀时的处理，需去除添加的前缀
        const start = item.otherPrefixIndex + item.otherPrefix.length;
        item.name =
          item.name.slice(0, item.otherPrefixIndex) + item.name.slice(start);
        item.otherPrefixIndex = -2; // 清除重置索引为-2，是因为默认的查找的索引是-1，两者相同会不添加前缀
      }
      if (
        sIndex >= 0 &&
        !prefixType.value.includes('singleName') &&
        [3].includes(item.levelType) &&
        sIndex === item.singleNamePrefixIndex
      ) {
        // 清除单项前缀时的处理，需去除添加的前缀
        const start = item.singleNamePrefixIndex + item.singleNamePrefix.length;
        item.name =
          item.name.slice(0, item.singleNamePrefixIndex) +
          item.name.slice(start);
        item.singleNamePrefixIndex = -2;
        updatePrefixIndexLocation('singleName', item);
      }
      if (
        pIndex >= 0 &&
        !prefixType.value.includes('projectName') &&
        [2, 3].includes(item.levelType) &&
        pIndex === item.projectNamePrefixIndex
      ) {
        // 清除项目前缀时的处理，需去除添加的前缀
        item.name = item.name.slice(item.projectNamePrefix.length);
        item.projectNamePrefixIndex = -2;
        updatePrefixIndexLocation('projectName', item);
      }
    }
  });
};
const updatePrefixIndexLocation = (type, item) => {
  if (type === 'projectName') {
    // 如果清除项目名称，则更新对应的单项名称和其他前缀索引位置
    item.singleNamePrefixIndex -= item.projectNamePrefix.length;
    item.otherPrefixIndex -= item.projectNamePrefix.length;
  }
  if (type === 'singleName') {
    // 如果清除单项名称，则更新对应的其他前缀索引位置
    item.otherPrefixIndex -= item.singleNamePrefix.length;
  }
};

let findInfo = ref({
  keyword: '',
  replace: '',
});
let checkedKeys = ref({ checked: [] });
//单项、单位全选处理
const projectTypeChange = () => {
  console.info(777777777777, projectType.value);
  checkedKeys.value.checked = getAllKeys(copyTreeData.value, [], item => {
    return projectType.value.includes(item.levelType);
  });
};
const isCheckedKey = id => {
  return checkedKeys.value?.checked?.includes(id);
};
// 查找
const findHandler = () => {
  const keyword = findInfo.value.keyword;
  if (!keyword) return;
  loopTree(copyTreeData.value, item => {
    item.isEligible = false;
    if (isCheckedKey(item.id) && item.name.indexOf(keyword) >= 0) {
      // 符合条件查询
      item.isEligible = true;
    } else {
      deleteCheckKeyById(item.id);
    }
  });
};
/**
 * 替换
 */
const replaceHandler = () => {
  const { keyword, replace } = findInfo.value;
  if (!keyword) return;
  loopTree(copyTreeData.value, item => {
    if (isCheckedKey(item.id) && item.isEligible) {
      item.name = item.name.replace(new RegExp(`${keyword}`, 'g'), replace);
      item.isChange = true;
    }
  });
};

let submitLoading = ref(false);
const handleOk = () => {
  let changeData = [];
  submitLoading.value = true;
  loopTree(copyTreeData.value, item => {
    if (item.isChange) {
      changeData.push({ id: item.id, name: item.name });
    }
  });
  api
    .batchModifyName({
      constructId: copyTreeData.value[0]?.id,
      data: changeData,
    })
    .then(res => {
      console.log(res);
      message.success('修改成功');
      submitLoading.value = false;
      store.SET_IS_REFRESH_PROJECT_TREE(true);
      emits('update:visible', false);
    });
};
/**
 * 循环树
 */
const loopTree = (arr, callback = () => {}) => {
  arr.forEach((item, index) => {
    callback(item);
    if (item.children && item.children.length) {
      loopTree(item.children, callback);
    }
  });
};
const nameStyle = isEligible => {
  if (isEligible) {
    return {
      color: 'red',
    };
  }
  return {};
};
/**
 * 获取key
 * @param {*} arr
 * @param {*} keys
 */
const getAllKeys = (
  arr,
  keys = [],
  callback = () => {
    return true;
  }
) => {
  let allKeys = keys;
  arr.forEach(item => {
    if (callback(item)) {
      allKeys.push(item.id);
    }
    if (item.children && item.children.length) {
      allKeys = getAllKeys(item.children, keys, callback);
    }
  });
  return allKeys;
};

const checkTree = (checkKeys, e) => {
  console.log(checkKeys, e);
  const keys = getAllKeys([e.node.dataRef]);
  keys.forEach(item => {
    if (!isCheckedKey(item) && e.checked) {
      checkedKeys.value.checked?.push(item);
    }
    if (!e.checked && isCheckedKey(item)) {
      deleteCheckKeyById(item);
    }
  });
  console.log(checkedKeys.value);
};
const deleteCheckKeyById = id => {
  const index = checkedKeys.value.checked?.indexOf(id);
  if (index >= 0) {
    checkedKeys.value.checked?.splice(index, 1);
  }
};
const open = () => {
  copyTreeData.value = JSON.parse(JSON.stringify(props.treeData));
  checkedKeys.value.checked = getAllKeys(copyTreeData.value, []);
  init();
  unitAddParentSingleName();
};
const init = () => {
  //打开弹框初始化数据
  projectType.value = [];
  findInfo.value = {
    keyword: '',
    replace: '',
  };
  prefixType.value = [];
  otherPrefix.value = '';
};
const cancel = () => {
  emits('update:visible', false);
};

const getPlaceholder = levelType => {
  const map = {
    1: '项目工程',
    2: '单项工程',
    3: '单位工程',
  };
  return `请输入${map[levelType]}名称`;
};
// 根据id查找目标节点
function findNodeById(treeData, targetId) {
  // 遍历树形数据
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    // 检查当前节点是否是目标节点
    if (node.id === targetId) {
      return node; // 返回找到的节点
    }
    // 如果当前节点有子节点，则递归查找
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(node.children, targetId);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到了目标节点，则返回
      }
    }
  }
  // 如果遍历完整个树都没有找到目标节点，则返回 null
  return null;
}
const getIconType = row => {
  // console.log(row);
  const map = {
    1: 'icon-gongchengxiangmu',
    2: 'icon-danxianggongcheng',
    3: 'icon-danweigongcheng1',
  };
  return map[row.levelType] || 'icon-danweigongcheng1';
};
const saveInfo = row => {
  // 移开鼠标清除编辑状态

  const oldRow = props.treeData?.find(i => i.id === row.id);
  if (isRepeat(row.name, row.parentId, row.id)) {
    // $table.revertData(row, field);
    row.name = oldRow.name;
    row.copyName = oldRow.name;
    message.error('同级名称不可重复，请重新输入名称');
    return;
  }
  row.isNameEdit = false;
  row.name = row.name.trim();
  let value = row.name;
  if (!row.name) {
    row.name = row.copyName;
    return;
  }
  //字符长度50限制
  if (row.name.length > 50) {
    row.name = value.slice(0, 50);
  }
  //判断值是否变化
  // if (oldRow.name === row.name) return;
};
</script>
<style lang="scss" scoped>
.footer-btn-list {
  margin-top: 15px;
}
.batch-modify {
  display: flex;
  border: 1px solid rgba(0, 0, 0, 0.06);
  .head-check {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
  .tree-content {
    padding: 10px;
    height: 400px;
    ::v-deep .ant-tree .ant-tree-switcher {
      display: flex;
      align-items: center;
    }
  }
  .left {
    width: 60%;
  }
  .right {
    width: 40%;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
    .opt-form {
      padding: 0 15px;
    }
    .find-replace,
    .add-prefix {
      padding-top: 13px;
    }
  }
}
</style>
