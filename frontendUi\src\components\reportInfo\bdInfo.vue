<!--
 * @Descripttion: 
-->
<template>
  <div class="bd-info">
    <vxe-table
      :data="props.tableData?.get('bdxx')"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      class="table-scrollbar table-edit-common"
      :cell-class-name="cellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: autoClearEdit,
        beforeEditMethod: cellBeforeEditMethod,
      }"
      ref="vexTable"
      show-overflow
      height="500"
      @cell-click="cellClickEvent"
    >
      <vxe-column width="60" field="dispNo"></vxe-column>
      <vxe-column title="名称" field="name"></vxe-column>
      <vxe-column
        title="内容"
        field="remark"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="!isDateFiled(row)"
            :clearable="false"
            placeholder="请输入内容"
            v-model="row.remark"
            type="text"
            name="name"
          ></vxe-input>
          <a-date-picker
            placeholder="请选择时间"
            open
            valueFormat="YYYY-MM-DD"
            v-else
            @change="
              (date, dateString) => {
                vexTable.clearEdit();
              }
            "
            v-model:value="row.remark"
          />
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useCellClick } from '@/hooks/useCellClick.js';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
let vexTable = ref();
const isDateFiled = row => {
  return ['编制时间', '核对(复核)时间'].includes(row.name);
}; // 是否是日期字段

let autoClearEdit = ref(true); // 当点击表格之外或者非编辑列之后，是否自动清除单元格的激活状态
const cellClickEvent = info => {
  autoClearEdit.value = isDateFiled(info.row.name);
  useCellClickEvent(info);
};
const props = defineProps(['tableData']);
const cellClassName = ({ row, column, $columnIndex }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (row.requiredFlag === 1 && column.field === 'name') {
    return 'required-fields';
  }
  return selectName;
};
</script>

<style lang="scss" scoped>
::v-deep(.vxe-table .vxe-body--row .required-fields) {
  color: #de3f3f;
}
</style>
