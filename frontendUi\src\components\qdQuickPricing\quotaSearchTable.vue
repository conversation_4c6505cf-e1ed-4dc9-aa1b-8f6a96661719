<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2024-04-15 16:51:03
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-14 15:30:20
-->
<template>
  <div class="table-container">
    <div class="search-container">
      <a-input
        size="small"
        style="width: 200px"
        v-model:value="postData.bdName"
        @pressEnter="onSearch"
        placeholder="请输入编码、名称或项目特征关键字"
      />
      <a-button
        size="small"
        type="primary"
        style="margin: 0 0 6px 11px"
        @click="onSearch"
      >查找</a-button>
    </div>
    <div class="table-wrapper">
      <vxe-table
        :data="tableData.list"
        @cell-dblclick="dbClick"
        height="100%"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        @scroll="getScroll"
        min-height="100px"
        :scroll-y="{ scrollToTopOnChange: false, enabled: true }"
      >
        <vxe-column
          field="deCode"
          title="编码"
        ></vxe-column>
        <vxe-column
          field="deName"
          title="名称"
        ></vxe-column>
        <vxe-column
          field="unit"
          title="单位"
        ></vxe-column>
        <vxe-column
          field="price"
          title="单价"
        ></vxe-column>
        <vxe-column
          field="libraryName"
          title="专业"
        ></vxe-column>
      </vxe-table>
    </div>
  </div>
</template>
<script setup>
import { globalData, dbClick } from './status.js';
import { watch, ref, reactive, computed, onMounted } from 'vue';
import csProject from '@/api/csProject';
import { projectDetailStore } from '@/store/projectDetail.js';

const store = projectDetailStore();
let searchKey = ref('');
let tableData = reactive({
  list: [],
  total: 1,
  isLast: false,
});
let postData = ref({
  bdName: '',
  page: 1,
  limit: 10,
  constructId: store.currentTreeGroupInfo?.constructId,
  spId: store.currentTreeGroupInfo?.singleId,
  upId: store.currentTreeInfo?.id,
});

const onSearch = () => {
  postData.value.page = 1;
  tableData.isLast = false;
  tableData.list = [];
  getTableList();
};

const getTableList = () => {
  if (tableData.isLast || !postData.value.bdName) return;
  postData.value.spId = store.currentTreeGroupInfo?.singleId;
  postData.value.upId = store.currentTreeInfo?.id;
  csProject.selectDeByBdName({ ...postData.value }).then(res => {
    console.log('🚀 ~ 定额查找1111:', res);
    let list =
      postData.value.page == 1
        ? [...res.data]
        : [...tableData.list, ...res.data];
    if (store.deStandardReleaseYear === '22') {
      tableData.list = list.map(i => {
        i.price =
          store.taxMade == 1 ? i?.priceBaseJournal : i?.priceBaseJournalTax;
        return i;
      });
    } else {
      tableData.list = list.map(i => {
        return i;
      });
    }
    tableData.total = res.total;
    tableData.isLast = res.data.length < postData.value.limit;
  });
};

const getScroll = event => {
  // 判断是否滚动到底部
  const isBottom =
    Math.ceil(event.scrollTop + event.$event.target.clientHeight) >=
    event.scrollHeight;
  if (isBottom) {
    postData.value.page++;
    getTableList();
  }
};

getTableList();

const searchText = computed(() => {
  return globalData.searchText;
});

watch(searchText, (newV, oldV) => {
  postData.value.bdName = newV;
  onSearch();
});

onMounted(() => {
  postData.value.bdName = searchText.value;
  onSearch();
});
</script>
<style lang="scss" scoped>
.table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-container {
    height: 30px;
    margin: 0;
  }
  .table-wrapper {
    flex: 1;
    height: calc(100% - 30px);
  }
}
</style>
