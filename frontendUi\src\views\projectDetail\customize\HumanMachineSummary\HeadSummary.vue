<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2025-02-25 10:36:56
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-10 10:12:19
-->
<template>
  <div class="head-summary">
    <div style="margin-right: 10px" v-if="+projectStore.asideMenuCurrentInfo?.key == 14">
      <a-dropdown :trigger="['click']">
        <a @click.prevent>
          <icon-font
            type="icon-rencaijiguolv"
            style="font-size: 14px; margin: 3px 2px 0 0"></icon-font>
          <span style="color: black">过滤</span>
          <DownOutlined />
        </a>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <a-checkbox-group
                v-model:value="filterSelVal"
                style="width: 100%"
                @change="filterChange">
                <a-checkbox :value="1">人工</a-checkbox>
                <br />
                <a-checkbox :value="2">材料</a-checkbox>
                <br />
                <a-checkbox :value="3">机械</a-checkbox>
                <br />
                <a-checkbox :value="4">设备</a-checkbox>
                <br />
                <a-checkbox :value="5">主材</a-checkbox>
              </a-checkbox-group>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <icon-font type="icon-shichangjiaheji" style="font-size: 14px; margin: 3px 2px 0 0"></icon-font>
    <span v-if="isDeType('12')">市场价合计：{{ stateInfo.total }}</span>
    <span v-else>不含税市场价合计：{{ stateInfo.priceMarketTotal }}</span>
    <span class="vertical"></span>
    <span v-if="props.selectData.length">数量合计：{{ stateInfo.totalNumber }}</span>
    <span v-else>价差合计：{{ stateInfo.priceDifferencSumTotal }}</span>
  </div>
</template>

<script setup>
import { watchEffect, reactive, getCurrentInstance } from 'vue';
import decimalUtils from '@/utils/decimalUtils';
import { isDeType } from './tableColumns.js';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const props = defineProps({
  selectData: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
});
let filterSelVal = [1, 2, 3, 4, 5];
let lastFilyerSelVal=[]
let stateInfo = reactive({
  totalNumber: 0,
  priceMarketTotal: 0,
  priceDifferencSumTotal: 0,
  total: 0,
});
const calcHandler = listData => {
  let totalNumberList = [];
  let priceMarketList = [];
  let priceDifferencSumList = [];
  let totalList = [];
  for (let item of listData) {
    if (item.markSum && [1, 2].includes(item.levelMark)) continue; // 配比行跳过
    totalNumberList.push(item.totalNumber);
    priceMarketList.push(item.priceMarketTotal);
    priceDifferencSumList.push(item.priceDifferencSum);
    totalList.push(item.total);
  }
  stateInfo.total = decimalUtils.addParams(totalList);
  stateInfo.totalNumber = decimalUtils.addParams(totalNumberList);
  stateInfo.priceMarketTotal = decimalUtils.addParams(priceMarketList);
  stateInfo.priceDifferencSumTotal = decimalUtils.addParams(priceDifferencSumList);
};
const filterChange = val => {
  if(val.length==0){
    filterSelVal=lastFilyerSelVal
    return false
  }
  lastFilyerSelVal=val
  
  bus.emit('rcjhzFiterType', val);
};
watchEffect(() => {
  const listData = props.selectData?.length ? props.selectData : props.tableData;

  calcHandler(listData);
});
</script>
<style lang="scss" scoped>
.head-summary {
  display: flex;
  align-items: center;
  height: 100%;
  flex-wrap: nowrap;
  span {
    padding: 0 2px;
    font-size: 12px;
    line-height: 14px;
    color: #2a2a2a;
  }
  .vertical {
    margin: 0 8px;
    padding: 0;
    width: 1px;
    height: 13px;
    background-color: #007aff;
  }
}
</style>
