<!--
 * @Descripttion: 输入框，表格下拉
 * @Author: sunchen
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-06 11:22:17
-->
<template>
  <vxe-pulldown
    ref="pulldownRef"
    class="pulldown-wrap"
    v-model="pulldown.visible"
    :placement="placement"
    transfer="true"
  >
    <template #default>
      <div class="pulldown-wraps">
        <vxe-input
          autofocus
          v-model.trim="modeValues"
          ref="vxeInputRef"
          :maxlength="50"
          :clearable="false"
          placeholder="请输入或下拉选择内容"
          @blur="blur"
          @keyup="
            needJYlist.includes(tableType)
              ? (modeValues = modeValues.replace(
                  /[^0-9a-zA-Z|\-|\*|\+|\/|\.|\_|(|)]/g,
                  ''
                ))
              : ''
          "
        >
          <template #suffix>
            <i
              style="cursor: pointer"
              class="vxe-icon-caret-down icons"
              @click="focusEvent"
            ></i>
          </template>
        </vxe-input>
      </div>
    </template>
    <template #dropdown>
      <div class="editTable">
        <edit-Table
          :tableType="tableType"
          @selectTarget="selectTarget"
          :propInfo="propInfo"
        ></edit-Table>
      </div>
    </template>
  </vxe-pulldown>
</template>
<script>
export default {
  name: 'vxeTableEditTable',
};
</script>
<script setup>
import EditTable from './EditTable.vue';
import { nextTick, reactive, ref } from 'vue';
import xeUtils from 'xe-utils';
import feePro from '@/api/feePro';
import { isNumber } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
const store = projectDetailStore();
// const props = defineProps(['filedValue', 'tableType', 'placement']);
const needJYlist = ['QTXM', 'DJGC'];
const emits = defineEmits(['update:filedValue', 'showTable']);
const props = defineProps({
  tableType: {
    required: true,
    type: String,
  },
  filedValue: {
    required: true,
    type: String,
  },
  placement: {
    required: false,
    type: String,
    default: 'top',
  },
  propInfo: {
    required: false,
    type: Object,
    default: {},
  },
});
const modeValues = ref('');
const vxeInputRef = ref(null);

modeValues.value = props.filedValue;

nextTick(() => {
  console.log('props.filedValue', props.filedValue);
  vxeInputRef.value?.focus();
  getAllCostCodeList();
});

const pulldown = reactive({
  visible: false,
  values: '',
});

const focusEvent = () => {
  pulldown.visible = !pulldown.visible;
  emits('showTable', pulldown.visible);
};
const reg = /^([A-Za-z0-9]+|\d+[.]?\d+)([+\-*/]([A-Za-z0-9]+|\d+[.]?\d+))+$/;
const blur = () => {
  let value = xeUtils.trim(modeValues.value);
  if (props.tableType === 'QTXM') {
    let constantList = value.match(/[A-Za-z0-9]+(\.\d+)?/g);
    constantList = constantList.filter(item => {
      const isNum = !isNumber(Number(item));
      const isCode = !allCostCode.value.includes(item);
      return isNum && isCode;
    });
    if (constantList.length > 0) {
      message.error('请输入合法的费用代号或数字计算公式');
      return;
    }
  }
  emits('update:filedValue', value);
  pulldown.visible = false;
  emits('showTable', pulldown.visible);
};

const selectTarget = tar => {
  if (modeValues.value) {
    modeValues.value = `${modeValues.value} + ${tar}`;
  } else {
    modeValues.value = `${tar}`;
  }
  console.log('selectTarget', modeValues.value, tar);
  emits('update:filedValue', modeValues.value);
  pulldown.visible = false;
  emits('showTable', pulldown.visible);
};
let allCostCode = ref([]);
const getAllCostCodeList = () => {
  if (props.tableType !== 'QTXM') return;
  const formdata = {
    type: '',
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  feePro.costCodePriceQTXM(formdata).then(res => {
    if (res.status === 200 && res.result) {
      allCostCode.value = res.result.map(item => item.code);
      console.log('allCostCode', allCostCode);
    }
  });
};
</script>

<style lang="scss" scoped>
.pulldown-wrap {
  z-index: 0;
  width: 100%;
  ::v-deep(.vxe-input) {
    width: 98%;
  }
}
.input-wrap-dropdown {
  max-height: 40vh;
  z-index: 9999;
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
  .list-item {
    padding: 5px 4px;
    &:hover {
      background-color: azure;
      cursor: pointer;
    }
  }
}
.editTable {
  width: 500px;
  height: 400px;
  border: 2px solid #409eff;
  padding: 5px;
}
</style>
