<!--
 * @Descripttion: 快速设计
-->
<template>
  <common-modal
    className="dialog-comm rapid-design-dialog"
    width="1000px"
    v-model:modelValue="dialogVisible"
    title="快速设计"
    @cancel="cancel"
    @close="cancel"
    :destroyOnClose="true"
    @zoom="resetPreview"
    show-zoom
  >
    <div class="rapid-design-content">
      <div class="aside" @click="asideClick">
        <div
          :class="{ list: true, active: item.value === asideAction }"
          v-for="item in asideList"
          :key="item.value"
          :data-value="item.value"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="content">
        <div class="list-content" v-if="asideAction === 'pageConfig'">
          <div class="box-content">
            <div class="title">纸张方向</div>
            <div class="info">
              <a-radio-group
                v-model:value="formData.landSpace"
                name="radioGroup"
              >
                <a-radio :value="true">横板</a-radio>
                <a-radio :value="false">竖版</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div class="box-content" v-if="false">
            <div class="title">标题设计</div>
            <div class="info">
              <RichText></RichText>
            </div>
          </div>
          <div class="box-content">
            <div class="title">表眉设计</div>
            <div class="info">
              <div class="item">
                <span class="label">工程名称：</span>
                <a-select style="flex: 1" v-model:value="formData.projectName">
                  <a-select-option
                    v-for="item in projectNameList"
                    :key="item.key"
                    :value="item.label"
                    :disabled="item.disabled"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </div>
              <div class="item">
                <span class="label">页码展示：</span>
                <a-select style="flex: 1" v-model:value="formData.pageNumber">
                  <a-select-option
                    v-for="item in pageNumList"
                    :key="item.label"
                    :value="item.label"
                    :disabled="item.disabled"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </div>
            </div>
          </div>
        </div>

        <div class="list-content" v-else>
          <div class="box-content">
            <div class="title">内容设计</div>
            <div class="info">
              <a-checkbox
                v-for="item in contentDesignList"
                v-model:checked="formData[item.key]"
                >{{ item.label }}</a-checkbox
              >
            </div>
          </div>
        </div>
      </div>
      <div class="preview">
        <iframe
          v-if="fileUrl && previewData"
          id="myIframe"
          ref="iframeRef"
          :src="fileUrl"
          style="width: 100%; height: 100%; border: none"
        />
      </div>
    </div>
    <div class="btn-list">
      <a-button type="primary" @click="resetDefault">恢复默认设置</a-button>
      <a-button type="primary" @click="sheetSave">确定</a-button>
      <a-button @click="cancel">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { ref, nextTick, toRaw, watch, reactive } from 'vue';
import infoMode from '@/plugins/infoMode.js';
import RichText from './RichText.vue';
import csProject from '@/api/csProject';
import {
  globalData,
  getLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from './../reportFrom.js';
import XEUtils from 'xe-utils';
const emits = defineEmits(['refresh']);
const asideList = ref([
  {
    label: '页面设计',
    value: 'pageConfig',
  },
  {
    label: '报表内容',
    value: 'reportContent',
  },
]);
let asideAction = ref('pageConfig');
const asideClick = e => {
  console.log('🚀 ~ asideClick ~ e:', e);
  const value = e.target.dataset.value;
  if (value) asideAction.value = value;
};

const projectNameList = ref([
  {
    label: '`工程名称:`+{单项名称}+{单位名称}',
    key: 'unit',
  },
  {
    label: '`工程名称:`+{单项名称}',
    key: 'single',
  },
  {
    label: '`工程名称:`+{项目名称}',
    key: 'project',
  },
]);
const pageNumList = ref([
  {
    label: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
  },
]);
let formData = reactive({
  landSpace: true,
  numberZero: false,
  emptyField: false,
  projectName: '`工程名称:`+{单项名称}+{单位名称}',
  pageNumber: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
});
const projectNameListDefaultHandler = () => {
  const itemLevel = copyGlobalData?.treeParams.itemLevel;
  projectNameList.value.forEach(item => {
    item.disabled = true;
    if (itemLevel === 'project' && item.key === itemLevel) {
      item.disabled = false;
    }
    if (itemLevel === 'single' && ['project', 'single'].includes(item.key)) {
      item.disabled = false;
    }
    if (itemLevel === 'unit') {
      item.disabled = false;
    }
  });
  formData.projectName = projectNameList.value.find(
    item => item.key === itemLevel
  )?.label;
};

watch(
  () => XEUtils.clone(formData, true),
  (val, old) => {
    console.log('🚀 ~ watch ~ val:', val, old);
    if (
      val.projectName !== old.projectName ||
      val.pageNumber !== old.pageNumber
    ) {
      updateSheetEyeBrowInfo();
    }
    pdfPreview();
  },
  {
    deep: true,
  }
);

const contentDesignList = ref([
  { label: '数值0输出为空', key: 'numberZero', value: 1 },
  { label: '空字段输出为“-”', key: 'emptyField', value: 2 },
]);
let dialogVisible = ref(false);

const cancel = () => {
  dialogVisible.value = false;
};

const fileUrl = ref();
const previewData = ref(null);
const storageName = ref('rapidDesignReportForm');
let lanMuName = ref('');
let copyGlobalData = null;
const initFormDate = () => {
  const { emptyField, numberZero, print } = getLocalStorage();
  formData.landSpace = print?.landSpace;
  formData.numberZero = false;
  formData.emptyField = false;
  if (typeof numberZero === 'boolean') {
    formData.numberZero = numberZero;
  }
  if (typeof emptyField === 'boolean') {
    formData.emptyField = emptyField;
  }
  const name = getSheetEyeBrowName(
    copyGlobalData?.excelDataTemplate.sheets.sheet1
  );
  if (name) {
    formData.projectName = name;
  }
};
const open = info => {
  asideAction.value = 'pageConfig';
  lanMuName.value = info.lanMuName;
  copyGlobalData = XEUtils.clone(globalData, true);
  console.log('🚀 ~ copyGlobalData:', copyGlobalData, info);
  projectNameListDefaultHandler();
  initFormDate();
  dialogVisible.value = true;
  pdfPreview();
};
const pdfPreview = async () => {
  const data = await getShowSheetStyle(copyGlobalData);
  fileUrl.value = null;
  previewData.value = data;
  data
    ? setLocalStorage(data, storageName.value)
    : removeLocalStorage(storageName.value);
  resetPreview();
};
const resetPreview = () => {
  nextTick(() => {
    const zoom = getZoom();
    fileUrl.value = `/pdf/index.html?storageName=${storageName.value}&zoom=${zoom}`;
  });
};

let iframeRef = ref();
const getZoom = () => {
  const reportFormWidth = formData.landSpace ? 1087.5 : 770;
  const iframeWidth = document.querySelector('.preview').clientWidth;
  const zoom = (iframeWidth / reportFormWidth).toFixed(4);
  console.log(
    '🚀 ~ getZoom ~ iframeWidth / reportFormWidth:',
    iframeWidth,
    reportFormWidth
  );
  return zoom;
};

const updateSheetEyeBrowInfo = () => {
  const jsonData = copyGlobalData?.excelDataTemplate;
  const sheetInfo = jsonData.sheets.sheet1;
  const rowIndex = getSheetEyeBrowIndex(sheetInfo);
  const pageCellColumnIndex = getPageCellColumnIndex(sheetInfo);
  sheetInfo.cellData[rowIndex][0].v = formData.projectName;
  sheetInfo.cellData[rowIndex][pageCellColumnIndex].v = formData.pageNumber;
  console.log(copyGlobalData);
};

/**
 * 获取表眉行索引
 */
const getSheetEyeBrowIndex = sheetInfo => {
  return Object.values(sheetInfo.rowData).findIndex(
    item => item.field === 'sheetEyeBrow'
  );
};

const getSheetEyeBrowName = sheetInfo => {
  const rowIndex = getSheetEyeBrowIndex(sheetInfo);
  if (rowIndex === -1) return '';
  return sheetInfo.cellData[rowIndex][0].v;
};

/**
 * 获取页码单元格列开始索引
 */
const getPageCellColumnIndex = sheetInfo => {
  const rowIndex = getSheetEyeBrowIndex(sheetInfo);
  if (rowIndex === -1) return 0;
  const list = sheetInfo.mergeData.filter(item => item.startRow === rowIndex);
  if (list.length === 1) {
    // 只有一个合并单元格，则页码单元格索引为合并单元格结束列的下一个位置
    return list[0].endColumn + 1;
  }
  // 有多个合并单元格，则页码单元格索引为所有合并单元格开始列的最大值
  let pageCellColumnIndex = list.reduce((pre, cur) => {
    return cur.startColumn > pre ? cur.startColumn : pre;
  }, 0);
  return pageCellColumnIndex;
};

// 获取表单数据
const getShowSheetStyle = sheetInfo => {
  return new Promise((resolve, reject) => {
    const postData = {
      ...toRaw(copyGlobalData.treeParams.constructObj),
      itemLevel: copyGlobalData.treeParams.itemLevel,
      lanMuName: lanMuName.value,
      headLine: sheetInfo.headLine,
      isLandScape: formData.landSpace,
      jsonData: toRaw(copyGlobalData?.excelDataTemplate),
      // containsTaxCalculation: true,
      numberZero: formData.numberZero,
      emptyField: formData.emptyField,
    };
    console.log('🚀 ~ returnnewPromise ~ postData:', postData);
    csProject.displayQuickDesign(postData).then(res => {
      if (res.code == 200) {
        resolve(res.result);
      }
    });
  });
};
const sheetSave = () => {
  const postData = {
    ...toRaw(copyGlobalData.treeParams.constructObj),
    itemLevel: copyGlobalData.treeParams.itemLevel,
    lanMuName: lanMuName.value,
    headLine: copyGlobalData.headLine,
    isLandScape: formData.landSpace,
    jsonData: toRaw(copyGlobalData?.excelDataTemplate),
    // containsTaxCalculation: true,
    numberZero: formData.numberZero,
    emptyField: formData.emptyField,
  };
  csProject.confirmDisplayQuickDesign(postData).then(res => {
    console.log(
      '🚀 ~ csProject.confirmDisplayQuickDesign ~ res:',
      res,
      postData
    );

    if (res.code == 200) {
      cancel();
      emits('refresh');
    }
  });
};
const resetDefault = () => {
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否恢复当前报表默认设置',
    confirm: () => {
      restoreDesign(infoMode.hide);
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const restoreDesign = callback => {
  const postData = {
    ...toRaw(copyGlobalData.treeParams.constructObj),
    itemLevel: copyGlobalData.treeParams.itemLevel,
    lanMuName: lanMuName.value,
    headLine: copyGlobalData.headLine,
  };
  console.log('🚀 ~ postData:', postData);
  csProject.restoreOriginDesign(postData).then(res => {
    console.log(res);
    if (typeof callback === 'function') callback();
    emits('refresh');
    cancel();
  });
};
defineExpose({ open });
</script>
<style lang="scss" scoped>
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  :deep(.ant-btn) {
    margin: 0 5px;
  }
}
.rapid-design-content {
  display: flex;
  border: 1px solid #e1e1e1;
  height: calc(100% - 35px);
  min-height: 500px;
}
.box-content {
  position: relative;
  border: 1px solid #d9d9d9;
  padding: 17px 15px 13px;
  margin-bottom: 25px;
  .title {
    position: absolute;
    top: -11px;
    left: 12px;
    font-size: 14px;
    line-height: 1.5;
    padding: 0 3px;
    background: #fff;
    color: #2a2a2a;
  }
  .item {
    display: flex;
    align-items: center;
    &:last-child {
      margin-top: 10px;
    }
  }
}
.aside {
  width: 17.3%;
  border-right: 1px solid #e1e1e1;
  text-align: center;
  .list {
    font-size: 14px;
    line-height: 32px;
    color: #2a2a2a;
    cursor: pointer;
    &.active {
      color: #fff;
      background: #287cfa;
    }
  }
}
.content {
  width: 51%;
  border-right: 1px solid #e1e1e1;
  height: 100%;
  padding: 20px 10px;
  :deep(.ant-radio-wrapper) {
    margin-right: 90px;
  }
}
.preview {
  width: 31.7%;
  height: 100%;
}
</style>
