const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const TypConstant = require("../rcj_handle/TypConstant");
const InsertStrategy = require("../main_editor/insert/insertStrategy");
const InsertRcjStrategy = require("../rcj_handle/insert/insertRcjStrategy");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 补充清单 和 定额
 */
class SupplementController extends Controller {

    // <--------------------  补充清单   ------------>
    /**
     * 判断清单编码是否存在
     * code 用户输入的编码
     */
    qdCodeExistInUnit(args) {
        let {constructId, singleId, unitId, code} = args;
        let all = [];
        all = all.concat(PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
        all = all.concat(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes());

        let res = false;
        for (let i = 0; i < all.length; ++i) {
            let item = all[i];
            if (item.kind !== "03") continue;
            let itemCode = item.bdCode ? item.bdCode : item.fxCode;
            if (!itemCode) continue;
            if (itemCode.length == 12) {
                res = itemCode.includes(code);
            } else {
                res = (itemCode === code);
            }
            if (res) {
                return res;
            }
        }

        return res;
    }

    /**
     * 根据编码模糊搜索标准清单
     * @param args
     * code
     */
    searchQdByCode(args) {
        let {code} = args;
        let bases = this.service.supplementService.vagueSearchBaseQds(code);
        return ResponseData.success(bases);
    }


    /**
     * 根据清单名称模糊搜索标准清单
     * @param args
     * code
     */
    searchQdByName(args) {
        let {name} = args;
        let bases = this.service.supplementService.vagueSearchBaseQdByName(name);
        return ResponseData.success(bases);
    }

    /**
     * 判断清单编码是否存在
     * code 用户输入的编码
     */
    isQdCodeExist(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isCodeExist(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 判断是否是标准清单
     * code 用户输入的编码
     */
    isStandQd(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isStandQdCode(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 通过标准编码插入清单
     * pointLine 选中行信息 全量给回来，记得反序列化
     * code      用户输入的编码
     */
    updateQdByCode(args) {
        let {constructId, singleId, unitId, pointLine, code, isSortQdCode, unit} = args;
        let res = this.service.supplementService.supplementQdByCode(constructId, singleId, unitId,
            pointLine, code, isSortQdCode, unit);

        return ResponseData.success(res);
    }

    /**
     * 通过界面信息插入清单（可能是标准也可能不是标准）
     * pointLine 选中行信息 全量给回来，记得反序列化
     * pageInfo {
     *     code 编码
     *     name 名称
     *     unit 单位
     *     quantityExpression  工程量表达式
     *     type 类型      1 分部分项 , 2 措施项目
     * }
     */
    async updateQdByPage(args) {
        let {constructId, singleId, unitId, pointLine, pageInfo, type} = args;
        let res = await this.service.supplementService.supplementQdByPage(constructId, singleId, unitId,
            pointLine, pageInfo, type == 1 ? "fbfx" : "csxm");

        return ResponseData.success(true);
    }

    /**
     * 新增 然后补充清单
     * @param type 类型      1 分部分项 , 2 措施项目
     * @param rootLineId
     */
    async updateQdByEmpty(args,redo="补充清单") {
        let {constructId, singleId, unitId, pointLine, rootLineId, pageInfo, type} = args;
        let newLine = {kind: "03"};
        let insertRes;
        if (type === 1) {
            insertRes = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, newLine, rootLineId);
        } else {
            insertRes = await this.service.stepItemCostService.save(constructId, singleId, unitId, pointLine, newLine);
        }

        await this.updateQdByPage({
            "constructId": constructId,
            "singleId": singleId,
            "unitId": unitId,
            "pointLine": insertRes.data,
            "pageInfo": pageInfo,
            type
        });

        return ResponseData.success(insertRes);
    }



    /**
     * 处理编码中含有- 的编码处理逻辑
     *
     * @param args
     * code
     */
    changeQdByCode(args) {
        let {code,constructId, singleId, unitId} = args;
        let newCode = this.service.supplementService.changeQdByCode(code,constructId, singleId, unitId);
        return ResponseData.success(newCode);
    }


    // <--------------------  补充定额   ------------>

    /**
     * 判断是否是本定额册下的标准定额
     */
    isMainStandQd(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isMainStandDeCode(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 判断是否是标准定额
     */
    isStandDe(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isStandDeCode(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 通过标准编码插入定额
     * pointLine 选中行信息 全量给回来，记得反序列化
     * code      用户输入的编码
     */
    async updateDeByCode(args) {
        let {constructId, singleId, unitId, pointLine, type, code} = args;
        //true 标记前端 传来修改
        let res = await this.service.supplementService.supplementDeByCode(constructId, singleId, unitId,
            type, pointLine, code, true);
        await this.service.management.sycnTrigger("unitDeChange");

        await this.service.management.trigger("itemChange");

        return ResponseData.success(true);
    }

    /**
     * 通过界面信息插入清单（可能是标准也可能不是标准）
     * pointLine 选中行信息 全量给回来，记得反序列化
     * pageInfo {
     *     code 编码
     *     name 名称
     *     unit 单位
     *     quantityExpression  工程量表达式
     *     classifyLevel1
     *     classifyLevel2
     *     classifyLevel3
     *     classifyLevel4  下拉框会有
     *     rfee 人工费
     *     cfee 材料费
     *     jfee 机械费
     *     zcfee 主材费
     *     sbfee 设备费
     * }
     */
    async updateDeByPage(args) {
        let {constructId, singleId, unitId, type, pointLine, pageInfo} = args;
        //处理表达式小写的qdl
        pageInfo.quantityExpression=pageInfo.quantityExpression.toUpperCase();
        let res = await this.service.supplementService.supplementDeByPage(constructId, singleId, unitId,
            type, pointLine, pageInfo);
        /* await this.service.management.sycnTrigger("unitDeChange");
         await this.service.unitCostCodePriceService.countCostCodePrice({
             constructId: constructId,
             singleId: singleId,
             unitId: unitId,
         });*/
        return ResponseData.success(true);
    }

    /**
     * updateDeByEmpty 的基础上加一个 rootLineId
     * @param args
     * @return {ResponseData}
     */
    async updateDeByEmpty(args,redo="补充定额") {
        let {constructId, singleId, unitId, pointLine, pageInfo, rootLineId, type} = args;
        let newLine = {kind: "04"};
        let insertRes;
        if (type === 1) {
            insertRes = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, newLine, rootLineId);
        } else {
            insertRes = await this.service.stepItemCostService.save(constructId, singleId, unitId, pointLine, newLine);
        }

        await this.updateDeByPage({
            "constructId": constructId,
            "singleId": singleId,
            "unitId": unitId,
            "type": type,
            "pointLine": insertRes.data,
            "pageInfo": pageInfo
        })

        return ResponseData.success(insertRes);
    }

    // < ---------------------      人材机补充 --------------------------- >

    /**
     * 获取类型列表
     */
    getTypeList() {
        return [
            {"kind": 1, "type": "人工费"},
            {"kind": 2, "type": "材料费"},
            {"kind": 3, "type": "机械费"},
            {"kind": 4, "type": "设备费"},
            {"kind": 5, "type": "主材费"},
            {"kind": 6, "type": "商砼"},
            {"kind": 7, "type": "砼"},
            {"kind": 8, "type": "浆"},
            {"kind": 9, "type": "商浆"},
            {"kind": 10, "type": "配比"}
        ]
    }

    /**
     * 是否是标准人材机
     * code 人材机编码
     */
    isStandRcj(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isStandRcj(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 是否是主定额库的标准编码
     */
    isMainLibStandRcj(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.isMainLibStandRcj(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }


    /**
     * 获取缓存中的补充人材机数据   返回null标识缓存总没有
     */
    getCacheSRcj(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.getCacheSRcj(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }


    /**
     * 人材机是否已经 在单位下 存在
     * code 人材机编码
     */
    isRcjExist(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.supplementService.rcjCodeExist(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 从界面补充人材机
     * pointLine  选中行 可能是操作区的行数据 也可能是明细区域的行数据 反正再在哪里给哪里
     * pageInfo{
     *     name 名称
     *     code 编码
     *     kind 数字那个 1 2 3 。。。。。。  getTypeList 返回的
     *     type 文字那个 人工费 材料费  机械费 。。。。。。
     *     unit 单位
     *     specification 规格型号
     *     price 单价
     *     taxRemoval 出水系数
     * }
     */
    async spRcjByPage(args) {
        let {constructId, singleId, unitId, pointLine, pageInfo, region} = args;
        await this.service.supplementService.supplementRcjByPage(constructId, singleId, unitId, pointLine, pageInfo, region);

        return ResponseData.success(true);
    }

    async spRcjByEmpty(args,redo="根据清单/定额名称生成主材或设备") {
        let {constructId, singleId, unitId, pointLine, pageInfo, type, region, rootLineId, deItemId} = args;
        if (!pointLine.hasOwnProperty("name") || ObjectUtils.isEmpty(pointLine.name)){
            return ResponseData.success();
        }
        pageInfo.quantityExpression="QDL";
        pageInfo.quantity=pointLine.quantity;
       let insertStrategy =  new InsertStrategy({constructId,singleId,unitId,pageType: type == 1 ? "fbfx" : "csxm"});
      let line = await insertStrategy.supplement({pointLine,newLine:{kind: "04",rcjFlag:1},pageInfo});
        await this.service.supplementService.bcrcjtscl(constructId, singleId, unitId,pageInfo,type);
        return ResponseData.success({data:line.sequenceNbr});
    }


    /**
     * 补充人材机明细
     * @return {ResponseData}
     */
    async supplementRcjDetail(args){
        let {constructId, singleId, unitId, pointLine, pageInfo, type, region, rootLineId, deItemId} = args;
        if (pointLine.kind == "04" && (!pointLine.hasOwnProperty("name") || ObjectUtils.isEmpty(pointLine.name))){
            return ResponseData.success();
        }
        let data = null;
        if (type == 1){
            data = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        }else {
            data = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        let de = data.find(k =>k.sequenceNbr == deItemId);


        let insertRcjStrategy = new InsertRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        let newRcj = await insertRcjStrategy.execute({de:de,pointLine:pointLine,rcj:pageInfo});
        await this.service.supplementService.bcrcjtscl(constructId, singleId, unitId,pageInfo,type);

        //处理人材机换算信息
        this.service.rcjProcess.updateRcjSyncDeConversionInfo(constructId, singleId, unitId,de.sequenceNbr,newRcj,"add",null,null,1);
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        //重新计算费用汇总
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        return ResponseData.success(newRcj);

    }

    /**
     * spRcjByPage 基础上 加一个参数 type
     * type 类型      1 分部分项 , 2 措施项目
     * @param args
     * @return {ResponseData}
     */
    async spRcjByEmptyOld(args) {
        let {constructId, singleId, unitId, pointLine, pageInfo, type, region, rootLineId, deItemId} = args;

        // let data = null;
        // if (type == 1){
        //     data = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        // }else {
        //     data = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        // }
        // let de = data.find(k =>k.sequenceNbr == deItemId);
        // args.de = de;



        let insertRes;
        if (region == 0) {  // 操作区
            if (type === 1) {
                insertRes = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, {
                    kind: "04",
                    rcjFlag: 1
                }, rootLineId);
            } else {
                insertRes = await this.service.stepItemCostService.save(constructId, singleId, unitId, pointLine, {
                    kind: "04",
                    rcjFlag: 1
                });
            }
            if (!PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs) {
                PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs = [];
            }
            // 操作区新增空人材机后，还要挂一个虚拟人材机
            PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.push({
                sequenceNbr: Snowflake.nextId(),
                deId: insertRes.data.sequenceNbr
            });
        } else {        // 明细区
            let deLine = this.service.rcjProcess.findDeByDeId(constructId, singleId, unitId, deItemId);
            if (pointLine && pointLine.rcjId && pointLine.parentId && pointLine.rcjId !== "") {
                insertRes = {
                    data: {
                        sequenceNbr: Snowflake.nextId(),
                        rcjId: pointLine.rcjId,
                        parentId: pointLine.rcjId
                    }
                };
                let witchOut = PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjDetailList.filter(f => f.rcjId !== pointLine.rcjId);
                let witchIn = PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjDetailList.filter(f => f.rcjId === pointLine.rcjId);
                let sx = [];
                for (let i = 0; i < witchIn.length; ++i) {
                    if (witchIn[i].sequenceNbr === pointLine.sequenceNbr) {
                        sx.push(insertRes.data);
                    }
                    sx.push(witchIn[i]);
                }
                PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjDetailList = witchOut.concat(sx);
            } else {
                if (deLine.rcjFlag && deLine.rcjFlag === 1) {
                    let prcj = PricingFileFindUtils.getRcjList(constructId, singleId, unitId).filter(f => f.deId === deLine.sequenceNbr)[0];
                    insertRes = {
                        data: {
                            sequenceNbr: Snowflake.nextId(),
                            rcjId: prcj.sequenceNbr,
                            parentId: prcj.sequenceNbr
                        }
                    };
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjDetailList.push(insertRes.data);
                } else {
                    insertRes = {data: {sequenceNbr: Snowflake.nextId(), deId: deItemId}};
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.push(insertRes.data);
                }
            }
        }

        await this.service.supplementService.supplementRcjByPage(constructId, singleId, unitId, insertRes.data, pageInfo, region);
        return ResponseData.success(insertRes);
    }

    /**
     * 根据编码补充人材机
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine  选中行 可能是操作区的行数据 也可能是明细区域的行数据 反正再在哪里给哪里
     * @param code      编码
     * @param region  region 0 操作区  1 明细区
     */
    spByCode(args) {
        let {constructId, singleId, unitId, pointLine, code, region} = args;
        this.service.supplementService.supplementRcjByCode(constructId, singleId, unitId, pointLine, code, region);

        return ResponseData.success(true);
    }


    //<------------------------------------------------>

    /**
     * 补充清单定额人材机 默认编码
     */
    async defaultCodeColl(arg) {
        const result = await this.service.supplementService.defaultCode(arg);
        return ResponseData.success(result);
    }


}

SupplementController.toString = () => '[class SupplementController]';
module.exports = SupplementController;
