<template>
  <div class="operate-list">
    <div
      class="list"
      @click="openExternal('https://www.yunsuanfang.com/feedback')"
    >
      <icon-font class="icon" type="icon-yijianfankui" />意见反馈
    </div>
    <div class="list" @click="visible = true">
      <icon-font class="icon" type="icon-lianxiwomen" />联系我们
    </div>
    <div class="list">
      <RemoteAssistance text="申请远程协助"></RemoteAssistance>
    </div>
    <common-modal
      className="cs-operate-dialog-comm dialog-comm"
      width="620"
      :title="` `"
      v-model:modelValue="visible"
      @cancel="cancel"
      @close="cancel"
    >
      <template #default>
        <div class="dialog-body">
          <div class="list">
            <img src="@/assets/img/newCsProject/contact-email.png" alt="" />
            <div class="info">
              <div class="name">邮箱</div>
              <div class="text"><EMAIL></div>
            </div>
          </div>
          <div class="list">
            <img src="@/assets/img/newCsProject/contact-tel.png" alt="" />
            <div class="info">
              <div class="name">联系电话</div>
              <div class="text">************</div>
            </div>
          </div>
        </div>
      </template>
    </common-modal>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import RemoteAssistance from '@/components/RemoteAssistance/index.vue';
const { shell } = require('electron');
let visible = ref(false);
const cancel = () => {
  visible.value = false;
};
const openExternal = link => {
  shell.openExternal(link);
};
</script>
<style lang="scss">
.cs-operate-dialog-comm {
  .vxe-modal--header {
    height: 129px;
    background: url(@/assets/img/newCsProject/contact-head-bg.png) no-repeat 50%
      50%;
    &::after {
      display: none;
    }
  }
  .vxe-modal--close-btn {
    top: 23px;
    right: 19px;
    transform: none;
    font-size: 18px;
  }
  .vxe-modal--content {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
.dialog-body {
  padding: 65px 0;
  background: url(@/assets/img/newCsProject/contact-bg.png) no-repeat 50% 50%;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  .list {
    display: flex;
    align-items: center;
    &:last-child {
      margin-left: 80px;
    }
    img {
      width: 40px;
      height: 40px;
      margin-right: 20px;
    }
    .name,
    .text {
      font-size: 14px;
      color: #2a2a2a;
    }
    .text {
      color: #606060;
    }
  }
}
.operate-list {
  display: flex;
  width: 100%;
  height: 30px;
  background: #326df4;
  border-radius: 1px 1px 1px 1px;
  opacity: 0.89;
  .list {
    position: relative;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #ffffff;
    &:hover {
      background: #1953d8;
    }

    .icon {
      margin-right: 7px;
    }
    &:last-child::after {
      display: none;
    }
    &::after {
      display: block;
      content: '';
      position: absolute;
      right: 0px;
      top: 9px;
      width: 1px;
      height: 12px;
      background-color: #ffffff;
      opacity: 0.31;
    }
  }
}
</style>
