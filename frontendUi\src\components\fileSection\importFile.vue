<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-29 09:54:24
-->
<template>
  <common-modal
    @close="cancel()"
    className="dialog-comm tree-dialog"
    :showClose="!loading"
    :loading="loading"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="导入项目"
  >
    <div class="tree-content-wrap">
      <div class="tree-list">
        <div class="dialog-content">
          <div class="title">当前项目</div>
          <div class="list" v-if="currentTreeData">
            <a-tree
              ref="currentTreeRef"
              defaultExpandAll
              show-line
              showIcon
              :tree-data="currentTreeData"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              v-model:expandedKeys="expandedKeys"
              v-model:selectedKeys="useSelectedKeys"
              @select="currentSelect"
            >
              <template #switcherIcon="{ switcherCls }">
                <down-outlined :class="switcherCls" />
              </template>
              <template #title="{ levelType, id, name, whetherNew, children }">
                <icon-font
                  class="mIcon del-icon"
                  :class="`delicon${levelType}`"
                  type="icon-shanchu1"
                  v-show="whetherNew"
                  @click.stop="delImportItem(id)"
                />
                <span class="check-labels">{{ name }}</span>
              </template>
            </a-tree>
          </div>
        </div>

        <div class="handle-btn-wrap">
          <a-button type="primary" :disabled="disableImBtn" @click="move">
            <template #icon><left-outlined /></template>
            导入
          </a-button>
          <a-button style="margin-top: 30px" @click="onReset"> 重置 </a-button>
        </div>

        <div class="dialog-content">
          <div class="title">
            <span>导入项目</span>
            <!-- <div class="checkhandle">
              <a-radio-group v-model:value="checkStatus" @change="changeStatus">
                <a-radio value="all">全部</a-radio>
                <a-radio value="part">取消全选</a-radio>
              </a-radio-group>
            </div> -->
          </div>
          <div class="list" v-if="importTreeData">
            <a-tree
              v-model:checkedKeys="importCheckedKeys"
              defaultExpandAll
              checkable
              multiple
              show-line
              :tree-data="importTreeData"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
            >
              <template #switcherIcon="{ switcherCls, children }">
                <down-outlined :class="switcherCls" />
              </template>
            </a-tree>
          </div>
        </div>
      </div>
      <!-- <div class="group-list">
        <a-radio-group v-model:value="dataStatus">
          <a-radio value="all">以当前项目费率为准</a-radio>
          <a-radio value="part">以导入项目标准率为准</a-radio>
        </a-radio-group>
      </div> -->
      <div class="footer-btn-list">
        <a-button @click="cancel()">取消</a-button>
        <a-button
          type="primary"
          @click="handleOk"
          :title="handleTip()"
          :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  nextTick,
  toRaw,
  defineExpose,
  watchEffect,
  shallowRef,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import { constructLevelTreeStructureList } from '@/api/csProject';
import { DownOutlined, LeftOutlined } from '@ant-design/icons-vue';
import xeUtils from 'xe-utils';
import Snowflake from '@/plugins/Snowflake.js';

const SnowflakePlugins = new Snowflake();
const emits = defineEmits(['closeDialog']);
const route = useRoute();
const submitLoading = ref(false);
const dialogVisible = ref(false);
const currentTreeData = ref(null);
const importTreeData = ref(null);
const copyImportTreeData = ref(null);

const importCheckedKeys = shallowRef([]);
const checkStatus = ref(null); //全选，取消全选
const hasSingle = ref(false); //当前项目，是否有单项结构
const isEmptyProject = ref(false); //是否是空项目

const loading = ref(false);
const expandedKeys = shallowRef([]);
const useSelectedKeys = ref([]);
const currentTreeRef = ref(null);
const currentSelected = reactive({
  initData: [],
  keys: '',
  node: null,
});
const importSelected = reactive({
  historyKeys: [],
  handleKeys: [], //点击了全选
  filterSingHistoryKeys: [], //过滤了单项的，
});
const dataStatus = ref(null);
const importYsf = ref(null);

const props = defineProps(['importList']);

// 处理确定的提示语
const handleTip = () => {
  let msg = '';
  if (!importSelected.historyKeys.length) {
    msg = '请选择导入项目!';
  }

  // else if(!dataStatus.value){
  //   msg="请选择费率标准!"
  // }
  return msg;
};

// 左侧选中数据
const currentSelect = (
  selectedKeys,
  { selected, selectedNodes, node, event }
) => {
  // !node.whetherNew &&
  if ([1, 2, 3].includes(node.levelType) && selected) {
    currentSelected.keys = selectedKeys[0];
    currentSelected.node = node.dataRef;
    useSelectedKeys.value = [currentSelected.keys];
  }

  if (!selected) {
    useSelectedKeys.value = [currentSelected.keys];
  }
};

// 找到所有的下级id,
const handleIds = (node, ids = []) => {
  ids.push(node.id);
  if (node.children && node.children?.length) {
    node.children.forEach(child => handleIds(child, ids));
  }
  return ids;
};

/**
 * 查找节点
 * first 第一个还是最后一个
 */
const findLastLeafNode = (node, isFirst = false) => {
  if (node?.children && node.children.length > 0) {
    return findLastLeafNode(
      node.children[isFirst ? 0 : node.children.length - 1],
      isFirst
    );
  } else {
    return node;
  }
};

//查找当前节点的兄弟元素
const findSiblingNodes = (parentId, tree) => {
  let siblings = [];
  // 遍历树结构
  for (let node of tree) {
    if ('parentId' in node && node.parentId === parentId) {
      siblings.push({ ...toRaw(node) });
    }

    if ('children' in node && Array.isArray(node.children)) {
      const subtreeSiblings = findSiblingNodes(parentId, node.children);
      siblings = [...siblings, ...subtreeSiblings];
    }
  }

  return siblings;
};

// 左右联动，处理导入按钮
const disableImBtn = shallowRef(false);

// 前置校验
const beforeCheck = () => {
  let btnStatus = false;
  // 查找要复制过去的元素
  const elNodes = importCheckedKeys.value
    .filter(i => !importSelected.historyKeys.includes(i))
    .map(i => toRaw(findElement(i, importTreeData.value)));

  const tree = xeUtils.toArrayTree(elNodes, { key: 'id' });
  console.log('🚀左右联动，处理导入按钮当前选中的:', importCheckedKeys.value);
  console.log('左右联动，处理导入按钮历史数据', importSelected.historyKeys);
  console.log('🚀左右联动，处理导入按钮复制的数据:', tree);
  console.log('🚀🚀左右联动，处理导入按钮复制的数据elNodes:', elNodes);

  const hasUseUnit = elNodes.some(i => i.levelType == 3);
  const hasUseSingle = elNodes.some(i => i.levelType == 2);

  if (!currentSelected.node || !importCheckedKeys.value?.length) {
    btnStatus = true;
  }

  if (currentSelected.node && currentSelected.node.levelType == 1) {
    // 选中工程专业
  }

  if (currentSelected.node && currentSelected.node.levelType == 2) {
    // 选中单项
    if (!hasUseUnit) {
      btnStatus = true;
    }
  }

  if (currentSelected.node && currentSelected.node.levelType == 3) {
    // 选中单位
    if (!hasUseUnit) {
      btnStatus = true;
    }
  }

  if (!hasSingle.value && !hasUseUnit) {
    // 空单项，并且没选择单位或者选择了单项
    btnStatus = true;
  }

  disableImBtn.value = btnStatus;
};

watch(
  () => [importCheckedKeys.value, currentSelected.keys],
  () => {
    // 左右实时联动
    beforeCheck();
  },
  {
    immediate: true,
  }
);

// 修改导入项目选择
const changeStatus = async () => {
  importSelected.handleKeys = [];
  await flattenTree(importTreeData.value, true);
  importCheckedKeys.value =
    checkStatus.value === 'all'
      ? [...importSelected.handleKeys, ...importSelected.historyKeys]
      : importSelected.historyKeys;
};

// 点击导入
const move = async () => {
  if (!currentTreeData.value.length) {
    message.error('当前项目错误！');
    return;
  }

  // 查找要复制过去的元素
  let elNodes = importCheckedKeys.value
    .filter(i => !importSelected.historyKeys.includes(i))
    .map(i => toRaw(findElement(i, importTreeData.value)));

  // 判断单项下面全是单位工程，只插入单位工程
  let isAllUnit = false;
  if (
    currentTreeData.value &&
    currentTreeData.value.length &&
    currentTreeData.value[0].children
  ) {
    isAllUnit = currentTreeData.value[0].children.every(i => i.levelType == 3);

    if (isAllUnit) {
      elNodes = elNodes.filter(i => i.levelType == 3);
    }
  }
  const tree = xeUtils.toArrayTree(elNodes, { key: 'id' });

  const handleData = handleAppendData(tree, elNodes);

  recursionTree(handleData, 'addNew');

  // 插入数据
  currentTreeData.value = handleData;

  //  导入项目，禁用已经导入的
  disableItem();

  disableImBtn.value = true;
};

const handleAppendData = (tree, elNodes) => {
  let appendId = currentSelected.keys || currentTreeData.value[0].id;
  const leaveType = currentSelected.node.levelType;

  let TreeData = [];
  const leftTreeData = JSON.parse(JSON.stringify(currentTreeData.value));

  // 所有的单位层级过滤
  let allDwList = [];
  if ([2, 3].includes(leaveType)) {
    allDwList = findLeavesWithType(tree, 3);
  }

  let filterSingHistoryKeys = [];
  switch (leaveType) {
    case 1:
      // 选择的是工程项目
      let unitList = []; //查找一级是单位的
      let singList = [];

      // 同时选中了一个完成层级，和其他单位，区分处理
      tree.forEach((node, index) => {
        if (node.levelType === 3) {
          unitList.push({ ...node, num: 0 });
        } else {
          singList.push({ ...node, num: 0 });
        }
      });

      if (isEmptyProject.value) {
        // 空工程
        unitList = findLeavesWithType(tree, 3);
      }

      if (unitList.length) {
        // 查找最后一个元素
        const lastNode = findLastLeafNode(leftTreeData[0]);
        //查找所有的兄弟元素，为了后续处理名称
        const siblingsNodes = findSiblingNodes(lastNode.parentId, leftTreeData);

        if (siblingsNodes.length) {
          siblingsNodes.forEach((v, index) => {
            unitList.forEach(j => {
              j.oldParentId = j.parentId;
              j.parentId = [1, 2].includes(lastNode.levelType)
                ? lastNode.id
                : lastNode.parentId;
            });
          });
        } else {
          // 空的单项
          unitList.forEach(j => {
            j.oldParentId = j.parentId;
            j.parentId = [1, 2].includes(lastNode.levelType)
              ? lastNode.id
              : lastNode.parentId;
          });
        }

        let insertId = lastNode.parentId;
        let insertData = [];
        if ([1, 2].includes(lastNode.levelType)) {
          // 最后面的是空单项
          insertId = lastNode.id;
          insertData = handleName([], [...unitList]);
        } else {
          insertData = handleName([...siblingsNodes], [...unitList]);
        }

        console.log('🚀 选择了工程项目:', insertId, insertData);

        replaceChildrenInTree(leftTreeData, insertId, insertData);
        filterSingHistoryKeys = unitList.map(v => v.id);
      }

      if (singList.length && !isEmptyProject.value) {
        //查找所有的兄弟元素，为了后续处理名称
        const siblingsNodes = findSiblingNodes(appendId, leftTreeData);
        // singList = xeUtils.toArrayTree(singList)
        siblingsNodes.forEach((v, index) => {
          singList.forEach(j => {
            j.oldParentId = j.parentId;
            j.parentId = appendId;
          });
        });

        singList = handleName([...siblingsNodes], [...singList], true);
        appendNodeInTree(appendId, leftTreeData, singList);
        const ids = collectIdsFromMultipleTrees([singList]);
        filterSingHistoryKeys = [...filterSingHistoryKeys, ...ids];
      }
      break;
    case 2:
      // 选择单项
      console.log('🚀选择单项', currentSelected.node);
      const lastNode = findLastLeafNode(currentSelected.node, true);
      let siblingsNodes = [];
      let insertId = lastNode.parentId;
      if ([2].includes(lastNode.levelType)) {
        // 相当于空单项
        insertId = lastNode.id;
        allDwList.forEach(j => {
          j.oldParentId = j.parentId;
          j.parentId = lastNode.id;
        });

        // allDwList = handleName(allDwList)
      } else {
        siblingsNodes = findSiblingNodes(lastNode.parentId, leftTreeData);
        siblingsNodes.forEach((v, index) => {
          allDwList.forEach(j => {
            j.oldParentId = j.parentId;
            j.parentId = lastNode.parentId;
          });
        });
      }

      const test = handleName([...siblingsNodes], [...allDwList]);

      replaceChildrenInTree(leftTreeData, insertId, test);

      if ([2].includes(lastNode.levelType)) {
        // 解决空对象，一直覆盖问题
        currentSelected.node = {
          ...currentSelected.node,
          children: [
            ...allDwList.map(v => {
              v.whetherNew = true;
              return v;
            }),
          ],
        };
      }

      filterSingHistoryKeys = [...allDwList].map(v => v.id);

      break;
    case 3:
      // 选择单位
      let unitSiblingsNodes = findSiblingNodes(
        currentSelected.node?.parentId,
        leftTreeData
      );
      unitSiblingsNodes.forEach((v, index) => {
        allDwList.forEach(j => {
          j.oldParentId = j.parentId;
          j.parentId = currentSelected.node?.parentId;
        });
      });

      let insertionIndex = unitSiblingsNodes.findIndex(i => {
        return i.id == appendId;
      });

      allDwList = handleName([...unitSiblingsNodes], [...allDwList], true);

      unitSiblingsNodes.splice(insertionIndex + 1, 0, ...allDwList);

      replaceChildrenInTree(leftTreeData, currentSelected.node.parentId, [
        ...unitSiblingsNodes,
      ]);
      filterSingHistoryKeys = [...allDwList].map(v => v.id);
      break;
    default:
      break;
  }

  importSelected.filterSingHistoryKeys = [
    ...importSelected.historyKeys,
    ...filterSingHistoryKeys,
  ];
  // 打开所有的节点
  const expandedKeyList = handleExpandedKeys(tree);
  nextTick(() => {
    expandedKeys.value = [...expandedKeys.value, ...expandedKeyList];
  });

  return leftTreeData;
};

/**
 *
 */
const handleName = (initList, currentList, isAlone = false) => {
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }

  function setName(item) {
    let NAME = '';
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }

    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (
        !nameList.includes(lastName.trim()) &&
        nameList.includes(currentName.trim())
      ) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }

    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }

    return NAME;
  }

  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();

  initList.forEach(item => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });

  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);

    const newName =
      count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;

    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }

    namesMap.set(item.checkName, count);
    acc.push(newItem);

    return acc;
  }, []);

  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }

  if (isAlone) {
    // 分开数据
    return [...renamedItems];
  } else {
    return [...initList, ...renamedItems];
  }
};

/**
 * 处理当前项目的打开节点
 * @param {*} tree
 * @param {*}
 */
const handleExpandedKeys = (nodes, idsArray = []) => {
  for (const node of nodes) {
    if ([1, 2].includes(node.levelType)) {
      idsArray.push(node.id);
    }
    if (Array.isArray(node.children)) {
      handleExpandedKeys(node.children, idsArray);
    }
  }

  return idsArray;
};

/**
 * 查找所有levelType 等于 type 的节点
 * @param {*} treeData
 * @param {*} type
 */
const findLeavesWithType = (treeData, type) => {
  const leaves = [];

  function traverse(node) {
    if (node.levelType === type) {
      leaves.push({ ...toRaw(node) });
    }

    if (Array.isArray(node.children)) {
      node.children.forEach(traverse);
    }
  }

  treeData.forEach(traverse);

  return leaves;
};

const delListId = ref([]); // 左侧点击删除的数据
const delImportItem = async id => {
  // 左侧的删除数据
  let currentChildIds = findCurrentChildIds(
    [...toRaw(currentTreeData.value)],
    id
  );
  delListId.value = [...currentChildIds, id];
  removeNodeInTree(currentTreeData.value, id);

  const useList = toRaw(importSelected.historyKeys).filter(i => {
    return !delListId.value.includes(i);
  });
  importSelected.historyKeys = useList;

  importCheckedKeys.value = useList;

  importSelected.filterSingHistoryKeys = useList;

  // 解除禁用操作
  recursionTree(importTreeData.value, 'delDisable');

  if (id == currentSelected.keys) {
    // 检测id是不是删除的自己，如果是自己，直接选中到工程
    nextTick(() => {
      const replaceData = currentTreeData.value[0];
      useSelectedKeys.value = [replaceData.id];
      currentSelected.keys = replaceData.id;
      currentSelected.node = { ...replaceData };
    });
  }
};

// 导入的数据，将左侧数据禁用
const disableItem = async () => {
  // 存储历史上一步操作的数据
  importSelected.historyKeys = xeUtils.clone(
    importSelected.filterSingHistoryKeys,
    true
  );

  recursionTree(importTreeData.value, 'addDisable');
};

// 在树中查找匹配的元素
const findElement = (key, nodes) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.id === key) {
      const list = xeUtils.clone(node, true);
      delete list.children;
      return list;
    }
    if (node.children) {
      const result = findElement(key, node.children);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

// 循环返回id值列表
// 定义一个函数用于扁平化单个树并收集id
const findTreeIdList = (tree, result = []) => {
  tree.forEach(node => {
    result.push(node.id);

    if (Array.isArray(node.children)) {
      findTreeIdList(node.children, result);
    }
  });

  return result;
};

// 函数用于处理多个树数组
const collectIdsFromMultipleTrees = trees => {
  let allIds = [];
  trees.forEach(tree => {
    allIds = [...allIds, ...findTreeIdList(tree)];
  });
  return allIds;
};

// 扁平化树结构
const flattenTree = (tree, isLog = false, parent = null, result = []) => {
  for (const node of tree) {
    const { children, ...data } = node;
    result.push({ ...data });

    if (!node.disabled && isLog && !node.initDisable) {
      importSelected.handleKeys.push(node.id);
    }

    if (children && children.length > 0) {
      flattenTree(children, isLog, node.parentId, result);
    }
  }
  return result;
};

// 树结构插入数据
const appendNodeInTree = (id, tree, obj) => {
  tree.forEach(ele => {
    if (ele.id === id) {
      ele.children = ele.children ? [...ele.children, ...obj] : obj;
    } else if (ele.children) {
      appendNodeInTree(id, ele.children, obj);
    }
  });
  return tree;
};

// 树结构替换子数据
const replaceChildrenInTree = (treeData, parentId, newChildren) => {
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    if (node.id === parentId) {
      node.children = newChildren;
      return;
    }
    if (Array.isArray(node.children)) {
      replaceChildrenInTree(node.children, parentId, newChildren);
    }
  }
};

// 树结构删除数据
const findCurrentChildIds = (tree, targetId, ids = []) => {
  for (let node of tree) {
    if (node.id === targetId) {
      if (node.children) {
        collectChildIds(node.children, ids);
      }
      return ids;
    } else if (node.children) {
      findCurrentChildIds(node.children, targetId, ids);
    }
  }

  return ids;
};

// 辅助递归函数用于收集子节点id
const collectChildIds = (children, ids) => {
  children.forEach(child => {
    ids.push(child.id);
    if (child.children) {
      collectChildIds(child.children, ids);
    }
  });
};

// 树结构删除数据
const removeNodeInTree = (treeList, id) => {
  if (!treeList || !treeList.length) {
    return;
  }
  for (let i = 0; i < treeList.length; i++) {
    if (treeList[i].id === id) {
      treeList.splice(i, 1);
      break;
    }
    removeNodeInTree(treeList[i].children, id);
  }
};

// 循环，处理树
/*
**type addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
       addNew      添加new
*/
const recursionTree = (treeList, type) => {
  treeList.forEach(node => {
    setValue(node, type);
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        setValue(child, type);
        recursionTree([{ ...child }], type);
      });
    }
  });
};

//设置值
const setValue = (data, type) => {
  if (!data.initDisable) {
    // 过滤最原始就不能编辑的值
    switch (type) {
      case 'addDisable':
        data.disableCheckbox = importSelected.filterSingHistoryKeys.includes(
          data.id
        );
        data.disabled = importSelected.filterSingHistoryKeys.includes(data.id);
        break;
      case 'addNew':
        data.whetherNew = importSelected.filterSingHistoryKeys.includes(
          data.id
        );

        break;
      case 'delDisable':
        if (delListId.value.includes(data.id)) {
          data.disableCheckbox = false;
          data.disabled = false;
        }
        break;
      default:
        break;
    }
  }
};

const findValueInTree = data => {
  if (!data) return;
  // 遍历树节点
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    if ([1, 2].includes(node.levelType)) {
      expandedKeys.value.push(node.id);
    }
    if (node.children) {
      findValueInTree(node.children);
    }
  }
};

/**
 * 刷新id
 * @param tree
 * @param parentId
 * handleType
 */
const idReset = (tree, parentId = null, handleType = 'current') => {
  for (let item of tree) {
    item.idBak = item.id;
    item.id = SnowflakePlugins.nextId();
    item.parentId = parentId;
    item.num = 0;
    item.name = item.name.trim();
    item.checkName = item.name.trim();
    if (handleType === 'import') {
      // 处理导入项目
      const DisableStatus = [1].includes(item.levelType);
      item.whetherNew = false;
      item.disableCheckbox = DisableStatus;
      item.disabled = DisableStatus;
      if (DisableStatus) {
        // 设置最原始的值，默认就是不能编辑
        item.initDisable = true;
      }
    }

    if (item.children && item.children.length > 0) {
      idReset(item.children, item.id, handleType);
    }
  }
};
/**
 * 恢复id
 * @param tree
 * @param parentId
 */

const idRecovery = (tree, parentId = null) => {
  for (let item of tree) {
    item.id = item.idBak;
    item.parentId = parentId;

    delete item.idBak;
    if (item.children && item.children.length > 0) {
      idRecovery(item.children, item.id);
    }
  }
};

// 当前项目
const getTreeList = (isReset = false) => {
  expandedKeys.value = [];
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      if (res.code === 200) {
        let data = xeUtils.toArrayTree(
          Array.isArray(res.result) ? res.result : [res.result],
          {
            children: 'children',
            id: 'id',
            pid: 'parentId',
            sort: 'sort',
          }
        );

        idReset(data);
        currentTreeData.value = data;
        isEmptyProject.value = data[0]?.children?.length === 0;
        currentSelected.initData = data;
        useSelectedKeys.value = [data[0].id];
        currentSelected.keys = data[0].id;
        currentSelected.node = { ...data[0] };

        findValueInTree(data);
        // 获取导入项目
        getImportTreeList(isReset);
      }
    }
  );
};

const renameDuplicates = items => {
  const namesCountMap = new Map();

  return items.map((item, index) => {
    let newName = item.name;
    let newNum = item.num;

    // 检查当前名称是否重复，如果重复则更新名称
    if (namesCountMap.has(item.name)) {
      const count = namesCountMap.get(item.name);
      namesCountMap.set(item.name, count + 1);
      namesCountMap.set(item.num, count + 1);
      newNum = count + 1;
      newName = `${item.name}_${count + 1}`;
    } else {
      namesCountMap.set(item.name, 0);
      namesCountMap.set(item.num, 0);
    }

    // 更新并返回新的对象
    return { ...item, name: newName, num: newNum };
  });
};

// 导入项目列表
const getImportTreeList = async (isReset = false) => {
  if (isReset) {
    importTreeData.value = JSON.parse(JSON.stringify(copyImportTreeData.value));
  } else {
    const data = xeUtils.toArrayTree(
      Array.isArray(importYsf.value) ? importYsf.value : [importYsf.value],
      {
        children: 'children',
        id: 'id',
        pid: 'parentId',
        sort: 'sort',
      }
    );

    const list = await flattenTree(toRaw(currentTreeData.value));
    // 判断当前项目有没有单项
    hasSingle.value = list.some(i => {
      return [2].includes(i.levelType);
    });

    idReset(data, null, 'import');
    importTreeData.value = data;
    copyImportTreeData.value = JSON.parse(JSON.stringify(data));
  }

  nextTick(() => {
    loading.value = false;
  });
};

// status true， 保存成功
const cancel = (status = false) => {
  dialogVisible.value = false;
  if (!status) {
    csProject.deleteImportProject(route.query.constructSequenceNbr);
  }
  emits('closeDialog', { status });
};

// 点击确定按钮
const handleOk = async () => {
  const status = await handleTip();
  if (status) {
    message.error(status);
    return;
  }
  if (submitLoading.value) return;
  submitLoading.value = true;
  let projectStructureTree = JSON.parse(JSON.stringify(currentTreeData.value));
  idRecovery(projectStructureTree);
  const postData = {
    constructId: route.query.constructSequenceNbr,
    projectStructureTree: projectStructureTree[0],
  };
  console.log('🚀 发送数据:', postData);
  csProject
    .saveImportProject(postData)
    .then(res => {
      if (res.status === 200) {
        message.success('导入成功');
        setTimeout(() => {
          location.reload();
        }, 1000);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

/**
 *
 * @param {*} ossPath 导入文件的路径
 */
const openDialog = data => {
  if (data) {
    loading.value = true;
    importYsf.value = data;
    dialogVisible.value = true;
    getTreeList();
  }
};

watchEffect(() => {
  if (props.importList) {
    openDialog(JSON.parse(JSON.stringify(props.importList)));
  }
});

const onReset = () => {
  getTreeList(true);
  importCheckedKeys.value = [];
  importSelected.historyKeys = [];
  importSelected.handleKeys = [];
  importSelected.filterSingHistoryKeys = [];
};
</script>
<style lang="scss" scoped>
.del-icon {
  font-size: 17px;
}

.mIcon {
  margin-right: 10px;
}

.tree-content-wrap {
  width: 70vw;
  height: 100%;
  max-width: 800px;
  min-width: 700px;
  display: flex;
  flex-direction: column;
}
.tree-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .handle-btn-wrap {
    flex: 1;
    display: flex;
    padding: 0 50px;
    flex-direction: column;
    justify-content: center;
  }
}

.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  height: 50vh;
  width: 43%;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 7px 13px;
    background-color: #eaeaea;
    span {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }
    .checkhandle {
      display: flex;
      align-items: center;
    }
  }
  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    ::v-deep .ant-tree {
      background-color: #fafafa;
      .ant-tree-switcher-noop {
        opacity: 0;
      }
      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 12px 0 30px;
  padding: 9px 18px;
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
}

.footer-btn-list {
  margin-top: 30px;
}
</style>
