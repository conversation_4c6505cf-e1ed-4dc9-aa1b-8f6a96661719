
const {WinManageUtils} = require("../../common/WinManageUtils");
const ConstantUtil = require("../enum/ConstantUtil");
const EE = require("../../core/ee");
const {UPCContext} = require("../unit_price_composition/core/UPCContext");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ProjectFileUtils} = require("../../common/ProjectFileUtils");



class ConSoleCommonHandler {
    constructor(ctx) {
        let {path} = ctx;
        //文件路径
        this.path = path;
        this.route = this.getRoute();
    }


    getRoute() {
        //获取文件后缀名
        let pathSuffix = this.path.match(/[^.]+$/)[0];
        let route = null;
        switch (pathSuffix) {
            //预算
            case "YSF": {
                route =ConstantUtil.YUSUAN_ROUTE
                break;
            }
            //预算 招标项目
            case "YSFZ": {
                route =ConstantUtil.YUSUAN_ROUTE
                break;
            }
            //预算 单位工程
            case "YSFD": {
                route =ConstantUtil.YUSUAN_ROUTE
                break;
            }
            //预算 工料机
            case "YSFG": {
                route =ConstantUtil.YUSUAN_ROUTE
                break;
            }
            //结算
            case "YJS": {
                route = ConstantUtil.JIESUAN_ROUTE;
                break;
            }
            //预算审核
            case "YSH": {
                route = ConstantUtil.YUSUANSHENHE_ROUTE;
                break;
            }
            //概算
            case "YGS": {
                route = ConstantUtil.GS_ROUTE;
                break;
            }
            //
            case "YSFG": {
                route =ConstantUtil.GLJ_ROUTE
                break;
            }
        }
        return route;
    }



    /**
     *文件数据处理
     */
    async fileDataHandle(obj){
        throw new Error("需要子类去实现");
    }


    /**
     * 拉起项目窗口
     */
    async pullObjWin(obj){
        return WinManageUtils.createWindow(this.path,obj.sequenceNbr,this.route);
    }

    /**
     *窗口事件
     */
    winEventInit(win,obj){
        //添加窗口关闭事件
        win.on('closed', () => {
            WinManageUtils.getAllWindowIdCache().delete(obj.sequenceNbr);
            global.constructProject[obj.sequenceNbr] = null;
        });
    }


    /**
     * 打开本地文件
     * @param params
     */
    async openLocalObj(obj) {
        obj = await this.before(obj);
        return await this.openFromMemory(obj);

        // //文件数据处理
        // obj = await this.fileDataHandle(obj);
        // //拉起项目窗口
        // let win = await this.pullObjWin(obj);
        // //窗口事件定义
        // this.winEventInit(win,obj);
        // await this.after(win,obj);
        // return win;
    }

    /**
     * 打开内存项目，用于复制后打开
     */
    async openFromMemory(obj) {
        //文件数据处理
        obj = await this.fileDataHandle(obj);
        //拉起项目窗口
        let win = await this.pullObjWin(obj);
        //窗口事件定义
        this.winEventInit(win,obj);
        await this.after(win,obj);
        return win;
    }


    async before(obj) {
        let {service} = EE.app;
        if(obj.UPCContext){
            UPCContext.load(obj.UPCContext);
        }
        //将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        service.systemService.loadProject(obj);
        return obj;
    }


    async after(win,obj) {
        //用户的打开历史记录列表数据处理
        ProjectFileUtils.writeUserHistoryListFile(obj);
        //对象的历史记录删除
        global.redoMap?.delete(obj.sequenceNbr);
    }

}

module.exports = {
    ConSoleCommonHandler
}
