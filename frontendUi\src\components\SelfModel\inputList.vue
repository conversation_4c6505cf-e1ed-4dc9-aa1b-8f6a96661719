<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-17 14:19:32
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-22 11:37:22
-->

<template>
  <div class="content">
    <div>
      <a-form
        ref="form"
        :model="inputData"
        :label-col="colStyle.labelCol"
        :wrapper-col="colStyle.wrapperCol"
        @finish="onSubmit"
        :class="
          showType.projectType == 'danwei'
            ? 'newClass'
            : showType.projectType == 'toubiao'
              ? 'toubiaoClass'
              : 'normalClass'
        ">
        <template v-if="showType.projectType != 'gongliaoji'">
          <a-form-item
            label="单位工程名称"
            name="constructName"
            v-if="showType.projectType == 'danwei'"
            :rules="[
              {
                required: true,
                message: '请输入单位工程名称!',
              },
              {
                max: 50,
                message: '单位工程名称长度不可超过50个字!',
              },
              {
                pattern: /^[^`\^。>？!！￥~!@$^&*\=+[\]{}\\|;:<>/?]*$/,
                message: '单位工程名称不可包含特殊字符!',
              },
            ]">
            <a-input v-model:value="inputData.constructName" placeholder="请输入单位项目名称" />
            <!-- <a-input v-model:value="inputData.iptList.unitProjectName" placeholder="请输入单位工程名称" /> -->
          </a-form-item>
          <a-form-item
            label="项目名称"
            v-if="showType.projectType !== 'danwei'"
            name="constructName"
            :rules="[
              {
                required: true,
                message: '请输入项目名称!',
              },
              {
                max: 50,
                message: '项目名称长度不可超过50个字!',
              },
              {
                pattern: /^[^`\^。>？!！￥~!@$^&*\=+[\]{}\\|;:<>/?]*$/,
                message: '项目名称不可包含特殊字符!',
              },
            ]">
            <a-input v-model:value="inputData.constructName" placeholder="请输入项目名称" />
          </a-form-item>
          <a-form-item
            label="项目编码"
            name="constructCode"
            :rules="[
              {
                max: 50,
                message: '项目编码长度不可超过50个字!',
              },
              {
                pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]{1,}$/,
                message: '项目编码不可包含特殊字符!',
              },
            ]">
            <a-input v-model:value="inputData.constructCode" placeholder="请输入项目编码" />
          </a-form-item>
          <a-form-item
            label="清单标准"
            name="mainfest"
            :rules="[{ required: true, message: '请选择清单标准!' }]">
            <a-select
              v-model:value="inputData.mainfest"
              :options="lists.mainfestList"
              placeholder="请选择清单标准"
              :size="colStyle.colSize"></a-select>
          </a-form-item>
          <a-form-item
            label="定额标准"
            name="ration"
            :rules="[{ required: true, message: '请选择定额标准!' }]">
            <a-select
              v-model:value="inputData.ration"
              :options="lists.rationList"
              placeholder="请选择定额标准"
              :size="colStyle.colSize"
              @change="chengeRation($event)"></a-select>
          </a-form-item>
          <a-form-item label="电子标" @click="upFile" v-if="showType.projectType == 'toubiao'">
            <a-input placeholder="请上传电子标书" :value="inputData.importName" />
          </a-form-item>
          <!--        <a-form-item-->
          <!--          label="xml厂家"-->
          <!--          name="xmlFactory"-->
          <!--          :rules="[{ required: true, message: '请选择xml厂家!' }]"-->
          <!--          v-if="inputData.fileExtension == 'xml'"-->
          <!--        >-->
          <!--          <a-select-->
          <!--            v-model:value="inputData.xmlFactory"-->
          <!--            :options="lists.xmlList"-->
          <!--            placeholder="请选择"-->
          <!--            :size="colStyle.colSize"-->
          <!--          ></a-select>-->
          <!--        </a-form-item>-->
          <a-form-item
            label="清单专业"
            v-if="showType.projectType == 'danwei'"
            name="constructMajorType"
            :rules="[{ required: true, message: '请选择清单专业!' }]">
            <a-select
              v-model:value="inputData.constructMajorType"
              :options="lists.engineerMajorList"
              placeholder="请选择清单专业"
              :size="colStyle.colSize"
              @change="typeChange"></a-select>
          </a-form-item>
          <a-form-item
            label="主定额册名称"
            v-if="inputData.constructMajorType"
            name="constructMajorType">
            <a-select
              v-model:value="inputData.libraryCode"
              :options="lists.majorTypeDropdownList"
              placeholder="请选择主定额册"
              :size="colStyle.colSize"
              :fieldNames="{
                label: 'libraryName',
                value: 'libraryCode',
              }"
              @change="secondInstallationProjectNameByDropdownList"></a-select>
          </a-form-item>
          <!-- :label="`${inputData.constructMajorType}二级专业`" -->
          <a-form-item
            label="定额专业"
            v-if="showType.projectType == 'danwei'"
            name="secondInstallationProjectName"
            class="lastChild"
            :rules="[{ required: true, message: '请选择定额专业!' }]">
            <a-select
              v-model:value="inputData.secondInstallationProjectName"
              :placeholder="`请选择${
                inputData.constructMajorType ? inputData.constructMajorType : ''
              }定额专业`"
              :options="lists.secondDropdownList"
              :fieldNames="{ label: 'cslbName', value: 'cslbName' }"></a-select>
          </a-form-item>
          <a-form-item
            label="计税方式"
            name="taxCalculationMethod"
            :rules="[{ required: true, message: '请选择计税方式!' }]"
            v-if="!inputData.importName">
            <a-select
              v-model:value="inputData.taxCalculationMethod"
              placeholder="请选择计税方式"
              :options="lists.taxMathodList"
              :fieldNames="{ label: 'value', value: 'code' }"
              @change="chengeTaxMeathod($event)"></a-select>
          </a-form-item>
          <a-form-item
            label="税改文件"
            name="taxReformDocumentsId"
            v-if="
              inputData.taxCalculationMethod === '1' &&
              inputData.ration === '河北12定额标准' &&
              !inputData.importName
            "
            :rules="[
              {
                required: true,
                message: '请选择税改文件!',
              },
            ]">
            <a-select
              v-model:value="inputData.taxReformDocumentsId"
              placeholder="请选择税改文件"
              :options="lists.fileList"
              :fieldNames="{ label: 'value', value: 'code' }"></a-select>
          </a-form-item>
        </template>

        <template v-else>
          <!-- 工料机 -->
          <a-form-item label="计价方法" name="name">
            <a-input v-model:value.trim="jjName" disabled style="background: white; color: black" />
          </a-form-item>

          <a-form-item
            label="项目名称"
            name="name"
            :rules="[
              {
                required: true,
                message: '请输入项目名称！',
              },
              {
                max: 50,
                message: '项目名称长度不可超过50个字！',
              },
              {
                pattern: /^[^`\^。>？!！￥~!@$^&*\=[\]{}\\|;:<>/?]*$/,
                message: '项目名称不可包含特殊字符！',
              },
            ]">
            <a-input v-model:value.trim="inputData.name" placeholder="请输入项目名称" />
          </a-form-item>
          <a-form-item
            label="项目编码"
            name="code"
            :rules="[
              {
                max: 50,
                message: '项目编码长度不可超过50个字！',
              },
              {
                pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]{1,}$/,
                message: '项目编码不可包含特殊字符！',
              },
            ]">
            <a-input v-model:value.trim="inputData.code" placeholder="请输入项目编码" />
          </a-form-item>
          <a-form-item
            label="定额标准"
            name="deStandardReleaseYear"
            :rules="[{ required: true, message: '请选择定额标准！' }]">
            <a-select
              v-model:value="inputData.deStandardReleaseYear"
              :options="lists.rationList"
              placeholder="请选择"
              :size="colStyle.colSize"></a-select>
          </a-form-item>
          <a-form-item
            label="计税方式"
            name="taxCalculationMethod"
            :rules="[{ required: true, message: '请选择计税方式！' }]">
            <a-select
              v-model:value="inputData.taxCalculationMethod"
              :options="taxCalculationMethodArr"
              placeholder="请选择"
              :size="colStyle.colSize"></a-select>
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ span: 20 }" class="sure">
          <a-button type="primary" :loading="spinning || loading" html-type="submit">新建</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>

  <a-upload
    class="file-uploadFile"
    name="file"
    :maxCount="1"
    :showUploadList="false"
    :customRequest="uploadFile"
    :before-upload="beforeUpload"
    accept=".xml"
    v-show="false"></a-upload>
  <edit-project-structure
    v-model:visible="editVisible"
    :config="editConfig"
    @success="editSuccess"
    @editClose="editClose"></edit-project-structure>

  <!-- zip,rar项目校验 -->
  <check-file ref="checkFileRef"></check-file>
</template>

<script setup>
import csProject from '../../api/csProject';
import {
  getCurrentInstance,
  onMounted,
  reactive,
  toRaw,
  watch,
  ref,
  useAttrs,
  nextTick,
} from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { ipcApiRoute } from '@/api/main';
import { proModelStore } from '@/store/proModel';
import { projectDetailStore } from '@/store/projectDetail';
import csGljProject from '@/gongLiaoJiProject/api/csProject';
import { ipcApiRoute as ipcGljApiRoute } from '@gongLiaoJi/api/main';

const store = proModelStore();
const proStore = projectDetailStore();
const attrs = useAttrs();
const emits = defineEmits(['closeDialog', 'setLoading']);
const form = ref();

const globalProperties = getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;

const router = useRouter();
const showType = defineProps({
  projectType: {
    type: String, //类型字符串
    default: 'zhaobiao',
  },
});
const colStyle = reactive({
  colSize: null,
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
});

const loading = ref(false);
const spinning = ref(false);
const inputData = reactive({
  constructName: null, //项目名称
  constructCode: null, //项目编码
  mainfest: null, //清单标准
  ration: null, //定额标准
  importUrl: null, //  电子标
  importName: null,
  fileExtension: null,
  xmlFactory: null,
  // unitProjectName:null,//单位工程名称
  constructMajorType: null, // 工程专业类型
  libraryCode: null,
  secondInstallationProjectName: null,
  taxCalculationMethod: null,
  taxReformDocumentsId: null,
});
const lists = reactive({
  mainfestList: [], //清单标准列表
  rationList: [], //定额标准列表
  engineerMajorList: [], //工程专业列表
  xmlList: [],
  majorTypeDropdownList: [],
  secondDropdownList: [],
  taxMathodList: [], //计税方式
  fileList: [], //税改文件
});

// 点击选择文件
const upFile = () => {
  if (loading.value || spinning.value) return;
  document.querySelector('.file-uploadFile .ant-upload input').click();
};

/**
 * @description: 文件上传到oss
 * @param {*} params
 * @return {*}
 */
const uploadFile = async params => {
  loading.value = true;
  const { file } = params;
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  loading.value = false;
  inputData.importUrl = file.path;

  if (!inputData.constructName) {
    inputData.constructName = name;
    form.value?.clearValidate('constructName');
    form.value?.validateFields('constructName');
  }
};

// 上传前置判断
const beforeUpload = file => {
  if (file.name.length > 100) {
    message.error('上传文件名不能超过100字!');
    return false;
  }

  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
  if (!['xml'].includes(fileType)) {
    // 电子标只支持xml格式文件 12/5
    message.error('上传文件格式不正确!');
    return false;
  }
  if (['xml'].includes(fileType)) {
    inputData.fileExtension = 'xml';
  }
  inputData.importName = file.name;
  return true;
};

const onSubmit = () => {
  //此处应该将vueX里面的弹框数据置为true
  // if (!verify()) {
  //   return false
  // }

  if (showType.projectType == 'gongliaoji') {
    saveGljProject();
    return;
  }

  const proType =
    showType.projectType === 'toubiao' ? 1 : showType.projectType === 'danwei' ? 2 : 0;
  const iptData = toRaw(inputData);
  const qdStandard = lists.mainfestList.filter(item => item.value === inputData.mainfest);
  const deStandard = lists.rationList.filter(item => item.value === inputData.ration);
  let formData = {
    ...iptData,
    ssProvince: 130000,
    ssProvinceName: '河北省',
    biddingType: proType,
    // constructName:proType =='2'?iptData.unitProjectName:iptData.constructName,//项目名称
    constructName: iptData.constructName, //项目名称
    constructCode: iptData.constructCode, //项目编码
    qdStandardId: qdStandard && qdStandard[0].code, //清单标准
    deStandardId: deStandard && deStandard[0].code, //定额标准
    constructMajorType: iptData.constructMajorType,
  };
  // console.log('formData', formData);
  // importProject 新建投标，并且选择了电子表
  const isDianZiBiao = showType.projectType === 'toubiao' && formData.importUrl;
  let apiName = isDianZiBiao ? 'importProject' : 'newBudgetProject';
  if (iptData.importName) {
    delete formData.taxCalculationMethod;
    delete formData.taxReformDocumentsId;
  }
  spinning.value = true;
  emits('setLoading', true);
  console.log('新建formData', formData);
  setTimeout(() => {
    csProject[apiName](formData)
      .then(function (response) {
        console.log('新建预算------------------', response);
        if (response.status === 200) {
          if (response.result) {
            if (isDianZiBiao) {
              handleFileSuccess({
                list: response.result,
                isZip: ['zip', 'rar'].includes(formData.fileExtension.toLowerCase()),
              });
              return;
            } else {
              message.success('新建成功');
              reset();
              emits('closeDialog');
            }
          }
        } else {
          message.error(response.message);
        }
      })
      .finally(() => {
        store.SET_Refresh(!store.isRefresh);
        spinning.value = false;
        emits('setLoading', false);
      });
  }, 0);
};

// 导入投标文件，新建投标，并且选择电子标成功
let ConstructId = ref('');
const editVisible = ref(false);
let editConfig = reactive({
  showMenu: false, //隐藏编辑按钮
  constructObj: null, //项目树
  isEdit: true, // 是否可以编辑工程专业
});
// 编辑结构成功
const editSuccess = constructSequenceNbr => {
  reset();
  emits('closeDialog');
  editVisible.value = false;
  store.SET_Refresh(!store.isRefresh);
  // store.commit('SET_ShowProList', true);
  // store.commit('SET_ShowNewPro', true);
};
const editClose = type => {
  if (type !== 'noBack') {
    csProject.deleteProject({ sequenceNbr: ConstructId.value }).then(res => {
      console.log('删除', res);
    });
  }
};
const checkFileRef = ref();
const handleFileSuccess = ({ list, isZip }) => {
  editConfig.constructObj = list;
  console.log('🚀 ~handleFileSuccess ~ list:', list);
  ConstructId.value = list[0]?.id;
  proStore.deType = inputData.ration === '河北22定额标准' ? '22' : '12';
  isZip ? checkFileRef.value?.open() : (editVisible.value = true);
};

watch(
  () => showType.projectType,
  oldVal => {
    loading.value = false;
    spinning.value = false;
    form.value.clearValidate();
    reset();

    if (showType.projectType == 'gongliaoji') {
      queryRationList();
    } else {
      quotaStandardDropdownList();
    }
  }
);
//submit提交内容校验
const verify = () => {
  if (!inputData.constructName) {
    showType.projectType == 'danwei'
      ? message.error('请输入单位工程名称')
      : message.error('请输入项目名称');
    return false;
  }

  if (!inputData.iptList.mainfest) {
    message.error('请选择清单标准');
    return false;
  }
  if (!inputData.ration) {
    message.error('请选择定额标准');
    return false;
  }
  if (showType.projectType == 'danwei' && !inputData.constructMajorType) {
    message.error('请选择工程专业');
    return false;
  }
  return true;
};
// input框输入值置为空
const reset = () => {
  for (let key in inputData) {
    // 定额和清单不清空
    if (!['mainfest', 'ration'].includes(key)) {
      inputData[key] = null;
    }
  }
  inputData.taxCalculationMethod = lists.taxMathodList && lists.taxMathodList[0].code;
  inputData.ration = lists.rationList.find(item => item.value === '河北22定额标准')?.value;
  lists.secondDropdownList = [];
  chengeTaxMeathod(inputData.taxCalculationMethod);
};

// 获取清单标准列表
const listStandardDropdownList = () => {
  const postData = {
    areaId: 130000,
    type: '1',
  };
  $ipc.invoke(ipcApiRoute.listStandardDropdownList, postData).then(result => {
    if (result.status === 200) {
      result.result.map(item => {
        lists.mainfestList.push({
          code: item.sequenceNbr,
          value: item.name,
        });
      });

      inputData.mainfest = lists.mainfestList[0]?.value;
    }
  });
};

// 获取定额标准下拉列表
const quotaStandardDropdownList = async () => {
  const postData = {
    areaId: 130000,
    type: '2',
  };
  $ipc.invoke(ipcApiRoute.quotaStandardDropdownList, postData).then(result => {
    if (result.status === 200) {
      lists.rationList = [];
      result.result.map(item => {
        lists.rationList.push({
          code: item.sequenceNbr,
          value: item.name,
        });
      });

      inputData.ration = lists.rationList.find(item => item.value === '河北22定额标准')?.value;
      queryEngineerMajorList();
    }
  });
};

//获取专业列表下拉列表
const queryEngineerMajorList = () => {
  const postData = {
    deStandard: inputData.ration === '河北22定额标准' ? '22' : '12',
  };
  $ipc.invoke(ipcApiRoute.engineeringSpecialtiesDropdownList, postData).then(function (response) {
    // console.log('queryEngineerMajorList', postData);
    lists.engineerMajorList = [];
    response.result.map(item => {
      lists.engineerMajorList.push({
        key: item.sequenceNbr,
        value: item.unitProjectName,
      });
    });
  });
};

// 获取xml厂商
const getXml = () => {
  $ipc.invoke(ipcApiRoute.xmlFactroyDropdownList).then(res => {
    const list = [];
    res.map(item => {
      list.push({ value: item, label: item });
    });
    lists.xmlList = list;
  });
};

const typeChange = async value => {
  inputData.secondInstallationProjectName = null;
  inputData.libraryCode = null;
  await constructMajorTypeDropdownList();
  //所有专业均有二级专业
  secondInstallationProjectNameByDropdownList();
};

const constructMajorTypeDropdownList = async () => {
  let apiData = {
    deType: inputData.ration === '河北22定额标准' ? '22' : '12',
    constructMajorType: inputData.constructMajorType,
  };
  const res = await csProject.getMainDeLibrary(apiData);
  console.log('constructMajorTypeDropdownList', apiData, res);
  if (res.status === 200) {
    lists.majorTypeDropdownList = res.result;
    lists.majorTypeDropdownList.forEach(item => {
      if (item.defaultDeFlag === 1) {
        inputData.libraryCode = item.libraryCode;
      }
    });
  }
};

const secondInstallationProjectNameByDropdownList = () => {
  csProject
    .getSecondInstallationProjectName({
      constructMajorType: inputData.constructMajorType,
      libraryCode: inputData.libraryCode,
      deStandard: inputData.ration === '河北22定额标准' ? '22' : '12',
    })
    .then(res => {
      if (res.status === 200) {
        lists.secondDropdownList = res.result;

        const hasDq = res.result.some(i => {
          return i.cslbName === '电气设备安装工程';
        });
        inputData.secondInstallationProjectName =
          hasDq && inputData.constructMajorType === '安装工程'
            ? '电气设备安装工程'
            : lists.secondDropdownList[0].cslbName;
      }
    });
};

onMounted(() => {
  listStandardDropdownList();
  quotaStandardDropdownList();
  getXml();
  gettaxMethodList();
});
const gettaxMethodList = () => {
  lists.taxMathodList = [];
  csProject.getDefaultTaxCalculation().then(function (res) {
    // console.log('gettaxMethodList', res);
    if (res.status === 200) {
      for (var i of res.result.taxCalculationMethodOption) {
        for (var k in i) {
          lists.taxMathodList.push({
            code: k,
            value: i[k],
          });
        }
      }
      inputData.taxCalculationMethod = lists.taxMathodList && lists.taxMathodList[0].code;
      for (var i of res.result.taxReformDocumentsOption) {
        for (var k in i) {
          lists.fileList.push({
            code: k,
            value: i[k],
          });
        }
      }
      chengeTaxMeathod(inputData.taxCalculationMethod);
    }
  });
};
const chengeTaxMeathod = eve => {
  if (Number(eve) === 1) {
    inputData.taxReformDocumentsId = lists.fileList && lists.fileList[0].code;
  } else {
    inputData.taxReformDocumentsId = null;
  }
};
const chengeRation = eve => {
  inputData.constructMajorType = null;
  inputData.secondInstallationProjectName = null;
  inputData.libraryCode = null;
  if (eve === '河北22定额标准') {
    inputData.taxCalculationMethod = '1';
    inputData.taxReformDocumentsId = null;
  } else {
    inputData.taxCalculationMethod = '1';
    inputData.taxReformDocumentsId = lists.fileList && lists.fileList[0].code;
  }
  queryEngineerMajorList();
};

// 工料机
const jjName = ref('工料法');
const taxCalculationMethodArr = ref([
  { label: '一般计税', value: 1 },
  { label: '简易计税', value: 0 },
]);

//获取定额标准下拉列表
const queryRationList = () => {
  const postData = {
    provenceCode: 130000,
    cityCode: 130100,
  };
  $ipc.invoke(ipcGljApiRoute.getDeLibraryByDirection, postData).then(function (result) {
    if (result.status === 200) {
      lists.rationList = [];
      let datas = result.result;
      lists.rationList.push({
        value: datas[0].sequenceNbr,
        label: datas[0].libraryCode === '2022-JZGC-DEY' ? '河北省2022序列定额' : '',
      });
      nextTick(() => {
        inputData.deStandardId = lists.rationList[0]?.value;
        inputData.deStandardReleaseYear = lists.rationList[0]?.value;
        inputData.taxCalculationMethod = 1;
      });
    }
  });
};

// 点击新建工料机项目
const saveGljProject = () => {
  if (spinning.value || loading.value) {
    message.error('正在发送中...');
    return;
  }
  form.value.validateFields().then(() => {
    spinning.value = true;
    const { deStandardId, name, code, deStandardReleaseYear, taxCalculationMethod } =
      toRaw(inputData);
    let postData = {
      name,
      code,
      deStandardReleaseYear,
      deStandardId,
      taxCalculationMethod,
      type: 1,
    };
    console.log('参数：', toRaw(postData));
    emits('setLoading', true);

    csGljProject
      .createProject({ ProjectModel: postData })
      .then(res => {
        if (res.status !== 200) {
          spinning.value = false;
          message.error(res.message);
          return false;
        }
        emits('closeDialog');
        let obj = {
          constructId: res.result.sequenceNbr,
        };
        $ipc.invoke(ipcGljApiRoute.openGljProject, { ...toRaw(obj) }).then(res2 => {});
      })
      .finally(() => {
        store.SET_Refresh(!store.isRefresh);
        spinning.value = false;
        emits('setLoading', false);
      });
  });
};
</script>
<style lang="scss" scoped>
::v-deep .ant-button {
  margin: auto !important;
}
::v-deep .ant-select {
  text-align: initial !important;
}
.normalClass {
  ::v-deep .ant-form-item {
    margin-bottom: 15px;
  }
}
// 增加计税方式后的样式
.newClass {
  ::v-deep .ant-form-item {
    margin: 0 0 8px 20px;
  }
}
.toubiaoClass {
  ::v-deep .ant-form-item {
    margin: 0 0 15px 20px;
  }
}

::v-deep .ant-form-item .ant-upload {
  width: 100% !important;
  button {
    width: 100% !important;
    text-align: left !important;
    color: #c0c0c0;
  }
}

::v-deep .lastChild .ant-form-item-label {
  margin: 0 6px 0 -5px;
}

.sure {
  position: absolute;
  bottom: 0px;
  left: 40%;
}
.content {
  padding-top: 30px;
  font-size: 14px;
  text-align: center;
  height: 95%;
  position: relative;
  // padding-left: 25px;
}
</style>
