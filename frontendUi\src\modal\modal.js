/*
 * @Descripttion: 关联数据
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-04 16:05:09
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-20 14:38:28
 */

import { ref, nextTick, watch ,toRaw} from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import redoApi from '@/api/redo';
import { useRoute } from 'vue-router';
// import xeUtils from 'xe-utils';
const xeUtils = require('xe-utils');
import modalData from '@/modal/modalData';
const { ipcRenderer } = require('electron');

export const createModal = (type, props, callback = (name, data) => {}) => {
  const route = useRoute();
  const projectStore = projectDetailStore();

  const openModal = (newProps) => {
    props = newProps
    redoApi
    .setModalState({
      windowId: projectStore.currentTreeGroupInfo?.constructId,
      type,
      showType: 'show',
    })
    .then(res => {
      updateData();
    });
    // redoApi
    //   .createModal({
    //     windowId: projectStore.currentTreeGroupInfo?.constructId,
    //     modal: JSON.parse(JSON.stringify(modalData.ys)).find(
    //       a => a.type === type
    //     ),
    //   })
    //   .then(res => {
    //     setTimeout(() => {
    //      updateData();
    //     }, 200);
    //   });
  };
  const updateData = xeUtils.debounce(() => {
    console.log(
      '执行往子窗口发送消息',
      projectStore.currentTreeGroupInfo?.constructId
    );
    ipcRenderer.send('postMsgToChild', {
      windowId: projectStore.currentTreeGroupInfo?.constructId,
      type: type,
      props: props,
      store: JSON.stringify(xeUtils.clone(projectStore.$state, true)),
    });
  }, 20);
  watch([props, projectDetailStore], ([newProps, newProjectDetailStore]) => {
    updateData();
  });
  ipcRenderer.on(
    'emitFun',
    xeUtils.debounce((event, data) => {
      console.log('emitFun', data);
      // 在渲染进程中处理数据
      if (data.type === type) {
        callback(data.name, data.data);
      }
    }, 20)
  );
  const postMsgToChild = (props) => {
    console.log(
      '执行往子窗口发送消息',
      projectStore.currentTreeGroupInfo?.constructId
    );
    ipcRenderer.send('postMsgToChild', {
      windowId: projectStore.currentTreeGroupInfo?.constructId,
      type: type,
      props,
      store: JSON.stringify(xeUtils.clone(projectStore.$state, true)),
    });
  }
  return {
    openModal,
    postMsgToChild
  };
};

/**
 * 发送消息到父窗口
 * @param {String} type 窗口id
 * @param {{name:String,data:Object}} options 发送的数据
 * @param {String} options.name 事件名称
 * @param {Object} options.data 事件数据
 * @return {undefined}
 */
export const emitFunc = (type, { name, data }) => {
  ipcRenderer.send(`postMsgToParent-${type}`, {
    windowId: getUrlParam(window.location.href, 'constructSequenceNbr'),
    type: type,
    name: name,
    data: toRaw(data),
  });
};
/**
 * 通过url和paramName获取url参数
 * @param {String} url url
 * @param {String} paramName 参数名称
 * @return {String | null} 参数值
 */
const getUrlParam = (url, paramName) => {
  const reg = new RegExp(`[?&]${paramName}=([^&#]*)`, 'i');
  const match = reg.exec(url);
  return match ? decodeURIComponent(match[1]) : null;
};
