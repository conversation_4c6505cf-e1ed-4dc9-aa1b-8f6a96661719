<!--
 * @Descripttion: 
-->
<template>
  <div class="rich-text">
    <div class="head">
      <div
        v-for="item in menuList"
        :key="item.key"
        :class="{ item: true, active: item.value === true && item.icon }"
        :data-key="item.key"
        @click="itemClick(item)"
      >
        <icon-font
          v-if="item.icon"
          :type="item.icon"
          class="icon-font"
        ></icon-font>
        <a-select
          v-else-if="item.key === 'fontFamily'"
          v-model:value="item.value"
          style="width: 60px"
        >
          <template #suffixIcon>
            <CaretDownOutlined />
          </template>
          <a-select-option v-for="opt in item.options" :value="opt.value">{{
            opt.value
          }}</a-select-option>
        </a-select>
        <div class="input-select" v-else-if="item.key === 'fontSize'">
          <a-auto-complete
            :bordered="true"
            style="min-width: 40px"
            v-model:value="item.value"
            :options="item.options"
          />
          <CaretDownOutlined class="icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { CaretDownOutlined } from '@ant-design/icons-vue';
const menuList = ref([
  {
    key: 'fontBold',
    icon: 'icon-jiacu',
    value: false,
  },
  {
    key: 'fontItalic',
    icon: 'icon-qingxie',
    value: false,
  },
  {
    key: 'textUnderline',
    icon: 'icon-xiahuaxian',
    value: false,
  },
  {
    key: 'alignLeft',
    icon: 'icon-zuoduiqi',
    value: false,
  },
  {
    key: 'alignCenter',
    icon: 'icon-juzhong',
    value: false,
  },
  {
    key: 'alignRight',
    icon: 'icon-youduiqi',
    value: false,
  },
  {
    key: 'fontFamily',
    icon: '',
    options: [{ value: '宋体' }, { value: '黑体' }, { value: '微软雅黑' }],
    value: '宋体',
  },
  {
    key: 'fontSize',
    icon: '',
    options: [
      { value: 6 },
      { value: 7 },
      { value: 8 },
      { value: 9 },
      { value: 10 },
      { value: 11 },
      { value: 12 },
      { value: 14 },
      { value: 16 },
      { value: 18 },
      { value: 20 },
      { value: 24 },
      { value: 30 },
      { value: 36 },
    ],
    value: 12,
  },
]);

const itemClick = item => {
  if (['alignLeft', 'alignCenter', 'alignRight'].includes(item.key)) {
    menuList.value.forEach(it => {
      if (
        ['alignLeft', 'alignCenter', 'alignRight'].includes(it.key) &&
        it.key !== item.key &&
        !item.value
      ) {
        // 取消其他选中状态，重复点击当前的不取消选中状态
        it.value = false;
      }
    });
  }
  if (item.icon && !item.options) {
    item.value = !item.value;
  }
};
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  :deep(.ant-select-selector) {
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
  }
  :deep(.ant-select-arrow) {
    right: 1px !important;
    color: #535353 !important;
  }
  :deep(.ant-select-selection-search) {
    left: 0;
    right: 0;
  }
}
.item {
  padding: 2px 4px;
  margin: 0 2px;
  cursor: pointer;
  &.active {
    background: rgba($color: #000000, $alpha: 0.3);
  }
}
.input-select {
  position: relative;
  .icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
