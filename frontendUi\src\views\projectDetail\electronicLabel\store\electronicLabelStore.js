/*
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: renmingming
 * @LastEditTime: 2024-10-11 17:06:30
 */
import { defineStore } from 'pinia';

export const electronicLabel = defineStore('electronicLabelStore', {
  state: () => {
    return {
        // 弹框中，招标和投标的项目的id
        "ppjg":{
            tbId:'',
            zbId:''
        },
        "treeActiveList":[],
        'treeActive':{}
    };
  },
  getters: { },
  actions: {
      SET_PPJG(info) {
          this.ppjg.tbId = info.tbId;
          this.ppjg.zbId = info.zbId;
      },
      SET_TREE_ACTIVE(info) {
          let list = [];
          let loop = (arge)=>{
              list.unshift(arge);
              arge.parent ? loop(arge.parent) : '';
          }
          loop(info);
          loop = null;
          this.treeActiveList = list;
          this.treeActive = info;
          console.log(this.treeActiveList)
      },
      SET_CLEAR(){
          this.ppjg.tbId = '';
          this.ppjg.zbId = '';
          this.treeActiveList = [];
          this.treeActive = [];
      }
  }
});
