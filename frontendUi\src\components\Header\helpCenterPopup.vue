<!--
 * @Descripttion: 帮助中心
 * @Author: liuxia
 * @Date: 2024-11-26 15:37:01
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-27 09:44:03
-->
<template>
  <common-modal
    className="dialog-comm set-dialog"
    v-model:modelValue="props.helpCenterVisible"
    :title="modalTitle"
    width="1100"
    height="800"
    min-height="550"
    min-width="900"
    :show-zoom="true"
    @close="close"
  >
    <div class="content">
      <div class="left">
        <vxe-table
          align="center"
          border="none"
          :show-header="false"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true, isCurrent: true }"
          :edit-config="{ trigger: 'click', mode: 'cell' }"
          :tooltip-config="{
            showAll: true,
            enterable: true,
            contentMethod: null,
          }"
          :tree-config="{
            transform: true,
            rowField: props.fileType === 1 ? 'id' : 'sequenceNbr',
            parentField: 'parentId',
            iconOpen: 'vxe-icon-square-minus-fill',
            iconClose: 'vxe-icon-square-plus-fill',
          }"
          :data="tableData"
          height="auto"
          :cell-class-name="cellClassName"
          keep-source
          ref="policyTable"
        >
          <vxe-column title="说明" tree-node align="left">
            <template #default="{ row }">
              <span
                v-if="!row.parentId && row.name === '人工费文件'"
                class="grandFatherFilePeople title"
              >
                <icon-font :type="peopleFile.icon" />
                <span>{{ row.name }}</span>
              </span>
              <span
                v-if="row.parentId && row.parentId === '人工费文件'"
                class="grandFatherFilePeople"
              >
                <vxe-select
                  v-model="rgSelected"
                  placeholder=""
                  style="width: 150px"
                  @change="getTableData($event)"
                  placement="bottom"
                >
                  <vxe-option
                    v-for="item in showRGList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                  ></vxe-option>
                </vxe-select>
              </span>

              <span
                class="grandFatherFileSafe title"
                v-else-if="!row.parentId && row.name === '安防费率文件'"
              >
                <icon-font :type="safeFile.icon" />
                <span>{{ row.name }}</span>
              </span>
              <span
                class="grandFatherFileGui title"
                v-else-if="!row.parentId && row.name === '规费文件'"
              >
                <icon-font :type="guiFile.icon" />
                <span>{{ row.name }}</span>
              </span>
              <span v-else-if="row.parentId" @click="lookDetail(row)">
                <span class="title-info">{{ row.name }}</span>
              </span>
              <span
                class="title-info"
                v-else-if="props.fileType === 2"
                @click="lookDetail(row)"
              >
                {{ row.name }}</span
              >
            </template></vxe-column
          >
          <template #empty>
            <span
              style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              "
            >
              <icon-font
                style="font-size: 22px"
                type="icon-zanwushuju"
              ></icon-font>
              暂无数据
            </span>
          </template>
        </vxe-table>
      </div>
      <div class="right">
        <div style="width: 100%; height: 100%">
          <iframe
            :src="currentInfo?.fileUrl"
            title="description"
            width="100%"
            height="100%"
            v-if="currentInfo?.fileUrl"
          ></iframe>
          <p v-if="!currentInfo?.fileUrl" class="noData">暂无数据</p>
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref, watch } from 'vue';
import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail';
import { getUrl } from '@/utils/index.js';
import feePro from '@/api/feePro.js';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
const props = defineProps(['helpCenterVisible', 'fileType']);
const emits = defineEmits(['closePopup']);
let store = projectDetailStore();

let tabList = [
  {
    key: 'file',
    name: '政策文件',
  },
  {
    key: 'setting',
    name: '勘误说明',
  },
];

const peopleFile = ref({
  icon: 'icon-rengongfeiwenjian',
  isRadio: '',
  title: '人工费文件',
});
const safeFile = ref({
  icon: 'icon-anfangfeishuaiwenjian',
  isRadio: '',
  title: '安防费率文件',
});
const guiFile = ref({
  icon: 'icon-guifeiwenjian',
  isRadio: '',
  title: '规费文件',
});

const policyTable = ref();
let tabKey = ref('file');
let docList = ref(null);
let selectListNo = ref([]); //选中的数据
let periodicalData = ref([]); // 勘误说明数据
let tableData = ref([]); // 政策文件列表
let currentInfo = ref(null); // 当前点击的文件内容
let showRGList = ref([]); //人工费文件下拉列表
let noRGlist = ref([]);
let rgSelected = ref(); //人工费最初展示的市
let firstSelectList = ref([]); //最开始获取到的数据里面选中的文件
let modalTitle = ref('政策文件');

watch(
  () => props.helpCenterVisible,
  value => {
    if (value) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          if (props.fileType === 1) {
            modalTitle.value = '政策文件';
            getFeePolicyDocData();
          } else {
            modalTitle.value = '勘误说明';
            getPeriodicalData();
          }
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  }
);

const visible = ref(true);
const close = () => {
  emits('closePopup');
};

// 勘误说明数据
const getPeriodicalData = () => {
  api.getPeriodicalData().then(res => {
    console.log('res111111111', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
      policyTable.value.setCurrentRow(res.result[0]);
      currentInfo.value = res.result[0];
    }
  });
};

// 政策文件列表
const getFeePolicyDocData = () => {
  let apiData = {
    type: 1,
    constructId:
      store.currentTreeInfo?.type === 1
        ? store.currentTreeInfo?.id
        : store.currentTreeGroupInfo?.constructId,
  };
  selectListNo.value = [];
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      selectListNo.value = [
        Number(res.result.rgfId),
        Number(res.result.awfId),
        Number(res.result.gfId),
      ];
      getFinallyData(res.result && res.result.basePolicyDocumentMap);
    } else {
      tableData.value = [];
    }
  });
};
//政策文件处理
const getFinallyData = data => {
  docList.value = [];
  getList(data, null);

  let newobj = {};
  docList.value = docList.value.reduce((preVal, curVal) => {
    newobj[curVal.id] ? '' : (newobj[curVal.id] = preVal.push(curVal));
    return preVal;
  }, []);
  docList.value.map((item, index) => {
    item.sort = index + 1; //每条数据加序号
    if (selectListNo.value.includes(Number(item.sequenceNbr))) {
      item.selectNo = true;
    } else {
      item.selectNo = false;
    }
  });
  // tableData.value = docList.value;
  showRGList.value = docList.value.filter(
    item => item.parentId === '人工费文件'
  );
  noRGlist.value = docList.value.filter(
    item => item.parentId !== '人工费文件' && item.grandFather !== '人工费文件'
  );
  firstSelectList.value = docList.value.filter(item => item.selectNo);
  currentInfo.value = firstSelectList.value[0];
  getTableData();
  console.log('tableData.value', tableData.value);
};
const getList = (data, parId) => {
  let keyList = [];
  let valueList = [];
  let list = [];
  for (let key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      if (key === '人工费文件') {
        docList.value.push({ id: key, parentId: null, name: key });
        getList(data['人工费文件'], key);
      } else {
        if (parId) {
          keyList.push({
            id: key,
            parentId: parId,
            name: key,
          });
          data &&
            data[key] &&
            data[key].map((item, index) => {
              item.parentId = item.cityName;
              item.id = `${key}${index}`;
              item.grandFather = '人工费文件';
            });
        } else {
          keyList.push({ id: key, parentId: null, name: key });
          data &&
            data[key] &&
            data[key].map((item, index) => {
              item.parentId = key;
              item.id = `${key}${index}`;
            });
        }
        valueList.push(...data[key]);
        list.push(...keyList, ...valueList);
      }
    }
  }
  docList.value.push(...list);
};
//获取筛选项之后的tableData
const getTableData = (eve = null) => {
  let city = eve?.value;
  console.log('--------', city);
  let rgList = [];
  if (eve) {
    rgList = docList.value.filter(
      item => item.cityName === city || item.id === city
    );
  } else {
    let rgSelect = docList.value.find(
      item => item.selectNo && item.grandFather === '人工费文件'
    );
    if (!rgSelect) {
      rgSelect = docList.value.find(
        item =>
          item.cityName === '石家庄市' && item.grandFather === '人工费文件'
      );
    }
    rgSelected.value = rgSelect && rgSelect.cityName;
    rgList = docList.value.filter(
      item =>
        item.cityName === rgSelect.cityName || item.id === rgSelect.cityName
    );
  }
  tableData.value = [...rgList, ...noRGlist.value];
  renderData();
};
//渲染表格数据
const renderData = () => {
  setTimeout(() => {
    const $table = policyTable.value;
    $table?.loadData(tableData.value);
    $table?.setTreeExpand(tableData.value, true);
  }, 0);
  setTimeout(() => {
    if (currentInfo.value) {
      console.log('currentInfo.value', currentInfo.value);
      let select = tableData.value.find(
        item => item.sequenceNbr === currentInfo.value.sequenceNbr
      );
      policyTable.value.setCurrentRow(select);
      policyTable.value.scrollToRow(currentInfo.value);
    } else {
      console.log('firstSelectList.value[0]', firstSelectList.value[0]);
      policyTable.value.setCurrentRow(firstSelectList.value[0]);
      policyTable.value.scrollToRow(firstSelectList.value[0]);
    }
  }, 100);
};

const lookDetail = row => {
  currentInfo.value = row;
};
</script>

<style lang="scss">
.set-dialog {
  width: 60%;
  max-width: 600px;
  min-width: 200px;
  .vxe-modal--content {
    padding-bottom: 0 !important;
  }
  .content {
    display: flex;
    height: 96%;
    .left {
      width: 236px;
      height: 653px;
    }
    .right {
      flex: 1;
    }
    .title-info {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
