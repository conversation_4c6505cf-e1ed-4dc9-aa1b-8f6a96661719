<template>
  <common-modal
    className="dialog-comm noMask"
    width="700"
    height="550"
    v-model:modelValue="props.visible"
    @cancel="closeFWFModal"
    @close="closeFWFModal"
    :mask="false"
    :loadingModal="loading">
    <template #header>
      <div class="vxe-modal--header-title">
        固定安文费（沧州）
        <a-tooltip placement="right" :overlayStyle="{ maxWidth: '500px', fontSize: '12px' }">
          <template #title>
            <span>计算规则：</span>
            <div v-html="awfTipText"></div>
          </template>
          <icon-font style="cursor: auto" type="icon-bangzhutishi"></icon-font>
        </a-tooltip>
      </div>
      <div class="vxe-modal--header-right">
        <i
          class="vxe-modal--close-btn trigger--btn vxe-icon-close"
          title="关闭"
          @click.stop="closeFWFModal"></i>
      </div>
    </template>
    <div class="awf-dialog-box">
      <awf-modal @successCallback="successCallback" @setLoading="setLoading"></awf-modal>
    </div>
  </common-modal>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import AwfModal from '@/views/projectDetail/customize/CostAnalysis/awfModal.vue';
const props = defineProps(['visible']);

const emits = defineEmits(['update:visible', 'refreshTableList']);
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
let itemAnalysisRef = ref();
const successCallback = () => {
  emits('refreshTableList');
  emits('update:visible', false);
};
let loading = ref(false);
const setLoading = bol => {
  loading.value = bol;
};
const closeFWFModal = () => {
  emits('update:visible', false);
};
let awfTipText = ref('');
onMounted(() => {
  setAwfTipText();
});
const setAwfTipText = () => {
  const deType = store.deType + '';
  const taxMade = store.taxMade + '';
  if (taxMade === '1' && deType === '12') {
    // 一般计税 && 12定额
    awfTipText.value = `安全生产、文明施工费（不含税）= 【直接费（不含安文费、设备费）+企管费+利润+价款调整】*安文费率 <br/>
安全生产、文明施工费税金 =（不含税安文费-不含税安文费*除税系数3%）*增值税费率9%-不含税安文费*除税系数3%+【（不含税安文费-不含税安文费*除税系数3%）*增值税费率-不含税安文费*除税系数3%】*附加税费计算费率市区13.22%；县城，城镇11%；非市区、县城、镇6.58%）<br/>
安全生产、文明施工费（含税）= 安全生产、文明施工费（不含税）+安全生产、文明施工费税金`;
  }
  if (taxMade === '1' && deType === '22') {
    // 一般计税 && 22定额
    awfTipText.value = `安全生产、文明施工费（不含税）= 【直接费（不含安文费、设备费）+企管费+利润+价款调整】*安文费率 <br/>
安全生产、文明施工费税金 = 安文费*税率（一般计税：9%）<br/>
安全生产、文明施工费（含税）= 安全生产、文明施工费（不含税）+安全生产、文明施工费税金`;
  }
  if (taxMade === '0' && deType === '12') {
    // 简易计税 && 12定额
    awfTipText.value = `安全生产、文明施工费（不含税）= 【直接费（不含安文费、设备费）+企管费+利润+价款调整】*安文费率<br/>
安全生产、文明施工费税金=不含税安文费*税率（市区3.38%；县城，城镇3.31%；非市区、县城、镇3.19%）<br/>
安全生产、文明施工费（含税）=安全生产、文明施工费（不含税）+安全生产、文明施工费税金`;
  }
  if (taxMade === '0' && deType === '22') {
    // 简易计税 && 22定额
    awfTipText.value = `安全生产、文明施工费（不含税）= 【直接费（不含安文费、设备费）+企管费+利润+价款调整】*安文费率<br/>
安全生产、文明施工费税金 = 安文费*税率（简易计税：3%）<br/>
安全生产、文明施工费（含税）= 安全生产、文明施工费（不含税）+安全生产、文明施工费税金`;
  }
};
let fixFWFModal = ref(false);
const fixAWFfn = () => {
  setAwfTipText();
  console.log('固定安文费');
  fixFWFModal.value = true;
};
</script>

<style lang="scss" scoped>
.awf-dialog-box {
  height: 100%;
  ::v-deep(.content) {
    height: calc(100% - 58px);
  }
}
</style>
