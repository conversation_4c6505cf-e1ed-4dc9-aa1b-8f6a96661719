<!--
 * @Descripttion: 清单快速组价
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: sunchen
 * @LastEditTime: 2024-05-06 11:08:31
-->
<template>
  <common-modal
    className="dialog-comm qdQuickPricing-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    :title="title"
    width="80vw"
    height="72vh"
    min-width="900px"
    min-height="780px"
    :mask="false"
    show-zoom
    resize
    :lock-view="false"
    :loading="loading"
    :loading-config="{
      text:'正在加载中...'
    }"
    :destroyOnClose="true"
  >
    <div class="content-wrap">
      <div class="content-box content-box-top">
        <div class="info-box">
          <div><label>清单名称：</label>{{globalData.editQD?.name}}</div>
          <div><label>清单编码：</label>{{globalData.editQD?.bdCode || globalData.editQD?.fxCode }}</div>
          <div><label>工程量：</label>{{globalData.editQD?.quantity}}</div>
        </div>
        <div class="btn-box">
          <!-- lockBtnStatus 锁定 -->
            <a-button size="small"  @click="switchData('prev')" :disabled="lockBtnStatus || !globalData.editQD?.sequenceNbr || firstQD ">上一条清单</a-button>
            <a-button size="small"  @click="switchData('next')" style="margin-left: 14px;"  :disabled="lockBtnStatus || !globalData.editQD?.sequenceNbr || lastQD" >下一条清单</a-button>
        </div>
      </div>

      <div class="content-box content-box-center">
        <div class="content-box-table">
          <div class="left-box"  @mouseup="handleMouseUp" ref="qdInfoRef">
            <div v-if="globalData.editQD?.name">清单名称：</div>
            <div>{{globalData.editQD?.name}}</div>
            <div v-if="globalData.editQD?.projectAttr">清单项目特征：</div>
            <div>{{ globalData.editQD?.projectAttr }}</div>
          </div>
          <div class="right-box">
            <vxe-table
              :row-class-name="rowClassName"
              :row-config="{ isCurrent: true, keyField: 'customIndex' }"
              :data="globalData.tableData"
              width="100%"
              height="auto"
              ref="vexTable"
              auto-resize
              :edit-config="{trigger: 'dblclick', mode: 'cell'}"
              @current-change="currentChange"
              @edit-closed="editClosedEvent"
              >
              <vxe-column field="bdCode" title="项目编码">
                <template #default="{row}">
                  {{ row.bdCode || row.fxCode }}
                </template>
              </vxe-column>
              <vxe-column field="name" title="名称"></vxe-column>
              <vxe-column field="unit" title="单位"></vxe-column>
              <vxe-column field="price" title="单价"></vxe-column>
              <vxe-column field="quantityExpression" title="工程量表达式" :edit-render="{autofocus: '.vxe-input--inner'}">
                <template #edit="{ row }">
                  <vxe-input
                      :disabled="row.disabled"
                      v-model="row.quantityExpression"
                      placeholder="工程量表达式"
                    ></vxe-input>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
        <div class="content-box-btn">
          <span class="tips">选择清单信息自动进行查找</span>
          <div class="btn-box">
            <a-button :disabled="globalData.useIndex == -1" @click="handleDel">删除</a-button>
            <a-button :disabled="saveStatus" style="margin-left: 12px;" type="primary" @click="handleSave">保存</a-button>
          </div>
        </div>
      </div>


      <div class="content-box content-box-bottom">
        <subTable ref="subTableRef"></subTable>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref,watch,defineAsyncComponent,toRaw,nextTick,reactive, computed, shallowRef, shallowReactive, defineExpose,getCurrentInstance } from 'vue';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import 'splitpanes/dist/splitpanes.css'
import { message } from 'ant-design-vue';
import { globalData } from './status.js';
import {
  quantityExpressionHandler,
  everyNumericHandler,
} from '@/utils/index';

import QuantitiesService from './quantitiesService.js';
const Quantities = new QuantitiesService();


const subTable = defineAsyncComponent(() => import('./subTable.vue'))
const cxt = getCurrentInstance();
const subTableRef = ref(null);
const vexTable = ref(null);
const $ipc = cxt.appContext.config.globalProperties.$ipc;
const props = defineProps({
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: String,
    default: 'fbfx',
  },
  lockBtnStatus:{
    type: Boolean,
    default: false
  }
});



const route = useRoute();
const emits = defineEmits(['closeDialog','refresh','posRow']);
let dialogVisible = ref(false)
const store = projectDetailStore();

let title = ref('快速组价');
let loading = ref(false)
let qdInfoRef = ref(null)


let qdList = ref([])
let switchBtnStatus = reactive({
  prev: false,
  next: false
})


const lastQD = computed(() => {
  return globalData.qdIdList[globalData.qdIdList.length-1] == globalData.editQD?.sequenceNbr
})
const firstQD = computed(() => {
  return globalData.qdIdList[0] == globalData.editQD?.sequenceNbr
})

// 过滤数据
const filterAddList = () =>{
  return globalData.tableData.filter(i=>{
    return !i.disabled
  })
}


const saveStatus = computed(() => {
  return globalData.tableData.every(i => {
    return i.disabled
  });
})


 const switchData = type => {
  const addList =  filterAddList()
  console.log("🚀 ~ switchData ~ addList:", addList)

  if(addList.length){
    infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: '是否保存当前修改',
        confirm: () => {
          infoMode.hide();
          handleSave()
        },
        close: () => {
          globalData.tableData = globalData.tableData.filter(x => x.disabled)
          infoMode.hide();
        },
      });
    return
  }

  const qdList = globalData.editAreaTable.filter(item => item.kind == '03' && (item.bdCode || item.fxCode));
  console.log('🚀 ~ switchData ~ qdList:', qdList);
  const Index = qdList.findIndex(i=>{
    return i.sequenceNbr == globalData.editQD.sequenceNbr
  })

  switchBtnStatus.prev = Index == 0
  switchBtnStatus.next = Index == qdList.length - 1

  if((Index == 0 && type=='prev') || (Index == qdList.length - 1 && type=='next')){
    message.error('已经是第一条或最后一条清单')
    return
  }
  globalData.editQD = qdList[type=='prev'?Index-1:Index+1]
  emits('posRow',globalData.editQD?.sequenceNbr)
};


const cancel = (refresh=false) => {
  if(refresh){
    emits('refresh');
    return
  }
  dialogVisible.value = false;
  emits('closeDialog');
};

const handleMouseUp = () => {
  const selection = window.getSelection();
  if (selection && !selection.isCollapsed) {
    // 检查选区是否完全包含在目标元素内（可选）
    globalData.searchText = selection.toString().replace(/\s+/g, '');;
  }
}




/**
 * type csxm :措施项目
 */
let businessType = ref('')
const open = (type='csxm') => {
  businessType.value = type
  dialogVisible.value = true;
  globalData.searchKey = ''
  qdList.value = []
  globalData.tableData = []
  getDeByQd()
};


const rowClassName = ({ row }) => {
  if (row?.disabled) {
    return 'row-disabled';
  }
};

const handleDel = () =>{
  globalData.tableData.splice(globalData.useIndex,1)
  globalData.useIndex = -1
}


const handleSave = () => {
  const addList =  filterAddList()
  console.log("🚀 ~ handleSave ~ addList:", addList)

  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    pointLine:JSON.parse(JSON.stringify(globalData.editQD)),
    rootLineId:store.asideMenuCurrentInfo?.sequenceNbr,
    fbfxOrCsxm: businessType.value,
    deType: store.deType,
    deLine:JSON.parse(JSON.stringify(addList)),
  }

  loading.value = true
  csProject.saveDe(postData).then(res => {
    loading.value = false
    message.success('保存成功')
    cancel(true)
  }).catch(err => {
    loading.value = false
    message.error('保存失败')
  })

}


 /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
   const editClosedEvent = ({ row, column }) => {
    console.log("🚀 ~ editClosedEvent ~ row:", row)
    console.log('表格ref', vexTable.value);
    let field = column.field;

    // 判断单元格值是否被修改
    if (field === 'quantity') {
      field = 'quantityExpression';
    }
    row.quantityExpression =
      column.field === 'quantity' ? row.quantity : row.quantityExpression;
    row.quantity = row.originalQuantity;
    console.log("🚀 ~ editClosedEvent ~ row.quantity:", row.quantity)

    nextTick(() => {
      expressionEditEvent(field, row, () => {
        vexTable.value.revertData(row, 'quantityExpression');
        row.quantityExpression = row.originalQuantityExpression;
      });
    });
  };


/**
   * 表达式处理
   * @param {*} field
   * @param {*} row
   * @param {*} revertDataCallback
   * @returns
   */
   const expressionEditEvent = (field, row, revertDataCallback) => {
    console.log('表达式处理', row);
    if (field !== 'quantityExpression') return;
    const expressionArr = row.quantityExpression.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const orgExpressionArr = row.originalQuantityExpression
      .toString()
      ?.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const [isSuccess, msg] = quantityExpressionHandler(row);
    if (isSuccess) {
      revertDataCallback();
      // infoVisible.value = true;
      // isSureModal.value = true;
      // infoText.value = msg;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: msg,
        confirm: () => {
          infoMode.hide();
        },
      });
    } else if (
      !orgExpressionArr?.includes('HSGCL') &&
      expressionArr.includes('HSGCL')
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '计算式输入非法，请重新输入标准四则运算表达式或数值',
        confirm: () => {
          infoMode.hide();
        },
      });
    } else if (
      !expressionArr.includes(row.quantityVariableName) &&
      orgExpressionArr?.includes(row.quantityVariableName)
    ) {
      infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: '工程量明细已被调用，是否清空工程量明细？',
        confirm: () => {
          globalData.tableData[globalData.useIndex] = { ...Quantities.initDatas(row) }
          console.log("🚀 ~ expressionEditEvent ~ globalData.tableData[globalData.useIndex]:", globalData.tableData[globalData.useIndex])
          updateFbData(row, 'quantityExpression');
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
    } else {
      row.quantityExpression = everyNumericHandler(row.quantityExpression);
      updateFbData(row, field);
    }
  };

const updateFbData = () => {
  Quantities.caculateQuantityExpressionAndQuantity(globalData.tableData[globalData.useIndex])
}

const getDeByQd = () =>{
  if(!globalData.editQD?.sequenceNbr){
    return
  }
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId:  store.currentTreeInfo?.id , //单位ID
    sequenceNbr: globalData.editQD.sequenceNbr
  }
  let j = 0
  csProject.getDeByQdId(postData).then(res => {
    console.log("🚀 ~ csProject.getDeByQdId ~ res:", res)
    globalData.tableData = res.map(i=>{
      i.disabled = true
      i.customIndex = j++
      delete i.parent
      return i
    })
  })
}


const currentChange = ({row,rowIndex}) => {
  if(!row?.disabled){
    globalData.useIndex = rowIndex
  }else{
    globalData.useIndex = -1

  }
}

const editQDsequenceNbr = computed(() => {
  return globalData.editQD?.sequenceNbr
})

watch(editQDsequenceNbr, (newv,oldv) => {
  if(oldv != newv){
    globalData.tableData = []
    globalData.searchText = ''
  }

  if(newv && dialogVisible.value){
    globalData.tableData = []
    getDeByQd()
  }
  globalData.useIndex = -1

},{
  immediate: true,
  deep: true
})



defineExpose({
  open,
  cancel
});
</script>

<style lang="scss">
.qdQuickPricing-dialog{
  user-select: none;
  .vxe-modal--box{
    overflow: hidden!important;;
  }

  .content-wrap{
      display: flex;
      flex-direction: column;
      height: 100%; /* 设置容器高度为视口高度，确保整个页面空间被使用 */
      .content-box-top{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .info-box{
          font-weight: 400;
          font-size: 14px;
          color: #606060;
          display: flex;
          align-items: center;
          gap: 20px;
          label{
            color: rgba(0, 0, 0, 1);
          }
        }
      }
    }

    .content-box-center{
      height:  48%;
      display: flex;
      flex-direction: column;
      margin:10px 0 6px;
      overflow: hidden;
      .content-box-table{
        display: flex;
        justify-content: space-between;
        flex: 1;
        overflow: hidden;
        .left-box{
          height: 100%;
            display: inline-block;
            width: 30%;
            margin-right: 14px;
            padding: 4px 2px;
            border: 1px solid #B9B9B9;
            font-size: 13px;
            user-select: text;
            overflow-y: auto;
            span{
              line-height: 1.6;
            }
        }
        .right-box{
          display: inline-block;
          width: calc(70% - 14px);
          height: 100%;
          border: 1px solid #B9B9B9;
        }
        .row-disabled{
          background: rgba(232, 234 ,236,.4) !important;
        }
      }
      .content-box-btn{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 6px;
        .tips{
          font-weight: 400;
          font-size: 12px;
          color: #DE3F3F;
        }
      }

    }
    .content-box-bottom{
      flex: 1;
      overflow: hidden;
    }
}
</style>
