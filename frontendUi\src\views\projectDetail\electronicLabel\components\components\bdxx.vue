<script setup>
import { onActivated, onMounted, reactive, ref, watch } from "vue";
import { useElectronicLabel } from '@/views/projectDetail/electronicLabel/hooks/useElectronicLabel.js';
import api from "@/api/electronicLabel.js";
import { message } from "ant-design-vue";
import { electronicLabel } from '@/views/projectDetail/electronicLabel/store/electronicLabelStore';
const electronicLabelStore = electronicLabel();
// 上一项 下一项 differenceItems
let { tableOptions, differenceItems, gridRef,listActiveList,gridOptionsData } = useElectronicLabel();
const gridOptionsTop = reactive({
  ...tableOptions,
  cellClassName:(e)=>{
    if(e.row['explanation']){
      return 'color-orange'
    }
  },
  height: '100%',
  columns: [
    { field: 'seq', width: 50, title: '',slots: { default: 'seqDefault' } },
    { field: 'name', title: '名称',showOverflow: true },
    { field: 'zbContext', title: '新招标书内容',showOverflow: true },
    { field: 'tbContext', title: '当前工程内容', showOverflow: true },
    { field: 'explanation', title: '更新说明',showOverflow: true},
  ],
  data: []
});
//查询标段信息的数据
const selectBdxx = async ()=>{
    let apiData = {
      zbConstructId: electronicLabelStore.treeActiveList[0].id
    };
    console.log(`api-selectJbxx参数`,apiData);
    try {
      gridOptionsTop.loading = true;
      const res = await api.selectJbxx(apiData);
      console.log(`api-selectJbxx返回值`,res);
      if(res.status === 200){
        gridOptionsTop.data = res.result || [];
        //有差异项的数据过滤,上一步下一步能力
        listActiveList.value = res.result.filter(item => {
          return !!item.explanation;
        }).map(item =>item.sequenceNbr);
        gridOptionsData.value = res.result || [];
      }else{
        message.error(res.message);
      }
    }catch (e) {
      console.error(`api-selectJbxx=>catch`,e);
      message.error(e.message);
    }finally {
      gridOptionsTop.loading = false;
    }
}
// 界面已完全加载完成，需要执行的逻辑方法
const initPage = (arge)=>{
  console.log('initPage',arge)
  selectBdxx();
};
defineExpose({
  initPage
})
</script>

<template>
  <div class="right-top">
    <a-button type="text" size="small" @click.stop.prevent='differenceItems("upper")' >上一条差异项</a-button>
    <a-button type="text" size="small" @click.stop.prevent='differenceItems("below")'>下一条差异项</a-button>
  </div>
  <div style="height:calc(100% - 34px);width: 70%;">
    <vxe-grid class='table-line' v-bind="gridOptionsTop" ref='gridRef' height="auto">
      <template #seqDefault="{ row,rowIndex,$rowIndex }">
        <icon-font v-if="row.explanation" type="icon-tishi-cheng"/>
        <icon-font v-else type="icon-duigou"/>
        <span style="margin-left: 5px">{{ $rowIndex + 1 }}</span>
      </template>
    </vxe-grid>
  </div>
</template>

<style scoped lang="scss">
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';
::v-deep(.color-orange) {
  color: #E39517;
}
::v-deep(.vxe-icon-checkbox-unchecked:before){
  color: #b9b9b9;
}
.table-content {
  height: 100%;
  //user-select: none;
  ::v-deep(.vxe-table .row-unit) {
    background: #e6dbeb;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #efe9f2;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #dce6fa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .row-qd .code-color) {
    color: #ce2929;
  }
  ::v-deep(.vxe-table .normal-info .code-color) {
    color: #ce2929;
    .vxe-tree-cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
  ::v-deep(.surely-table-header-cell):hover {
    .icon-close-s {
      opacity: 1;
    }
  }
}
</style>