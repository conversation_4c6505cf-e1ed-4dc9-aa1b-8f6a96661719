<!--
 * @Descripttion: 
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="props.visible"
    title="确认"
    :mask="true"
    :lockView="false"
    :lockScroll="false"
    width="550px"
    @cancel="close"
    @close="close"
  >
    <div class="tip">
      <span style="color: #ff9800">温馨提示：</span
      >项目中存在单位工程已被锁定清单，是否强制调整？
    </div>
    <vxe-table
      ref="vexTable"
      min-height="100"
      max-height="400"
      align="center"
      :column-config="{ resizable: true }"
      :data="tableData"
      :row-config="{
        isCurrent: true,
        keyField: 'uniqueStr',
      }"
      :scroll-y="{
        scrollToTopOnChange: true,
      }"
      show-overflow="title"
    >
      <vxe-column field="code" width="200" title="清单编码"> </vxe-column>
      <vxe-column field="unit" width="80" title="单位"> </vxe-column>
      <vxe-column field="remark" title="备注"> </vxe-column>
    </vxe-table>
    <div class="btn-list">
      <a-button type="primary" @click="confirm">确定</a-button>
      <a-button @click="close" style="margin-left: 10px">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
const props = defineProps({
  visible: Boolean,
  tableData: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['update:visible', 'confirm']);
let spinning = ref(false); // 全局增加loading状态
const close = () => {
  emits('update:visible', false);
};
const confirm = () => {
  emits('confirm');
  close();
};
watch(
  () => props.visible,
  async () => {
    if (props.visible) {
    }
  }
);
</script>
<style lang="scss" scoped>
.tip {
  font-size: 14px;
  margin-bottom: 20px;
}
.btn-list {
  margin-top: 20px;
  text-align: center;
}
</style>
