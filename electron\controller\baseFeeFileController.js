const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const ConstantUtil = require("../enum/ConstantUtil");

/**
 * 取费文件
 */
class BaseFeeFileController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取工程项目或者单位的计税方式数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async taxCalculation(arg){
        const result = await this.service.baseFeeFileService.taxCalculation(arg);
        return ResponseData.success(result);

    }

    /**
     * 设置主取费文件  只有单位可以设置
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async setMainFeeFile(arg,redo="取费表 切换主取费文件为【{newValue}】"){
        await this.service.baseFeeFileService.setMainFeeFile(arg);
        return ResponseData.success(true);

    }

    /**
     * 创建 工程项目 计税方式选择
     */
    async getDefaultTaxCalculation(){
        const result = await this.service.baseFeeFileService.getDefaultTaxCalculation();
        return ResponseData.success(result);
    }


    /**
     * 根据选择的单位或者工程项 和 取费文件id 获取费用总览和费率说明数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
     async feeCollectionData(arg){
        const result =  await this.service.baseFeeFileService.feeCollectionData(arg);
        return ResponseData.success(result);

    }



    /**
     * 获取工程项目 或者单位工程级别的政策文件数据
     * @param arg
     * @return {ResponseData}
     */
    async policyDocument(arg){
        const result =  await this.service.baseFeeFileService.policyDocument(arg);
        return ResponseData.success(result);

    }


    /**
     * 保存计税方式数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    saveTaxCalculation(arg){
        const result =  this.service.baseFeeFileService.saveTaxCalculation(arg);
        return ResponseData.success(result);

    }


    /**
     * 单次保存费用总览数据，保存单位工程级别的费用总览数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async saveCostOverview(arg,redo="取费表 费率总览 {columnTitle} 由【{oldValue}】修改为【{newValue}】"){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        const result =  this.service.baseFeeFileService.saveCostOverview(arg);
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            countCostCodeFlag: true,
        });
        return ResponseData.success(result);

    }



    /**
     * 单次保存单位的费率说明数据，保存单位工程级别的费率说明数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async saveFeeDescription(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        const result = await this.service.baseFeeFileService.saveFeeDescription(arg);
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            countCostCodeFlag: true,
        });
        return ResponseData.success(result);

    }


    /**
     * 保存选择的工程项目的政策文件
     * @param arg
     * @return
     */
    async checkPolicyDocument(arg){
        let checkPolicyDocument = await this.service.baseFeeFileService.checkPolicyDocument(arg);

        /*await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");*/

        return checkPolicyDocument;
    }





    /**
     * 统一应用，处理工程项目级别修改费率说明或者费率总览或者计税方式的税率后的统一应用
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async unifiedUse(arg){
        const result = await this.service.baseFeeFileService.unifiedUse(arg);
        /*await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");*/
        return ResponseData.success(result);

    }


    /**
     * 根据计税方式和纳税地区获取对应的税率
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async getRateByMethodAndLocation(arg){
        const result = await this.service.baseFeeFileService.getRateByMethodAndLocation(arg);
        return ResponseData.success(result);

    }



    /**
     * 获取所有的取费文件
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async queryFeeFileData(args){
        //切记!!!!
        //const result = await this.service.baseFeeFileService.queryFeeFileData(arg.deStandard.startsWith("22"));
        //前端加上参数后会 改回来
        const result = await this.service.baseFeeFileService.queryFeeFileData(args);
        return ResponseData.success(result);
    }

    /**
     * 获取所有的单价构成
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async queryFeeFileTempData(args) {
        const result = await this.service.baseFeeFileService.queryFeeFileTempData(args);
        return ResponseData.success(result);
    }


    /**
     * 查询全量施工组织措施类别数据
     * @return {Promise<*>}
     */
    async querySzType(arg){
        let unitIs2022= PricingFileFindUtils.is22De(arg.constructId);
        const result = await this.service.baseFeeFileRelationService.list(unitIs2022);
        return ResponseData.success(result);
    }


    /**
     * 根据费率说明查询费用总览的数据
     * @return {Promise<*>}
     */
    async getCostOverview(arg){
        const result = await this.service.baseFeeFileService.getCostOverview(arg);
        return ResponseData.success(result);
    }


    /**
     * 恢复默认费率
     */
    async restoreDefaultFee(arg){
        const result = await this.service.baseFeeFileService.restoreDefaultFee(arg);
        return ResponseData.success(result);
    }


//---------------------取费基数设置--------------------
    /**
     * 查询取费下拉框集合
     * @return {Promise<*>}
     */
    async queryCalculateBaseDropDownList(arg) {
        const {type,is22de} = arg
        const result = await this.service.baseFeeFileService.queryCalculateBaseDropDownList(type,is22de);
        return ResponseData.success(result);
    }

    /**
     * 查询工程项目、单位工程取费计取基数
     * @return {Promise<*>}
     */
    async queryProjectUnitCalculateBaseList(arg) {
        const {constructId, singleId, unitId} = arg
        const result = await this.service.baseFeeFileService.queryProjectUnitCalculateBaseList(constructId, singleId, unitId);
        return ResponseData.success(result);
    }

    /**
     * 设置工程项目、单位工程取费设置基数
     * @return {Promise<*>}
     */
    async updateProjectUnitCalculateBaseList(arg) {
        const {constructId, singleId, unitId, feeCalculateBaseList} = arg
        await this.service.baseFeeFileService.updateProjectUnitCalculateBaseList(constructId, singleId, unitId, feeCalculateBaseList);
        return ResponseData.success(true);
    }

    /**
     * 设置工程项目取费设置后统一应用
     * @return {Promise<*>}
     */
    async updateProjectUnitCalculateBaseApply(arg) {
        const {constructId,feeCalculateBaseList} = arg
        await this.service.baseFeeFileService.updateProjectUnitCalculateBaseApply(constructId,feeCalculateBaseList);
        return ResponseData.success(true);
    }

    /**
     * 修改工程项目级别的计税方式
     */
    async updateTaxCalculationMethod(args) {
        await this.service.baseFeeFileService.updateTaxCalculationMethod(args);
        return ResponseData.success(true);
    }


    /**
     * 获取22工程项目下是否有12的单位工程
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async get22ConstructUnit12de(args){
        let  result = await this.service.baseFeeFileService.get22ConstructUnit12de(args);

        return ResponseData.success(result);
    }

    /**
     * 获取刊物说明
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getPeriodicalData(){
        let  result = await this.service.baseFeeFileService.getPeriodicalData();

        return ResponseData.success(result);
    }


    /**
     * 获取政策文件
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getPolicyData(){
        let  result = await this.service.baseFeeFileService.getPolicyData();

        return ResponseData.success(result);
    }
}

BaseFeeFileController.toString = () => '[class BaseFeeFileController]';
module.exports = BaseFeeFileController;
