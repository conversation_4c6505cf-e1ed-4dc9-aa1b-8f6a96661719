<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2025-02-28 16:56:30
-->
<template>
  <div class="table-content">
    <p :class="store.currentTreeInfo.levelType<3?'selectTabNo2':'selectTab'">
      <a-radio-group
        v-model:value="activeKey"
        :style="{ marginBottom: '8px' }"
      >
        <a-radio-button
          :value="item.key"
          v-for="item of activeOptions"
        >{{ item.tab }}</a-radio-button>
      </a-radio-group>
    </p>
    <p
      class="searchTab"
      v-if="!activeKey"
    >
      <span class="label">
        材料名称包含
      </span>
      <a-input-search
        size="small"
        v-model:value="searchName"
        :maxlength="50"
        :placeholder="'请输入名称查询'"
        class="searchTab-input"
        @search="onSearch"
      />
      <span class="label">
        的材料
      </span>
      <a-button
        size="small"
        @click="startFun(1)"
      >重新过滤</a-button>
      <a-button
        size="small"
        @click="startFun(2)"
      >自动关联招标材料</a-button>
    </p>
    <div
      :class="activeKey===0?'content':'content addContentClass'"
      v-if="isOnline"
    >
      <vxe-grid
        class="trends-table-column"
        v-bind="gridOptions"
        ref="vexTable"
        height="100%"
        style="overflow-x: auto;margin-right: 4px"
        :loading="tableLoading"
        :scroll-x="{enabled:true}"
        :scroll-y="{enabled:true}"
      >
        <template #zg_default="{ column, row, $columnIndex }">
          <vxe-checkbox
            v-model="row.isSelect"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="CheckboxChange(row)"
          ></vxe-checkbox>
        </template>
        <template #ghfs_default="{ column, row, $columnIndex }">
          {{ getDonorMaterialText(row.ifDonorMaterial) }}
        </template>
        <template #unit_default="{ column, row, $columnIndex }">
          <span v-if="+row.edit !== 1 && !otherCodeList.includes(row.materialCode)">
            {{row.unit }}
          </span>
          <span v-else>
            {{otherCodeList.includes(row.materialCode) && row.unit==='%'?'元': row.unit }}
          </span>
        </template>

        <template #empty>
          <span style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          ">
            <img
              :src="getUrl('newCsProject/none.png')"
              style="margin: auto"
            />
          </span>
        </template>
      </vxe-grid>
    </div>
    <div
      class="content"
      v-if="!isOnline"
    >
      <img
        :src="getUrl('newCsProject/none.png')"
        style="margin: auto;
"
      />
    </div>
  </div>
  <common-modal
    className="dialog-comm"
    title="自动关联招标材料"
    :width="400"
    :height="200"
    v-model:modelValue="autoAsscciateModal"
    @cancel="cancel"
    @close="autoAsscciateFun(0)"
    :mask="true"
    :lockView="true"
  >
    <div class="autoModal">
      <p>
        材料下列属性和招标材料一致时会被自动关联
      </p>
      <a-checkbox-group
        v-model:value="checkedList"
        name="checkboxgroup"
        :options="checkList"
      />
      <p class="btn-list">
        <a-button
          type="primary"
          ghost
          @click="autoAsscciateFun(0)"
        >取消</a-button>
        <a-button
          type="primary"
          style="margin-left: 14px"
          @click="autoAsscciateFun(1)"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
  <common-modal
    className="dialog-comm"
    title="输入单价或者税率"
    :width="400"
    :height="250"
    v-model:modelValue="inputModal"
    @cancel="cancel"
    @close="inputFun(0)"
    :mask="true"
    :lockView="true"
  >
    <div class="inputModal">
      <p>
        您选择的多个人材机的单价不一致，为了保证能正确通过评标，需要把这几条人材机的单价修改为一致
      </p>
      <p class="inputRow">
        请输入关联后的单价：<a-input-number
          size="small"
          :max="1000"
          :min="0"
          v-model:value="inputVal.price"
        ></a-input-number>
      </p>
      <p class="inputRow">
        请输入关联后的税率：<a-input-number
          size="small"
          :max="1000"
          :min="0"
          v-model:value="inputVal.rate"
        ></a-input-number>
      </p>
      <p class="btn-list">
        <a-button
          type="primary"
          ghost
          @click="inputFun(0)"
        >取消</a-button>
        <a-button
          type="primary"
          style="margin-left: 14px"
          @click="inputFun(1)"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
</template>

<script setup>
import {
  defineAsyncComponent,
  ref,
  watch,
  onMounted,
  reactive,
  toRaw,
} from 'vue';
import feePro from '@/api/feePro';
import csProject from '@/api/csProject';
import loadPrice from '@/api/loadPrice';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber, getUrl } from '@/utils/index';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import getTableColumns, { otherCodeList } from './tableColumns';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
import { getShowFeild, getDonorMaterialText } from './tableColumns';
const emits = defineEmits(['refresh']);
const store = projectDetailStore();
let tableData = ref([]);
let activeKey = ref(0); //0-关联材料   1-关联明细
onMounted(async () => {
  activeOptions[1].tab = title[+store.asideMenuCurrentInfo?.key];
  searchName.value = activeKey.value ? null : props.showInfo?.materialName;
  getColumns();
  getGridData();
});
let checkedList = ref([1, 2, 3]);
let inputVal = reactive({
  price: null,
  rate: null,
});
const checkList = reactive([
  {
    label: '编码',
    value: 1,
  },
  {
    label: '名称',
    value: 2,
  },
  {
    label: '规格型号',
    value: 3,
  },
]);
const activeOptions = reactive([
  {
    key: 0,
    tab: '关联材料',
  },
  {
    key: 1,
    tab: '关联明细',
  },
]);
const props = defineProps(['showInfo']);
let isOnline = ref(true);
let oldName = null;
watch(
  () => [props.showInfo, activeKey.value],
  ([val, valY], [newVal, newValY]) => {
    if (valY !== newValY) {
      searchName.value = activeKey.value ? null : props.showInfo?.materialName;
      getColumns();
    }
    if (
      oldName !== newVal?.materialName ||
      val?.sequenceNbr !== newVal?.sequenceNbr
    ) {
      selectList = []; //切换选中数据行清空选择行
      searchName.value = activeKey.value ? null : props.showInfo?.materialName;
      oldName = newVal?.materialName;
    }
    getGridData();
  }
);
const title = {
  8: '暂估材料关联明细',
  9: '发包人供应材料和设备',
  10: '承包人材料关联明细',
};
watch(
  () => store.asideMenuCurrentInfo,
  () => {
    if ([8, 9, 10].includes(+store.asideMenuCurrentInfo?.key)) {
      gridOptions.data = [];
      activeOptions[1].tab = title[+store.asideMenuCurrentInfo?.key];
    }
  }
);

let tableLoading = ref(false);
const gridOptions = reactive({
  headerAlign: 'center',
  showOverflow: true,
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    height: 30,
  },
  columns: [],
  data: [],
  align: 'center',
});
let selectList = reactive([]); //选中数据行列表---刷新下表格是获取
let nowSelectRow = ref(); //当前选择的行
let inputValApiData = reactive(); //关联接口传参
const vexTable = ref();
let massageInfo = reactive(null);
const CheckboxChange = row => {
  let originChecked = initData.find(
    a => a.sequenceNbr === row.sequenceNbr
  )?.isSelect;
  const checked = originChecked === 0 ? 1 : 0;
  row.isSelect = checked;
  console.log('checked', checked, originChecked);
  if (+store.asideMenuCurrentInfo?.key === 10 && row?.ifLockStandardPrice) {
    row.isSelect = 0;
    //承包人-选中行市场价锁定的情况下要勾选的话需要去解锁
    infoMode.show({
      iconType: 'icon-querenshanchu',
      infoText:
        '当前材料的市场价已锁定，如果想要修改请先返回人材机汇总界面解除锁定！',
      isFunction: false,
      isSureModal: true,
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }
  if (
    checked &&
    [8, 10].includes(+store.asideMenuCurrentInfo?.key) &&
    !props.showInfo?.materialCode
  ) {
    message.error('编码为空不可进行关联');
    row.isSelect = 0;
    return;
  }
  let targetUnit =
    otherCodeList.includes(props.showInfo?.materialCode) &&
    props.showInfo?.unit === '%'
      ? '元'
      : props.showInfo?.unit;
  let unit =
    otherCodeList.includes(row?.materialCode) && row?.unit === '%'
      ? '元'
      : row?.unit;
  if (checked && targetUnit !== unit) {
    message.error('单位不一致不可进行关联');
    row.isSelect = 0;
    return;
  }
  let { scjField } = getShowFeild();
  selectList = gridOptions.data.filter(
    a => a.isSelect && a.sequenceNbr !== row.sequenceNbr
  );
  const data = initData.find(
    a => a.sequenceNbr === row.sequenceNbr
  )?.sequenceNbr;
  let apiData = getParamsData({
    // zgjRcj: getFinally(props.showInfo),
    addIdList: row.isSelect ? getFinally([data]) : null,
    deleteIdList: !row.isSelect ? getFinally([data]) : null,
  });
  inputValApiData = { ...apiData };
  if (
    +store.asideMenuCurrentInfo?.key === 10 &&
    checked &&
    selectList.length > 0 &&
    selectList.some(a => a[scjField] != row[scjField])
  ) {
    //承包人-单价不一致-弹框修改单价和税率
    row.isSelect = 0;
    nowSelectRow.value = row;
    inputModal.value = true;

    return;
  }
  massageInfo = row.isSelect === 0 ? '取消关联成功' : '关联成功';
  startApi(apiData, 'glApi');
};
const getFinally = data => {
  return JSON.parse(JSON.stringify(data));
};
const getColumns = async () => {
  let { scjField, scjName, hjfield, hjname } = getShowFeild();
  console.log('getColumns', scjField, scjName, hjfield, hjname);
  if (activeKey.value == 0) {
    gridOptions.columns = [
      { type: 'seq', title: '序号', width: 50 },
      {
        field: 'isSelect',
        width: 70,
        title: '选择',
        slots: { default: 'zg_default' },
      },
      { width: 100, title: '材料编码', field: 'materialCode' },
      // { field: 'type', title: '类别', width: 100 },
      { field: 'materialName', title: '名称', width: 100 },
      { field: 'specification', title: '规格型号', width: 120 },
      {
        field: 'unit',
        title: '单位',
        slots: { default: 'unit_default' },
        width: 70,
      },
      { field: 'totalNumber', title: '数量', width: 100 },
      { field: scjField, title: scjName, width: 100 },
      { field: hjfield, title: hjname, width: 120 },
      {
        field: 'ifDonorMaterial',
        title: '供货方式',
        width: 100,
        slots: { default: 'ghfs_default' },
      },
      { field: 'producer', title: '产地', width: 100 },
      { field: 'manufactor', title: '厂家', width: 100 },
      { field: 'market', title: '备注', width: 100 },
    ];
  } else {
    gridOptions.columns = [
      { type: 'seq', title: '序号', width: 70 },
      { width: 100, title: '材料编码', field: 'materialCode' },
      // { field: 'type', title: '类别', width: 100 },
      { field: 'materialName', title: '名称', width: 100 },
      { field: 'specification', title: '规格型号', width: 120 },
      {
        field: 'unit',
        title: '单位',
        slots: { default: 'unit_default' },
        width: 70,
      },
      { field: 'totalNumber', title: '数量', width: 100 },
      { field: scjField, title: scjName, width: 100 },
      { field: hjfield, title: hjname, width: 120 },
      {
        field: 'ifDonorMaterial',
        title: '供货方式',
        width: 100,
        slots: { default: 'ghfs_default' },
      },
      { field: 'producer', title: '产地', width: 100 },
      { field: 'manufactor', title: '厂家', width: 100 },
      { field: 'market', title: '备注', width: 100 },
    ];
  }
};
let autoAsscciateModal = ref(false);
let inputModal = ref(false);
const autoAsscciateFun = type => {
  console.log(checkedList.value);
  //0-取消 1-确定
  if (type) {
    let apiData = getParamsData({
      chosenAttributeList: getFinally(checkedList.value),
    });
    startApi(apiData, 'autoApi');
  } else {
    //重置选中项
    checkedList.value = [1, 2, 3];
  }
  autoAsscciateModal.value = false;
};
const inputFun = type => {
  //0-取消 1-确定
  if (!(typeof inputVal.price == 'number' && typeof inputVal.rate == 'number'))
    return;
  if (type) {
    // nowSelectRow.value.isSelect = 1;
    let apiData = {
      ...inputValApiData,
      marketPrice: inputVal.price,
      taxRate: inputVal.rate,
    };
    console.log('inputFun', inputVal, inputValApiData);
    startApi(apiData, 'glApi');
  }
  console.log('执行inputFun末尾');
  inputModal.value = false;
  //初始化inputVal
  inputVal.price = null;
  inputVal.rate = null;
};
const startFun = type => {
  //type-1:重新过滤   2-自动关联
  if (type === 2) {
    autoAsscciateModal.value = true;
    // inputModal.value = true;
  } else {
    console.log('重新过滤刷新数据');
    getGridData();
  }
};
let searchName = ref(null);
const onSearch = () => {
  console.log(searchName.value);
  getGridData();
};
//承包人-暂估一些api
const apiFunObj = {
  8: {
    apiKey: 'zgjRcj',
    queryApi: 'selectZgjRelevancyRcj',
    glApi: 'rcjToZgjRcj',
    autoApi: 'zgjAutoRelate',
  },
  10: {
    apiKey: 'cbrRcj',
    queryApi: 'selectCbrRelevancyRcj',
    glApi: 'rcjToCbrRcj',
    autoApi: 'cbrAutoRelate',
  },
};
//执行接口
const isSelectField = {
  8: 'ifProvisionalEstimate',
  10: 'hasAssociation',
};
let startApi = async (apiData, type) => {
  apiData[apiFunObj[+store.asideMenuCurrentInfo?.key]['apiKey']] = getFinally(
    props.showInfo
  );
  let apiName = apiFunObj[+store.asideMenuCurrentInfo?.key][type];
  console.log('startApi---res', apiData, apiName);
  await csProject[apiName](apiData)
    .then(res => {
      console.log('selectZgjRelevancyRcj---res', apiName, apiData, res);
      if (type === 'queryApi') {
        res.result &&
          res.result.map(
            a =>
              (a.isSelect = a[isSelectField[+store.asideMenuCurrentInfo?.key]]
                ? 1
                : 0)
          );
        initData = getFinally(res.result || []);
        gridOptions.data = res.result || [];
      } else {
        // message.success(massageInfo ? massageInfo : '操作成功');
        message.success('操作成功');
        emits('refresh');
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
let initData = reactive([]);
const getGridData = () => {
  let apiData = getParamsData({
    materialName: searchName.value,
  });
  startApi(apiData, 'queryApi');
};

const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
</script>
<style lang="scss" scoped>
.table-content {
  height: 100%;
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 0px;
    .text {
      display: inline-block;
      width: 100px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }
  .selectTab,
  .selectTabNo2 {
    background-color: #e7e7e7;
    height: 32px;
    line-height: 30px;
    // padding-left: 20px;
    position: relative;
    border-bottom: 2px solid #e7e7e7;
    margin: 3px 0;
    .label {
      color: grey;
      font-size: 12px;
    }
    .showTitle {
      position: absolute;
      right: 0px;
      top: 0px;
      line-height: 30px;
      height: 30px;
      padding: 0 20px;
      font-size: 12px;
      // background-color: #e7e7e7;
      border-radius: 5px;
    }
  }
  .selectTab {
    .ant-radio-button-wrapper {
      font-size: 12px;
      background-color: #e7e7e7;
      border: none;
      box-shadow: none;
      // border-radius: 5px;
    }
    .ant-radio-button-wrapper-checked {
      // border-color: none;
      background-color: white;
      border: none;
      border-top: 2px solid #4786ff;
      color: black;
      &:hover {
        color: black;
      }
    }
  }
  .selectTabNo2 {
    .ant-radio-button-wrapper {
      font-size: 12px;
      background-color: white;
      border: none;
      box-shadow: none;
      color: black;
    }
    .ant-radio-button-wrapper-checked {
      background-color: #40a9ff;
      border: none;
      color: white;
    }
  }
  .searchTab {
    height: 30px;
    line-height: 30px;
    display: flex;
    margin-bottom: 0px;
    .label {
      font-size: 12px;
      margin: 0 5px 0 5px;
    }
    .ant-btn {
      font-size: 12px !important;
      margin: auto 10px;
    }
    .ant-select,
    .ant-input,
    .ant-input-group {
      height: 25px !important;
      font-size: 12px !important;
      // color: gray;
      margin: auto 0;
    }
    :deep(.ant-input-group) {
      font-size: 12px !important;
      margin: auto 0;
    }
    &-input {
      width: 250px;
      margin: 2px 0 0 0px;
      height: 25px;
      :deep(.ant-input) {
        font-size: 12px;
        line-height: 22px;
      }
    }
  }

  .addContentClass {
    height: calc(100% - 40px);
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
}
:deep(.ant-tree) {
  height: calc(100% - 40px);
  overflow-y: scroll;
  .ant-tree-node-content-wrapper {
    padding: 0 0px;
  }
  .color_light {
    height: 24px;
    padding: 0;
    display: inline-block;
    background-color: #bae7ff;
  }
}
:deep(.noAllow .ant-input-search-button) {
  pointer-events: none;
  cursor: not-allowed;
  background-color: #e7e7e7;
}
.autoModal,
.inputModal {
  :deep(.ant-checkbox-group) {
    width: 100%;
    .ant-checkbox-group-item {
      font-size: 12px;
      margin-right: 5px;
    }
    .ant-checkbox + span {
      padding-right: 5px;
      padding-left: 5px;
    }
  }
  .btn-list {
    margin-top: 20px;
    float: right;
  }
}
.inputModal {
  .inputRow {
    text-indent: 10px;
    display: flex;
    // justify-content: space-between;
    .ant-input {
      width: 150px;
    }
  }
  .btn-list {
    margin-top: 10px;
  }
}
</style>
