:root {
  --vxe-table-row-height-mini: 30px !important;
  --project-detail-header-height: 56px;
  --project-detail-functional-area-height: 60px;
  --project-detail-main-content-tabs-menu-height: 41px;
  --project-detail-footer: 33px;
  --project-detail-table-font-size: 12px;
}
#pricing_body pre {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC,
    Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial,
    sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}
.vxe-header--column .vxe-cell {
  text-align: center;
}
.surely-table-header-cell-title span {
  text-align: center !important;
}
@media (max-width: 1366px) {
  :root {
    --vxe-table-row-height-mini: 28px !important;
  }
  #pricing_body .ant-layout-content {
    // transform: scale(0.92);
    // transform-origin: 0 0;
    // width: 109vw;
    // height: 87.5vh;
    // .ant-spin-container > section {
    //   height: calc(
    //     100vh - var(--project-detail-header-height) -
    //       var(--project-detail-functional-area-height) -
    //       var(--project-detail-footer)
    //   );
    // }
    // .report-form-section {
    //   height: calc(100vh - var(--project-detail-header-height));
    // }
    // .report-form-content {
    //   height: calc(
    //     100vh - var(--project-detail-header-height) -
    //       var(--project-detail-footer)
    //   );
    //   .main-content {
    //     height: calc(
    //       100vh - var(--project-detail-header-height) -
    //         var(--project-detail-footer) -
    //         var(--project-detail-main-content-tabs-menu-height)
    //     );
    //   }
    // }
    // .main-content {
    //   display: flex;
    //   height: calc(
    //     100vh - var(--project-detail-header-height) -
    //       var(--project-detail-functional-area-height) -
    //       var(--project-detail-footer) -
    //       var(--project-detail-main-content-tabs-menu-height)
    //   );
    // }
  }
  //
  .xl {
  }
}
