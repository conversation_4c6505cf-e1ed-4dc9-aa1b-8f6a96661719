/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-05 15:42:34
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-28 15:54:19
 */
import axios from 'axios';
import notification from 'ant-design-vue/es/notification';
import { VueAxios } from './axios';

// 创建 axios 实例
let isDev = false; //判断是否是dev环境
if (
  process.env.NODE_ENV === 'development' ||
  process.env.NODE_ENV === 'dev' ||
  process.env.NODE_ENV === 'local'
) {
  isDev = true;
}
const request = axios.create({
  // API 请求的默认前缀
  // baseURL: import.meta.env.VITE_APP_BASE_URL + '/api',
  // baseURL: isDev ? 'http://***********' : 'http://***********',
   baseURL: 'https://www.yunsuanfang.com', //生产环境
  // baseURL: 'http://qa.www.yunsuanfang.com:2081',// 测试环境
  timeout: 60000, // 请求超时时间
  // headers: { AGENCY_CODE: 'HZJT', PRODUCT_CODE: 'HZJT_YZJ_WEB' },
});

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data;
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message,
      });
    }
    if (
      error.response.status === 401 &&
      !(data.result && data.result.isLogin)
    ) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      });
    }
  }
  return Promise.reject(error);
};

// request interceptor
request.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token && !config.headers.Authorization) {
    config.headers['Authorization'] = token;
  }
  config.headers['Cache-Control'] = 'no-cache';
  config.headers['PRODUCT_CODE'] = 'PRICEING_CLIENT';
  config.headers['Pragma'] = 'no-cache';
  config.headers['Application_name'] = 'PORTAL';
  return config;
}, errorHandler);

// response interceptor
request.interceptors.response.use(response => {
  return response.data;
}, errorHandler);

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, request);
  },
};

export default request;

export { installer as VueAxios, request as axios };
