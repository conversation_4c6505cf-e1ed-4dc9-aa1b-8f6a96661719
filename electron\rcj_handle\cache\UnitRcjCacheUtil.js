const {ObjectUtils} = require("../../utils/ObjectUtils");
const {RcjDataUtils} = require("../RcjDataUtils");


class UnitRcjCacheUtil{
    add(unitProject, rcj, deId){
        this.addByCode(unitProject, rcj, deId);
    }

    /**
     * @param unitProject
     * @param rcj
     * @param deId
     * @param childrenRcjList
     */
    addByCode(unitProject, rcj, deId){
        //删除rcjId字段  避免子父级数据

        if(ObjectUtils.isEmpty(unitProject.rcjCache)){
            unitProject.rcjCache = {};
        }
        let cacheRcj = null;
        let six=RcjDataUtils.rcjKeyByFiveElementsAddCode(rcj);
        if(ObjectUtils.isEmpty(unitProject.rcjCache[six])){
            cacheRcj = ObjectUtils.cloneDeep(rcj);
            delete cacheRcj.rcjId;
            unitProject.rcjCache[six] = cacheRcj;
        }else{
            cacheRcj = unitProject.rcjCache[six];
        }

        if(ObjectUtils.isEmpty(cacheRcj.rcjDeNumberMap)){
            cacheRcj.rcjDeNumberMap = {};
        }


        let cnt = 1;
        if(ObjectUtils.isNotEmpty(cacheRcj.rcjDeNumberMap[deId])){
            let cntTmp = cacheRcj.rcjDeNumberMap[deId];
            cnt = cntTmp ? cntTmp + 1 : 1;
        }

        cacheRcj.rcjDeNumberMap[deId] = cnt;
    }

    /**
     * 获取子集人材机数据
     * @param unitProject
     * @param rcj
     * @returns {null|[]}
     */
    queryChildrenRcj(unitProject, rcj){
        let newVar = this.get(unitProject,rcj);
        if(ObjectUtils.isEmpty(newVar)){
           return  null;
        }
        let currentChildren=[];
        let rcjCodeList = newVar.childrenRcjCodeList;
        if(ObjectUtils.isNotEmpty(rcjCodeList)){
            for(const code of rcjCodeList){
                let rcjCacheElement = unitProject.rcjCache[code.materialCode];
                rcjCacheElement.initResQty = code.resQty;
                //rcjCacheElement.taxRateInit = code.taxRateInit;
                rcjCacheElement.resQty = code.resQty;
                currentChildren.push(rcjCacheElement);
            }
        }

        return  currentChildren;
    }

    /**
     * 通过一组子材料编码查找父材料
     * @param unitProject
     * @param childrenCodeList
     * @returns parentRcj
     */
    queryParentRcjByChildrenCodeList(unitProject, childrenCodeList){

        let parentRcj;
        //获取所有的缓存数据
        let rcjCache = unitProject.rcjCache;
        if(ObjectUtils.isNotEmpty(rcjCache)){
             for(const rcj of Object.values(rcjCache)){
                 let childrenRcjCodeList = rcj.childrenRcjCodeList;
                 if(ObjectUtils.isNotEmpty(childrenRcjCodeList)){
                     // 检查长度
                     if (childrenRcjCodeList.length !== childrenCodeList.length) {
                         continue;
                     }
                     //获取人材机中的所有编码
                     let codes = childrenRcjCodeList.map(c=>c.materialCode);
                     // 排序并比较
                     const sortedArr1 = codes.slice().sort();
                     const sortedArr2 = childrenCodeList.slice().sort();
                     if( sortedArr1.every((value, index) => value === sortedArr2[index])){
                         parentRcj=rcj;
                         break;
                     }
                 }
             }
        }
        return  parentRcj;
    }



    get(unitProject, rcj){
        let six=RcjDataUtils.rcjKeyByFiveElementsAddCode(rcj);
        return unitProject.rcjCache ? unitProject.rcjCache[six] : null;
    }

    /**
     * 通过编码获取缓存人材机数据
     * @param unitProject
     * @param materialCode
     * @returns {*}
     */
    getByCode(unitProject, materialCode){
        let tempAllRcjArray = ObjectUtils.isEmpty(unitProject.rcjCache)?[]:Object.values(unitProject.rcjCache);
        let find = tempAllRcjArray.find(k => k.materialCode == materialCode );
        return find
    }

    /**
     * 通过编码获取缓存人材机数据
     * @param unitProject
     * @param materialCode
     * @returns {*}
     */
    getByCodeAndLibraryCode(unitProject, materialCode,libraryCode){
        let tempAllRcjArray = ObjectUtils.isEmpty(unitProject.rcjCache)?[]:Object.values(unitProject.rcjCache);
        let find = tempAllRcjArray.find(k => {
            if(k.materialCode == materialCode && (ObjectUtils.isNotEmpty(k.libraryCode) && k.libraryCode==libraryCode)){
                     return true;
            }
        } );
        //if (find.kind ==1 || find.kind ==3){
            delete find?.formattedCoefficient
        //}

        return find
    }


    /**
     * 删除子集或者父级的时候需要解除关联关系
     * @param unitProject
     * @param materialCode
     * @param deId
     */
    delete(unitProject, rcjObj, deId){
        let rcj = this.get(unitProject,rcjObj);
        //避免查不到时导致程序异常
        if(ObjectUtils.isEmpty(rcj)){
            return;
        }
        let cnt = rcj.rcjDeNumberMap[deId];
        if(cnt > 1){
            rcj.rcjDeNumberMap[deId] = cnt - 1;
        }else{
            delete rcj.rcjDeNumberMap[deId];
        }

        //处理批注 缓存 问题
        if (ObjectUtils.isEmpty(rcj.rcjDeNumberMap)||Object.keys(rcj.rcjDeNumberMap).length == 0){
            delete rcj.constructPostil;
            delete rcj.constructPostilState;
            delete rcj.singlePostil;
            delete rcj.singlePostilState;
            delete rcj.unitPostil;
            delete rcj.unitPostilState;
        }

    }

    update(unitProject, rcjObj,upRcj){
        let six=RcjDataUtils.rcjKeyByFiveElementsAddCode(rcjObj);
        if(ObjectUtils.isEmpty(unitProject.rcjCache)){
            unitProject.rcjCache = {};
        }
        //删除rcjId字段  避免子父级数据
        let rcj =ObjectUtils.cloneDeep(upRcj);
        delete rcj.rcjId;
        delete rcj.changeResQtyCunsumerIds;
        delete rcj.formattedCoefficient;
        //同步应用次数
        let rcjCacheElement = unitProject.rcjCache[six];
        if(ObjectUtils.isNotEmpty(rcjCacheElement)){
            rcj.rcjDeNumberMap=rcjCacheElement.rcjDeNumberMap;
            rcj.childrenRcjCodeList=rcjCacheElement.childrenRcjCodeList;
            unitProject.rcjCache[six] = rcj;
        }

    }

    /**
     * 更新父级人材机的子集人材机编码集合
     * @param unitProject
     * @param materialCode
     * @param upRcj
     */
    updateParentRcjChilderCode(unitProject, rcjObj,upRcj){
        if(ObjectUtils.isEmpty(unitProject.rcjCache)){
            unitProject.rcjCache = {};
        }
        let six=RcjDataUtils.rcjKeyByFiveElementsAddCode(rcjObj);
        let rcjCacheElement = unitProject.rcjCache[six];
        rcjCacheElement.childrenRcjCodeList=upRcj.childrenRcjCodeList;

    }

    /**
     * 根据编码 判断是否只有一条数据
     */
    getRcjListByCodeOnly(unitProject, materialCode){
        let sameCodeRcjs=this.getByCode(unitProject,materialCode);
        if(ObjectUtils.isEmpty(sameCodeRcjs)){
            return false;
        }else {
            let values = Object.values(sameCodeRcjs.rcjDeNumberMap);
            if(ObjectUtils.isNotEmpty(values)) {
                if (values.length == 1) {
                    if(values[0]==1){
                        return true;
                    }
                }
            }
            return false;
        }
        // let prcj = unitProject.constructProjectRcjs.filter(rcj=>rcj.materialCode==materialCode);
        // let crcj = unitProject.rcjDetailList.filter(rcj=>rcj.materialCode==materialCode);
        // let num=0;
        // if(ObjectUtils.isNotEmpty(prcj)){
        //     num=num+prcj.length;
        // }
        // if(ObjectUtils.isNotEmpty(crcj)){
        //     num=num+crcj.length;
        // }
        // if(num==1){
        //    return  true;
        // }
        // return false;
    }

    /**
     * 获取五要素相同的人材机数据
     * @param code
     * @returns {string}
     */
    queryFiveElmentsSameRcjs(unitProject,rcj){
        let s = RcjDataUtils.rcjKeyByFiveElements(rcj);
        let tempAllRcjArray = ObjectUtils.isEmpty(unitProject.rcjCache)?[]:Object.values(unitProject.rcjCache);
        let sameCodeRcjs = tempAllRcjArray.filter(k => RcjDataUtils.rcjKeyByFiveElements(k) == s );
        return  sameCodeRcjs;
    }






    getOriginalCode(code) {
        let str = code;
        if (str.includes("#")) {
            str = str.substring(0, str.lastIndexOf("#"));
        }
        return str;
    }
}


module.exports = {
    UnitRcjCacheUtil: new UnitRcjCacheUtil()
};