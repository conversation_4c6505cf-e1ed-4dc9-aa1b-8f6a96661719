/**
 * @description: 表格拖拽填充单元格
 */
import { ref, reactive, nextTick, watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
export const useDragFillCell = ({
  currentCellData,
  processingData,
  humanTable,
}) => {
  //currentCellData.value 指原有点击单元格时，选中时存储的对象值
  const isDragFill = ref(false);
  const currentCellDragFill = ref({});
  const displayedFields = ref([
    'ifDonorMaterial',
    'ifProvisionalEstimate',
    'ifLockStandardPrice',
  ]); // 需要下拉的字段
  const projectStore = projectDetailStore();

  let dragFillInfo = reactive({
    column: '',
    columnIndex: 0,
    startRow: null,
    rowIds: [],
    rows: [],
    endRowIndex: 0,
    endRow: null,
  });
  /**
   * 重置拖拽填充信息
   */
  const resetDragFillInfo = () => {
    dragFillInfo.columnIndex = 0;
    dragFillInfo.column = '';
    dragFillInfo.startRow = '';
    dragFillInfo.startRowIndex = 0;
    dragFillInfo.rows = [];
    dragFillInfo.rowIds = [];
    dragFillInfo.endRowIndex = 0;
    dragFillInfo.endRow = null;
    // currentCellData.value = {}
  };

  const cellMouseenterEvent = event => {
    if (isDragFill.value) {
      dragFillInfo.endRowIndex = event.rowIndex;
      dragFillInfo.endRow = event.row;

      // 解决，角柄和鼠标移动跟踪问题
      // currentCellDragFill.value.columnIndex = event.$columnIndex;
      currentCellDragFill.value.row = event.row;

      if (!dragFillInfo.column) {
        dragFillInfo.columnIndex = event.$columnIndex;
        dragFillInfo.column = event.column;
      }
      // if (event.row.sequenceNbr === dragFillInfo.startRow.sequenceNbr) {
      //   dragFillInfo.rowIds = [];
      //   dragFillInfo.rows = [];
      //   return;
      // }
      const index = dragFillInfo.rowIds.findIndex(
        item => item === event.row.sequenceNbr
      );
      if (index >= 0) {
        dragFillInfo.rows.splice(index + 1, 1);
        dragFillInfo.rowIds.splice(index + 1, 1);
      } else {
        dragFillInfo.rowIds.push(event.row.sequenceNbr);
        dragFillInfo.rows.push({
          rowIndex: event.rowIndex,
          row: event.row,
        });
      }
    }
  };
  //鼠标按下事件
  const handleMouseDown = (
    event,
    row,
    column,
    $columnIndex,
    rowIndex,
    useDragCb
  ) => {
    if (event.button === 2) return;
    // 如果当前行是拖拽的结束行，则不重置状态
    if (row.sequenceNbr !== dragFillInfo.endRow?.sequenceNbr) {
      // 重置拖拽填充信息
      resetDragFillInfo();
      // 初始化拖拽填充信息
      Object.assign(dragFillInfo, {
        columnIndex: $columnIndex,
        column,
        startRow: row,
        startRowIndex: rowIndex,
        rows: [{ rowIndex: rowIndex, row }],
        rowIds: [row.sequenceNbr],
        endRowIndex: rowIndex,
        endRow: row,
      });
    }
    isDragFill.value = true;
    // currentCellData.value = {
    //   columnIndex: $columnIndex,
    //   rowId: row.sequenceNbr,
    //   clickNum: 1,
    // };

    // 添加鼠标抬起事件监听器
    document.body.addEventListener(
      'mouseup',
      event => {
        try {
          processingData(dragFillInfo); // 处理如果鼠标移出，在移入时，出现断连的情况
          handleMouseUp(event, useDragCb);
        } catch (error) {
          console.error('document.body.addEventListener(mouseup=>', error);
          document.body.removeEventListener('mouseup', handleMouseUp);
        } finally {
        }
      },
      { once: true }
    );
  };

  function handleMouseUp(event, useDragCb) {
    if (event.button === 2) return;
    document.body.removeEventListener('mouseup', handleMouseUp);
    isDragFill.value = false;
    // 解决，表格上面移动事件，移除表格外，角柄消失问题
    currentCellDragFill.value.columnIndex = dragFillInfo.columnIndex;
    currentCellDragFill.value.row = dragFillInfo.endRow;

    // 解决选中效果消失时，角柄没消失问题
    currentCellData.value = {};
    if (!dragFillInfo.rows.length) {
      currentCellDragFill.value = {};
    }
    useDragCb && useDragCb(dragFillInfo);
  }
  function isValueInRange(value, start, end) {
    const minVal = Math.min(start, end);
    const maxVal = Math.max(start, end);
    return value >= minVal && value <= maxVal;
  }
  const dragFillCellClassName = ({
    row,
    rowIndex,
    $rowIndex,
    column,
    columnIndex,
    $columnIndex,
  }) => {
    let className = '';
    //  && dragFillInfo.rowIds.includes(row.sequenceNbr)
    if (
      $columnIndex === dragFillInfo.columnIndex &&
      isValueInRange(
        rowIndex,
        dragFillInfo.startRowIndex,
        dragFillInfo.endRowIndex
      )
    ) {
      const { startRowIndex, endRowIndex, rowIds } = dragFillInfo;
      const index = rowIds.length;
      const currentSequence = row.sequenceNbr;
      // 基础样式
      className += ' drag-fill-cell';
      // 方向判断逻辑
      const isUpward = startRowIndex > rowIndex;
      const isDownward = startRowIndex < rowIndex;

      if (isUpward) {
        // 向上拖动：最后一个元素是起始点，第一个元素是终点
        if (currentSequence === rowIds[index - 1]) {
          className += ' drag-fill-cell-first';
        }
        if (currentSequence === rowIds[0]) {
          className += ' drag-fill-cell-last';
        }
      } else if (isDownward) {
        // 向下拖动：第一个元素是起始点，最后一个元素是终点
        if (currentSequence === rowIds[0]) {
          className += ' drag-fill-cell-first';
        }
        if (currentSequence === rowIds[index - 1]) {
          className += ' drag-fill-cell-last';
        }
      } else {
        // 同行处理：根据最终拖动方向决定样式
        const isFinalDownward = endRowIndex > startRowIndex;
        const isFinalUpward = endRowIndex < startRowIndex;
        if (isFinalDownward) {
          className += ' drag-fill-cell-first';
        } else if (isFinalUpward) {
          className += ' drag-fill-cell-last';
        } else {
          className += ' drag-fill-cell-last drag-fill-cell-first';
        }
      }
    }
    return className;
  };
  const handLabelDisplay = ({ $columnIndex, column, row: row1 }) => {
    const { row, columnIndex: cIndex } = currentCellDragFill.value;
    return (
      displayedFields.value.includes(column.field) &&
      row &&
      'sequenceNbr' in row &&
      row.sequenceNbr === row1.sequenceNbr &&
      $columnIndex === cIndex
    );
  };
  watch(
    () => projectStore.currentTreeInfo,
    (New, Old) => {
      resetDragFillInfo();
      currentCellDragFill.value = {};
    },
    {
      deep: true,
    }
  );

  return {
    isDragFill,
    dragFillInfo,
    cellMouseenterEvent,
    handleMouseDown,
    dragFillCellClassName,
    resetDragFillInfo,
    currentCellDragFill,
    displayedFields,
    handLabelDisplay,
  };
};
