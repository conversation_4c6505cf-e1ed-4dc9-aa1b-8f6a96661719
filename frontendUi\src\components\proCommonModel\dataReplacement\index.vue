<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2024-04-17 14:27:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-09 14:20:49
-->
<template>
  <common-modal
    className="dialog-comm qdQuickPricing-dialog"
    @close="cancel"
    v-model:modelValue="show"
    :title="`替换${acCurrentInfo.kind === '03' ? '清单' : '定额'}`"
    width="80vw"
    height="80vh"
    min-width="960px"
    min-height="410px"
    :mask="false"
    show-zoom
    resize
    :lock-view="false"
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div
      class="content-wrap"
      :style="{
        gridTemplateRows:
          acCurrentInfo.kind !== '03'
            ? 'auto auto auto 8fr 1fr'
            : 'auto auto auto auto 8fr 1fr',
      }"
    >
      <div class="content-box content-box-top">
        <div class="info-box">
          <div>
            <label style="font-weight: bold"
              >当前{{ acCurrentInfo.kind === '03' ? '清单' : '定额' }}：</label
            >
          </div>
          <div><label>名称：</label>{{ acCurrentInfo.name }}</div>
          <div><label>编码：</label>{{ acCurrentInfo.bdCode }}</div>
          <div><label>单位：</label>{{ acCurrentInfo.unit }}</div>
          <div><label>综合单价：</label>{{ acCurrentInfo.price }}</div>
        </div>
      </div>
      <div class="condition-wrap">
        <div class="left-box">
          <div class="list">
            <div class="items">
              <span>范围：</span>
              <a-select
                ref="select"
                size="small"
                v-model:value="filterList.unitOrProject"
              >
                <a-select-option value="unit">当前单位工程</a-select-option>
                <a-select-option value="project">工程项目</a-select-option>
              </a-select>
            </div>
            <div class="items">
              <span>显示选项：</span>
              <a-select
                ref="select"
                style="width: 80px"
                size="small"
                v-model:value="filterList.showOp"
                @change="showOpChange"
              >
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="1">已选择</a-select-option>
                <a-select-option :value="2">未选择</a-select-option>
              </a-select>
            </div>
            <div class="items" v-if="acCurrentInfo.kind === '03'">
              <span>过滤条件：</span>
              <a-select
                ref="select"
                size="small"
                v-model:value="filterList.BitQdCode"
              >
                <a-select-option :value="0">无</a-select-option>
                <a-select-option :value="9">9位</a-select-option>
                <a-select-option :value="12">12位</a-select-option>
              </a-select>
            </div>
          </div>
        </div>
        <div class="right-box-wrap">
          <div class="items" v-if="acCurrentInfo.kind === '03'">
            <a-input
              v-model:value="filterList.searchKey"
              :placeholder="`${
                acCurrentInfo.kind === '03'
                  ? '输入被替换清单名称或项目特征过滤结果'
                  : '输入被替换定额名称'
              }`"
            />
          </div>
          <template v-else>
            <div class="items">
              <span>编码：</span>
              <a-input
                v-model:value="filterList.deCode"
                placeholder="输入被替换定额编码"
              />
            </div>
            <div class="items">
              <span>名称：</span>
              <a-input
                v-model:value="filterList.searchKey"
                placeholder="输入被替换定额名称"
              />
            </div>
          </template>
        </div>
      </div>
      <div class="condition-wrapnoline" v-if="acCurrentInfo.kind === '03'">
        <div class="left-box">
          <span class="title">根据选项查询清单：</span>
          <div class="list">
            <div class="items">
              <a-checkbox v-model:checked="filterList.isQdName"
                >名称</a-checkbox
              >
            </div>
            <div class="items">
              <a-checkbox
                style="margin-top: 2px"
                v-model:checked="filterList.profeature"
                >项目特征
                <a-tooltip>
                  <template #title>
                    <!-- v-html="projectAttrtooltip" -->
                    <data>模糊：过滤换行、特殊符号</data>
                  </template>
                  <icon-font type="icon-bangzhu"></icon-font>
                </a-tooltip>
              </a-checkbox>
              <a-select
                v-if="filterList.profeature"
                style="margin-top: 2px"
                ref="select"
                size="small"
                v-model:value="filterList.isProjectAttr"
              >
                <a-select-option value="similar">模糊</a-select-option>
                <a-select-option value="precise">精准</a-select-option>
              </a-select>
            </div>
            <div class="items">
              <a-checkbox
                style="margin-top: 2px"
                v-model:checked="filterList.isMerge"
                >未组价清单
              </a-checkbox>
            </div>
          </div>
        </div>
      </div>
      <div class="condition-wrap">
        <div class="left-box">
          <div class="list2">
            <div class="items">
              <a-checkbox
                v-model:checked="filterList.allcheck"
                @change="allcheck"
                >全选</a-checkbox
              >
            </div>
            <div class="items" v-if="acCurrentInfo.kind === '03'">
              <a-checkbox
                v-model:checked="filterList.showChild"
                @change="showChild"
                >显示子目</a-checkbox
              >
            </div>
            <div class="items refresh">
              <a-button @click="getTableList" size="small">
                <icon-font type="icon-shuaxin"></icon-font>刷新</a-button
              >
            </div>
          </div>
        </div>
      </div>
      <div class="content content-table custom-vxe-table-tree">
        <vxe-table
          border
          ref="vexTable"
          align="center"
          :column-config="{ resizable: true }"
          :data="tableData"
          :checkStrictly="false"
          :row-class-name="vxeRowClassName"
          height="100%"
          :row-config="{
            isCurrent: true,
            keyField: 'sequenceNbr',
          }"
          :checkbox-config="{
            showHeader: false,
            visibleMethod: ({ row }) => {
              return [acCurrentInfo.kind].includes(row.kind);
            },
            checkMethod: ({ row }) => {
              if (row.sequenceNbr === acCurrentInfo.sequenceNbr) {
                return false;
              }
              return true;
            },
          }"
          :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
          :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            line: true,
            showIcon: true,
            expandAll: true,
            indent: 15,
            iconOpen: 'vxe-icon-caret-down',
            iconClose: 'vxe-icon-caret-right',
          }"
          show-overflow="title"
          @cell-click="cellClick"
          @cell-dblclick="dbClick"
        >
          <vxe-column
            align="center"
            type="checkbox"
            width="40"
            title=""
          ></vxe-column>
          <vxe-column type="seq" width="80" title="序号">
            <template #default="{ row }">
              <span> {{ row.rowIndex }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="bdCode"
            tree-node
            width="250"
            title="编码"
            align="left"
          >
            <template #default="{ row }">
              <span>
                <icon-font
                  v-if="row.isLocked"
                  type="icon-qingdan-suoding"
                ></icon-font
                >&nbsp;{{ row.bdCode }}</span
              >
            </template>
          </vxe-column>
          <vxe-column field="quotaName" width="250" title="名称">
            <template #default="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </vxe-column>
          <vxe-column field="projectAttr" width="250" title="项目特征">
          </vxe-column>
          <vxe-column field="unit" title="单位"> </vxe-column>
          <vxe-column field="quantity" title="工程量"> </vxe-column>
          <vxe-column field="price" title="综合单价"> </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-box">
        <div class="footer-history">
          <p>
            <icon-font
              type="icon-querenshanchu"
              style="font-size: 14px"
            ></icon-font
            >&nbsp;双击定位到清单
          </p>
        </div>
        <div class="footer-handle">
          <template v-if="acCurrentInfo.kind === '03'">
            <a-checkbox v-model:checked="filterList.isqd">清单</a-checkbox>
            <a-checkbox v-model:checked="filterList.iszj">组价</a-checkbox>
          </template>
          &emsp; &emsp;
          <a-button
            size="small"
            :disabled="!filterList.isqd && !filterList.iszj"
            type="primary"
            @click="replaceData"
            >替换数据</a-button
          >
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script>
export default {
  name: 'dataReplacement',
};
</script>
<script setup>
import { ref, watch, inject, computed, reactive, watchEffect } from 'vue';
import operateList from '@/views/projectDetail/customize/operate.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { VXETable } from 'vxe-table';
import infoMode from '@/plugins/infoMode.js';
import { rowClassName } from '@/views/projectDetail/customize/subItemProject/classAndStyleMethod.js';

const { linkagePosition } = useReversePosition();
const store = projectDetailStore();
const vexTable = ref(null);
let loading = ref(false);
let acCurrentInfo = ref({});
let cloneData = ref();
const { currentInfo, componentId } = inject('mainData');
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
let dataReplacement = operateList.value.find(
  item => item.name === 'dataReplacement'
);
let tableData = ref([]);

let filterList = reactive({
  unitOrProject: 'project',
  BitQdCode: 9,
  isQdName: false,
  isProjectAttr: 'similar',
  profeature: false,
  searchKey: '',
  deCode: '',
  isMerge: true,
  projectAttr: true,
  selcetAll: false,
  showChild: true,
  showOp: 0,
  isqd: true,
  iszj: false,
});
watch(
  () => filterList,
  val => {
    console.log('val', val);
  },
  {
    deep: true,
  }
);
const emit = defineEmits(['update:visible']);
const show = computed({
  get: () => {
    console.log(props.visible);
    return props.visible;
  },
  set: val => {
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      currentInfo.value.moduleType =
        store.tabSelectName === '分部分项' ? 'fbfx' : 'csxm';
      acCurrentInfo.value = JSON.parse(JSON.stringify(currentInfo.value));
      init();
    }
  }
);
watch(
  () => currentInfo.value,
  val => {
    if (
      (val?.kind === '03' || val?.kind === '04') &&
      val?.type !== '费' &&
      (val?.bdCode || val?.fxCode) &&
      ((store.tabSelectName === '措施项目' && val.replaceFlag) ||
        store.tabSelectName === '分部分项')
    ) {
      dataReplacement.disabled = false;
    } else {
      dataReplacement.disabled = true;
    }
    if (val?.sequenceNbr === acCurrentInfo.value.sequenceNbr) {
      dataReplacement.disabled = false;
    }
  }
);
const vxeRowClassName = ({ row }) => {
  const name = rowClassName(row, '', tableData.value);
  if (row.sequenceNbr === acCurrentInfo.value.sequenceNbr) {
    return 'reQD ' + name;
  }
  if (row.replace) {
    return 'replace ' + name;
  }
  return name;
};
const allcheck = ({ target }) => {
  let checkData = cloneData.value.filter(
    a => a.kind === '03' && a.sequenceNbr !== acCurrentInfo.value.sequenceNbr
  );
  checkData.map(a => {
    vexTable.value.setCheckboxRow(a, target.checked);
  });
};
const showChild = ({ target }) => {
  if (target.checked) {
    tableData.value = cloneData.value;
  } else {
    tableData.value = cloneData.value?.filter(a => a.kind !== '04');
    tableData.value = tableData.value.map((item, index) => {
      return { ...item, ...{ rowIndex: index + 1 } };
    });
    vexTable.value.reloadData(tableData.value);
  }
};
const dbClick = ({ row }) => {
  if (row.unitId && row.moduleType) {
    linkagePosition({
      treeId: row.unitId,
      tabMenuName: row.moduleType === 'fbfx' ? '分部分项' : '措施项目',
      rowId: row.sequenceNbr,
    });
  }
};
const showOpChange = row => {
  console.log(row);
  if (row === 0) tableData.value = cloneData.value;
  if (row === 1) {
    tableData.value = [
      ...cloneData.value.filter(a => a.kind !== '04' && a.kind !== '03'),
      ...vexTable.value.getCheckboxRecords(),
    ].map((item, index) => {
      return { ...item, ...{ rowIndex: index + 1 } };
    });
  }
  if (row === 2) {
    tableData.value = differenceBy(
      cloneData.value,
      vexTable.value.getCheckboxRecords(),
      'sequenceNbr'
    ).map((item, index) => {
      return { ...item, ...{ rowIndex: index + 1 } };
    });
  }
  vexTable.value.reloadData(tableData.value);
  vexTable.value.setAllRowExpand(true);
};

function differenceBy(arr1, arr2, key) {
  const set2 = new Set(arr2.map(item => item[key]));
  return arr1.filter(item => !set2.has(item[key]));
}
const getTableList = async () => {
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    fbfxOrCsxm: componentId.value === 'subItemProject' ? 'fbfx' : 'csxm',
    unitOrProject: filterList.unitOrProject, //范围是当前工程还是整个工程项目  “unit”/"project"
    searchKey: filterList.searchKey, //输入清单相关的关键字
  };
  console.log(acCurrentInfo.value);
  if (acCurrentInfo.value.kind === '03') {
    let data = {
      qdId: acCurrentInfo.value.sequenceNbr,
      is12BitQdCode: filterList.BitQdCode === 12, //不选置false  选择后置true  是否是12位编码
      is9BitQdCode: filterList.BitQdCode === 9, //不选置false  选择后置true  是否是9位编码
      isQdName: filterList.isQdName, //不选置false
      isProjectAttr: filterList.profeature ? filterList.isProjectAttr : null, //不选为null  值为"precise" 表示精准  "similar" 表示近似
      isMerge: !filterList.isMerge, //清单 是否未组价  true 表示所有清单  false表示未组价
    };
    console.log({ ...postData, ...data });
    csProject.matchQueryByQdSelected({ ...postData, ...data }).then(res => {
      console.log('获取数据', res);
      tableData.value = res.result.map((item, index) => {
        return { ...item, ...{ rowIndex: index + 1 } };
      });

      console.log(tableData.value);
      vexTable.value.reloadData(tableData.value);
      cloneData.value = xeUtils.clone(tableData.value, true);
      showChild({ target: { checked: filterList.showChild } });
    });
  }
  if (acCurrentInfo.value.kind === '04') {
    console.log('postData', {
      ...postData,
      ...{ deId: acCurrentInfo.value.sequenceNbr, deCode: filterList.deCode },
    });
    csProject
      .matchQueryByDeSelected({
        ...postData,
        ...{ deId: acCurrentInfo.value.sequenceNbr, deCode: filterList.deCode },
      })
      .then(res => {
        console.log('获取数据', res);
        tableData.value = res.result.map((item, index) => {
          return { ...item, ...{ rowIndex: index + 1 } };
        });
        vexTable.value.reloadData(tableData.value);
        cloneData.value = xeUtils.clone(tableData.value, true);
        console.log(tableData.value);
      });
  }
};
const replaceData = () => {
  console.log(vexTable.value.getCheckboxRecords().filter(a => a.kind === '03'));
  let qdData = vexTable.value.getCheckboxRecords().filter(a => a.kind === '03');
  let deData = vexTable.value.getCheckboxRecords().filter(a => a.kind === '04');
  if (vexTable.value.getCheckboxRecords().length === 0) {
    showInfo('icon-querenshanchu', '请选择要替换的数据');
    return false;
  }
  if (
    acCurrentInfo.value.kind === '03' &&
    qdData.filter(a => a.isLocked === 1).length === qdData.length
  ) {
    showInfo('icon-qiangtixing', '清单已锁定不能替换清单');
    return false;
  }
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    fbfxOrCsxm: componentId.value === 'subItemProject' ? 'fbfx' : 'csxm',
  };
  if (acCurrentInfo.value.kind === '03') {
    postData.qdId = acCurrentInfo.value.sequenceNbr;
    postData.qdList = qdData;
    if (filterList.isqd && !filterList.iszj) {
      postData.replaceStyle = 'qd';
    }
    if (filterList.iszj && !filterList.isqd) {
      postData.replaceStyle = 'scheme';
    }
    if (filterList.isqd && filterList.iszj) {
      postData.replaceStyle = 'all';
    }

    setTableStyle(qdData);
    csProject.replaceQdData(JSON.parse(JSON.stringify(postData))).then(res => {
      console.log('获取数据', res);
      refreshData();
      if (
        qdData.filter(a => a.isLocked === 1).length > 0 &&
        qdData.filter(a => a.isLocked === 1).length < qdData.length
      ) {
        showInfo('icon-qiangtixing', '部分清单已锁定，未锁定的清单已被替换!');
      } else {
        showInfo('icon-ruotixing', '替换清单完成');
      }
    });
  }
  if (acCurrentInfo.value.kind === '04') {
    postData.deId = acCurrentInfo.value.sequenceNbr;
    postData.deList = deData.map(a => {
      return { ...a, ...{ replace: true } };
    });
    console.log(postData, 'postData');
    setTableStyle(deData);
    csProject.replaceDeData(JSON.parse(JSON.stringify(postData))).then(res => {
      console.log('获取数据', res);
      showInfo('icon-ruotixing', '替换定额完成');
      refreshData();
    });
  }
  dataReplacement.disabled = true;
};
const refreshData = () => {
  let data = cloneData.value.find(
    a => a.sequenceNbr === acCurrentInfo.value.sequenceNbr
  );
  console.log({
    treeId: acCurrentInfo.value.unitId,
    tabMenuName:
      acCurrentInfo.value.moduleType === 'fbfx' ? '分部分项' : '措施项目',
    rowId: acCurrentInfo.value.sequenceNbr,
  });
  linkagePosition({
    treeId: acCurrentInfo.value.unitId,
    tabMenuName:
      acCurrentInfo.value.moduleType === 'fbfx' ? '分部分项' : '措施项目',
    rowId: acCurrentInfo.value.sequenceNbr,
  });
};
const setTableStyle = data => {
  data.map(a => {
    tableData.value.map(b => {
      if (a.sequenceNbr === b.sequenceNbr && a.isLocked !== 1) {
        b.replace = true;
      }
    });
  });
  vexTable.value.reloadData(tableData.value);
  data.map(a => {
    vexTable.value.setCheckboxRow(a, true);
  });
};
const showInfo = (iconType, infoText) => {
  infoMode.show({
    iconType,
    infoText,
    isSureModal:
      iconType === 'icon-ruotixing' || iconType === 'icon-querenshanchu',
    confirm: () => {
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const init = () => {
  filterList = reactive({
    unitOrProject: 'project',
    BitQdCode: 9,
    isQdName: false,
    isProjectAttr: 'similar',
    profeature: false,
    searchKey: '',
    deCode: '',
    isMerge: true,
    projectAttr: true,
    selcetAll: false,
    showChild: true,
    showOp: 0,
    isqd: true,
    iszj: false,
  });
  getTableList();
};
const cancel = () => {
  show.value = false;
};
</script>
<style lang="scss" scoped>
.qdQuickPricing-dialog .content-wrap {
  display: flex;
  // grid-template-rows: auto auto auto auto 8fr 1fr;
  height: 100%;
}
.condition-wrap,
.condition-wrapnoline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  font-size: 14px;
  margin-top: 12px;
  border-top: 1px solid rgba(185, 185, 185, 1);
  .left-box {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  .items {
    display: flex;
    margin-right: 15px;
    align-items: center;
  }
  :deep(.refresh) {
    margin-left: auto;
    .ant-btn {
      border: 1px solid #287cfa;
      color: #287cfa;
    }
  }
  .list,
  .list2 {
    display: flex;
    align-items: center;
  }
  .list2 {
    width: 100%;
  }
  .right-box-wrap {
    display: flex;
    .items {
      .ant-input {
        width: 150px;
      }
    }
  }
}
.condition-wrapnoline {
  padding: 0px 0 0px;
  margin-top: 0px;
  align-items: center;
  border-top: none;
}
:deep(.content-table) {
  flex: 1;
  height: calc((100% - 130px) * 0.7);
  .vxe-table {
    // .vxe-tree--line {
    //   /* 修改连接线的颜色 */
    //   border-left: 1px solid #87b2f2;
    //   border-bottom: 1px solid #87b2f2;
    // }
    .replace {
      color: #de3f3f !important;
    }
    .reQD {
      background: #dbdb2940;
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }
}
.footer-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 17px;
  .footer-history {
    max-width: 50%;
    p {
      font-weight: 400;
      font-size: 12px;
      color: #2a2a2a;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }
  .footer-handle {
    display: flex;
    align-items: center;
    .rang-tips {
      margin-right: 10px;
      font-size: 14px;
    }
  }
}
</style>
