export default [
    'update:data',
    'keydown-start',
    'keydown',
    'keydown-end',
    'paste',
    'copy',
    'cut',
    'current-change',
    'radio-change',
    'checkbox-change',
    'checkbox-all',
    'checkbox-range-start',
    'checkbox-range-change',
    'checkbox-range-end',
    'checkbox-range-select',
    'cell-click',
    'cell-dblclick',
    'cell-menu',
    'cell-mouseenter',
    'cell-mouseleave',
    'cell-selected',
    'header-cell-click',
    'header-cell-dblclick',
    'header-cell-menu',
    'footer-cell-click',
    'footer-cell-dblclick',
    'footer-cell-menu',
    'clear-merge',
    'sort-change',
    'clear-sort',
    'filter-change',
    'filter-visible',
    'clear-filter',
    'resizable-change',
    'toggle-row-expand',
    'toggle-tree-expand',
    'menu-click',
    'edit-closed',
    'edit-actived',
    'edit-activated',
    'edit-disabled',
    'valid-error',
    'scroll',
    'custom',
    'change-fnr',
    'open-fnr',
    'show-fnr',
    'hide-fnr',
    'fnr-change',
    'fnr-find',
    'fnr-find-all',
    'fnr-replace',
    'fnr-replace-all',
    'cell-area-copy',
    'cell-area-cut',
    'cell-area-paste',
    'cell-area-merge',
    'clear-cell-area-merge',
    'header-cell-area-selection',
    'cell-area-selection-invalid',
    'cell-area-selection-start',
    'cell-area-selection-drag',
    'cell-area-selection-end',
    'cell-area-extension-start',
    'cell-area-extension-drag',
    'cell-area-extension-end',
    'cell-area-selection-all-start',
    'cell-area-selection-all-end',
    'cell-area-arrows-start',
    'cell-area-arrows-end',
    'active-cell-change-start',
    'active-cell-change-end'
];
