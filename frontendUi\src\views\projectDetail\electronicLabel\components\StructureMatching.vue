<script setup>
import {ref,reactive,defineEmits,toRef, toRaw } from 'vue';
const props =  defineProps(['matchState']);
const emit = defineEmits(['buttonHandle']);
import api from '@/api/electronicLabel.js';
import { message } from "ant-design-vue";
import { electronicLabel } from '@/views/projectDetail/electronicLabel/store/electronicLabelStore';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const electronicLabelStore = electronicLabel();
const loading = ref(false);
const dataSource = ref({
  tb:[],
  zb:[]
});
const importUrl = ref();
const vxeTableRefTb = ref();
const vxeTableRefZb = ref();
const doesItMatch = async (type)=>{

  console.log('doesItMatch',type);
  const apiName = { '1':'match', '2':'cancelMatch' };
  const apiData = {};
  //let {tbConstructId,zbConstructId,tbSpId,zbSpId,tbUnitId,zbUnitId}
  // 招标
  apiData.zbConstructId = dataSource.value.zb.find(item=>item.levelType == 1)?.id;
  if(selectRow.value.zb.levelType === 2){
    apiData.zbSpId = selectRow.value.zb.id;
  }else if(selectRow.value.zb.levelType === 3){
    apiData.zbUnitId = selectRow.value.zb.id;
  }
  // 投标
  apiData.tbConstructId = dataSource.value.tb.find(item=>item.levelType == 1)?.id;
  if(selectRow.value.zb.levelType === 2){
    apiData.tbSpId = selectRow.value.tb.id;
  }else if(selectRow.value.zb.levelType === 3){
    apiData.tbUnitId = selectRow.value.tb.id;
  }
  try {
    loading.value = true;
    console.log(`api-${apiName[type]}参数`,apiData);
    const res = await api[apiName[type]](apiData);
    console.log(`api-${apiName[type]}返回值`,res);
    if(res.status === 200){
      vxeTableRefZb.value.reloadData(res.result.zb);//解决树不展开bug
      vxeTableRefTb.value.reloadData(res.result.tb);//解决树不展开bug
      selectRow.value.zb = res.result.zb.find(item=>item.id === selectRow.value.zb.id);// 解决 选择匹配与不匹配时，招标数据会出现 ‘匹配投标工程名称’ 变化，导致没有选中
      type === '2' && (selectRow.value.tb = {});// 去除 取消匹配时，投标文件还被选中的效果
    }else{
      message.error(res.message);
    }
  }catch (e) {
    // dataSource.value.tb = [];
    // dataSource.value.zb = [];
    console.error('api.uploadUpdate=>catch',e);
    message.error(res.message);
  }finally {
    loading.value = false;
  }

}

const selectDocument = async ()=>{
  const apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  try {
    loading.value = true;
    console.log('api.uploadUpdat参数',apiData);
    const res = await api.uploadUpdate(apiData);
    console.log('api.uploadUpdat返回值',res);
    if(res.status === 200){
      dataSource.value.tb = res.result.tb;
      dataSource.value.zb = res.result.zb;
      electronicLabelStore.SET_PPJG({tbId:res.result.tb[0].id, zbId:res.result.zb[0].id});
      importUrl.value = res.result.importUrl;
      vxeTableRefZb.value.reloadData(res.result.zb);//解决树不展开bug
      vxeTableRefTb.value.reloadData(res.result.tb);//解决树不展开bug
      selectRow.value.tb = {};
      emit('buttonHandle',{page:'ppsj',flag:false});
    }else{
      message.error(res.message);
    }
  }catch (e) {
    dataSource.value.tb = [];
    dataSource.value.zb = [];
    console.error('api.uploadUpdate=>catch',e);
  }finally {
    loading.value = false;
  }
}

// 选中效果逻辑
const selectRow = ref({
  zb: {},
  tb:''
});
const selectColumn = ref({
  zb: {},
  tb:''
});
const doesItMatchDis = ref({
  match: true,
  cancelMatch: true
});

const cellClickEventZb = ({ row, column }) => {
  //新招标工程
  selectRow.value.zb = row;
  selectRow.value.tb = dataSource.value.tb.find(item => item.id === row.matchId) || {};
  doesItMatchDisHandle();
}
const cellClassNameZb = ({ row, column }) => {
  if (row === selectRow.value.zb && column.field === 'name') {
    return 'col-active'
  }
  return null
}
const cellClassNameTb = ({ row, column }) => {
  const isNameColumn = column.field === 'name';
  const isRowIdMatched = selectRow.value.tb.id === row.id;
  if (isNameColumn) {
    if (isRowIdMatched && 'matchId' in row) {
      return 'col-active'; // 当名称列、行ID匹配且行中有matchId时
    }
    if ([2, 3].includes(row.levelType) && !row.matchId) {
      return 'del-active'; // 当名称列、levelType为2或3且没有matchId时
    }
  }
  return null; // 没有匹配任何条件时
}
const cellClickEventTb = ({ row }) => {
  selectRow.value.tb = row;
  // doesItMatchDisHandle(selectRow.value.tb,row);
  doesItMatchDisHandle();
}

//按钮是否置灰逻辑
const doesItMatchDisHandle = (
    zbItem = selectRow.value.zb,
    tbItem = selectRow.value.tb
) => {
  console.log('doesItMatchDisHandle',zbItem,tbItem);
  // 项目对项目
  if(zbItem.levelType === 1 && tbItem.levelType === 1){
    doesItMatchDis.value.match = true;
    doesItMatchDis.value.cancelMatch = true;
  } else
  // 单项对单项
  if(zbItem.levelType === 2 && tbItem.levelType === 2 ||
  // 单位对单位
    zbItem.levelType === 3 &&  tbItem.levelType === 3
  ){
    doesItMatchDis.value.match = false;
    doesItMatchDis.value.cancelMatch = false;
  } else
  // 两边选中的  层级不对应
  if(zbItem.levelType !== tbItem.levelType ){
    doesItMatchDis.value.match = true;
    doesItMatchDis.value.cancelMatch = false;
  }
}

</script>

<template>
  <div class="common">
    <div class="select-head">
      <a-input readonly v-model:value="importUrl" placeholder="请选择" />
      <a-button type="primary" @click="selectDocument">选择标书…</a-button>
    </div>
    <div class="select-body">
      <div class="box ">
        <div class="top-title">新招标工程</div>
        <div class="box-left table-content">
          <vxe-table ref="vxeTableRefZb" border :loading='loading' class='table-line' align="center" :column-config="{ resizable: true }"
                     :row-config="{ isHover: true,isCurrent: true }"  height="100%" width="100px" :row-class-name="rowClassName"
                     :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', line: true,
                        showIcon: true, expandAll: true, indent:15,iconOpen: 'icon-caret-down', iconClose: 'icon-caret-right' }"
                     :cell-class-name="cellClassNameZb" @cell-click="cellClickEventZb">
            <vxe-column type="" title="" width="60">
              <template #default="{ $rowIndex }">
                {{$rowIndex + 1}}
              </template>
            </vxe-column>
            <vxe-column field="name" title="名称" tree-node width="140"></vxe-column>
            <vxe-column field="matchName" title="匹配投标工程名称" min-width="30%" max-width="60%" ></vxe-column>
          </vxe-table>
        </div>
      </div>
      <div class="box-con">
        <a-button type="primary" class="con-button" :disabled="doesItMatchDis.match" @click="doesItMatch('1')" style="background-color:#83B4FF;border-color:#83B4FF;margin-bottom: 10px;">匹配</a-button>
        <a-button class="con-button" :disabled="doesItMatchDis.cancelMatch" @click="doesItMatch('2')" style="margin-top: 10px;">取消<br/>匹配</a-button>
      </div>
      <div class="box">
        <div class="top-title">当前投标工程</div>
        <div class="box-right table-content">
          <vxe-table ref="vxeTableRefTb" border :loading='loading' class='table-line' align="center" :column-config="{ resizable: true }"
                     :row-config="{ isHover: true, isCurrent: false}"  height="100%" width="100px" :row-class-name="rowClassName"
                     :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', line: true,
						       showIcon: true, expandAll: true, indent:15,iconOpen: 'icon-caret-down', iconClose: 'icon-caret-right' }"
                     :cell-class-name="cellClassNameTb"  @cell-click="cellClickEventTb"
          >
            <vxe-column type="" title="" width="60">
              <template #default="{row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex}">
                {{$rowIndex + 1}}
              </template>
            </vxe-column>
            <vxe-column field="name" title="名称" tree-node width="140"></vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style scoped lang="scss">
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';
@use '@/views/projectDetail/customize/subItemProject/s-table.scss';
::v-deep(.vxe-body--column.col-active) {
  box-shadow: inset 0px 0px 0px 2px #267DF5;
  background: #fff
}
::v-deep(.vxe-body--column.del-active) {
  text-decoration: line-through;
  color: red;
}

.table-content {
  height: 100%;
  //user-select: none;
  ::v-deep(.vxe-table) {
    // .vxe-cell--tree-node{
    //   // left: 0!important;
    //   padding-left: 0!important;
    // }
  }
  ::v-deep(.vxe-table .row-unit) {
    background: #e6dbeb;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #efe9f2;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #dce6fa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .row-qd .code-color) {
    color: #ce2929;
  }
  ::v-deep(.vxe-table .normal-info .code-color) {
    color: #ce2929;
    .vxe-tree-cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      //max-height: 3.0em; /* 高度为字体大小的两倍 */
      //line-height: 1.5em; /* 行高 */
      //height: auto; /* 高度为行高的两倍 */
    }
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
  ::v-deep(.surely-table-header-cell):hover {
    .icon-close-s {
      opacity: 1;
    }
  }
}
  .select-head{
    width: 100%;
    margin: 8px 0;
    display: flex;
    //height: 100%;
    input {
      margin-right: 10px;
    }
  }
  .select-body {
    flex: 1;
    display: flex;
    .top-title {
      margin: 6px 0;
    }
    .box {
      height: 100%;
      flex: 1;
      .box-left {
        //border: 1px solid #B9B9B9;
        height: 400px;
      }
      .box-right {
        //border: 1px solid #B9B9B9;
        height: 400px;
      }
    }
    .box-con {
      width: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .con-button {
        width: 40px;
        height: 40px;
        line-height: 14px;
        font-size: 12px;
        border-radius: 5px;
        padding:0;
      }
    }
  }
</style>