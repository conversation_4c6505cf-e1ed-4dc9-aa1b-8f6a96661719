import {ref, reactive, defineEmits, toRef, watch, onActivated} from 'vue';
import {message} from "ant-design-vue";
/*
* matchState 界面流转的状态
* gridRef 表格的名字
* */
export const useElectronicLabel = () => {
    const matchState = ref('ppjg');
    const gridRef = ref(null);
    // 标识节点
    const buttonState = ref({
        ppsj: true,// matchState.value === "ppsj",
        ppjg: true,// matchState.value === "ppjg",
        ppwc: true
    });
    // 弹框下方按钮置灰规则
    const changeButtonHandle = ()=>{
        switch (matchState.value) {
            case 'ppjg':
                buttonState.value.ppjg = false;
                buttonState.value.ppsj = true;
                buttonState.value.ppwc = true;
                break;
            case 'ppsj':
                buttonState.value.ppjg = false;
                buttonState.value.ppsj = false;
                buttonState.value.ppwc = false;
                break;
            case 'ppwc':
                buttonState.value.ppjg = true;
                buttonState.value.ppsj = false;
                buttonState.value.ppwc = false;
                break;
            default:
                buttonState.value.ppjg = true;
                buttonState.value.ppsj = true;
                buttonState.value.ppwc = true;
                break;
        }
    }
    // 表格配置
    const tableOptions = reactive({
        rowClassName: ({ row }) => {
            // 定义一个映射对象，将 row.kind 映射到相应的类名
            const classMapping = {
                '0': 'row-unit',
                '01': 'row-sub',
                '02': 'row-sub',
                '03': 'row-qd'
            };
            return classMapping[row.kind] || 'normal-info';
        },
        border: true,
        stripe: true,
        maxHeight: 330,
        size:'mini',
        scrollY: { enabled: true, gt: 0},
        columnConfig: {
            resizable: true
        },
        rowConfig:{
            isCurrent: true
        },
        loading:false,
        treeConfig:{ transform: true, rowField: 'sequenceNbr', parentField: 'parentId', line: true, showIcon: true,
            expandAll: true, indent:15,iconOpen: 'icon-caret-down', iconClose: 'icon-caret-right' }
    });
    // 上下移动
    const listActiveList = ref([]);
    const gridOptionsData = ref([]);
    const differenceItems = (e)=>{
        console.log('differenceItems',e)
        if(!listActiveList.value.length){
            message.info('无差异项数据');
            return null;
        }
        const xTable = gridRef.value;

        const data = gridOptionsData.value;// xTable.getData(); // 只获取一次数据
        const field = 'sequenceNbr';//界面匹配的字段
        const listActive = listActiveList.value;
        // 当前没有选中项时 取默认差异项的第一个
        if(!xTable.getCurrentRecord()){
            const findCurrent = data.find(item=>item[field] === listActive[0]);
            xTable.setCurrentRow(findCurrent);
            return null;
        }
        const findCurrent = data.find(item=>item[field] === xTable.getCurrentRecord()[field]);
        if (!findCurrent) {
            console.log('当前记录未找到');
            return null; // 如果当前记录未找到，则直接返回，避免后续操作出错
        }
        let index = listActive.findIndex(item=>findCurrent[field] == item);
        if (e === 'below') {
            index = (index + 1) % listActive.length; // 使用模运算处理索引超出范围的情况
        } else if (e === 'upper') {
            index = (index - 1 + listActive.length) % listActive.length; // 使用模运算和加法保证结果为正数
        }
        const findItem = data.find(item=>item[field] === listActive[index]);
        if(findItem){
            xTable.scrollToRow(findItem);//滚到到选中项
            xTable.setCurrentRow(findItem);
        }else {
            console.log('未找到指定索引的记录');
        }
    }
    return {
        buttonState,
        matchState,
        tableOptions,
        gridRef,
        listActiveList,
        gridOptionsData,
        differenceItems,
        changeButtonHandle
    }
};