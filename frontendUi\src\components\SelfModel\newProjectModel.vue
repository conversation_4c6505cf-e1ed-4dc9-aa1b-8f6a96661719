<!--
 * @Author: z<PERSON><PERSON><PERSON>ng
 * @Date: 2023-05-17 10:58:45
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-07 11:09:21
-->

<template>
  <common-modal
    width="700"
    height="500"
    v-model:modelValue="fatherProps.visible"
    @close="cancel"
    :title="fatherProps.showType"
    :destroyOnClose="true"
    className="dialog-self"
  >
    <a-spin
      :spinning="loadingModal"
      tip="数据加载中,请稍后..."
      wrapperClassName="spin-noMask"
    >
      <!-- 增加计税方式后样式变化   height="500" -->
      <div class="headerPro">
        <div class="proList">
          <new-project-aside
            class="content"
            @getProType="getProType"
            v-if="fatherProps.showType === '新建预算项目'"
          ></new-project-aside>
          <img src="~@/assets/img/openPro.png" alt="" class="icon" />
        </div>
        <div class="iptInfo">
          <input-list
            :projectType="iptList.iptType"
            v-bind="$attrs"
            @setLoading="setLoading"
            @closeDialog="cancel"
            v-if="fatherProps.showType === '新建预算项目'"
          ></input-list>
        </div>
      </div>
    </a-spin>
  </common-modal>
</template>

<script setup>
import newProjectAside from './newProjectAside.vue';
import inputList from './inputList.vue';
import { defineEmits, onMounted, reactive, ref } from 'vue';
const iptList = reactive({
  iptType: String,
  default: 'zhaobiao',
});
const fatherProps = defineProps({
  showType: {
    type: String, //类型字符串
  },
  visible: {
    type: Boolean,
    default: false,
  },
});
let loadingModal = ref(false);
const setLoading = bol => {
  loadingModal.value = bol;
};
const emit = defineEmits(['update:visible']);
const cancel = () => {
  iptList.iptType = 'zhaobiao';
  emit('update:visible'); //关闭弹框
};
// const emit = defineEmits(['isvisible']);
// const handleOk = () => {
//   emit('isvisible', false);
// };
const getProType = type => {
  iptList.iptType = type;
};
</script>
<style lang="scss" scoped>
.headerPro {
  width: 100%;
  display: flex;
  height: 100%;
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(216, 216, 216, 0.15);
  }
  .proList {
    width: 30%;
    height: 100%;
    background: linear-gradient(
      180deg,
      #3f78ce 0%,
      rgba(51, 131, 252, 0.75) 100%
    );
    .content{
      position: relative;
      z-index: 3;
    }
    .icon {
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .iptInfo {
    width: 70%;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
  }
}
</style>
