<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-08-22 10:54:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-10 15:30:59
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="show"
    :title="title"
    :mask="false"
    :lockView="false"
    :lockScroll="false"
    width="1000px"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="associate-sub-quotas">
      <div class="left">
        <div class="variable-table">
          <s-table
            size="small"
            class="s-table s-table1"
            bordered
            :columns="variableGridOptions.columns"
            :scroll="{ y: 220 }"
            :pagination="false"
            :animateRows="false"
            rowKey="id"
            height="220"
            :row-height="30"
            :data-source="variableGridOptions.data"
            @openEditor="openEditor"
            @beforeOpenEditor="tableCellClickEvent"
          >
            <template #bodyCell="{ column, text, record: row }">
              <template v-if="column.key === 'editVaue'">
                {{ row.showValue }}
              </template>
              <template v-else>{{ text }}</template>
            </template>
            <template
              #cellEditor="{
                column,
                modelValue,
                save,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
              }"
            >
              <template v-if="column.key === 'editVaue'">
                <a-input
                  v-if="!['S', 'S1', 'S2', 'V'].includes(row.variableCode)"
                  :bordered="false"
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row[column.field]);
                      closeEditor();
                    }
                  "
                >
                </a-input>
                <span v-else>{{ modelValue.value }}</span>
              </template>
            </template>
          </s-table>
        </div>

        <div class="guide">
          <div class="title">子目指引</div>
          <div class="guide-content">
            <div
              class="list"
              v-for="item of guideList"
              :key="item"
              :class="{ active: item === guideValue }"
              @click="guideClick(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <s-table
          size="small"
          ref="stableRef"
          class="s-table"
          bordered
          :defaultExpandAllRows="true"
          :expandedRowKeys="expandedRowKeys"
          :expandIconColumnIndex="3"
          :custom-cell="customCell"
          :rowSelection="{
            selectedRowKeys: state.selectedRowKeys,
            getCheckboxProps: record => {
              return {
                disabled: [1, 2].includes(record?.levelType) || !record?.deCode,
              };
            },
            onChange: onSelectChange,
          }"
          :columns="tableOptions.columns"
          :scroll="{ y: 428 }"
          :pagination="false"
          :animateRows="false"
          rowKey="sequenceNbr"
          :row-height="23"
          :data-source="tableOptions.data"
          @expand="expand"
          @keydown="vxeTableKeydown"
        >
          <template #bodyCell="{ text, record: row, index, column, key, openEditor, closeEditor }">
            <div v-if="column.dataIndex === 'associate'">
              <span v-if="[1, 2].includes(row?.levelType)">{{ row.name }}</span>
            </div>
            <template v-if="column.key === 'quantityExpression'">
              {{ row.quantity }}
            </template>
          </template>
          <template
            #cellEditor="{
              column,
              modelValue,
              save,
              closeEditor,
              record: row,
              editorRef,
              getPopupContainer,
            }"
          >
            <template v-if="column.key === 'quantityExpression'">
              <a-input
                :bordered="false"
                :value="modelValue.value"
                :get-popup-container="getPopupContainer"
                @update:value="
                  v => {
                    modelValue.value = v;
                  }
                "
                @blur="
                  () => {
                    quantityEditClosedEvent({ row, column }, modelValue.value, row[column.field]);
                    closeEditor();
                  }
                "
              >
              </a-input>
            </template>
          </template>
        </s-table>
      </div>
    </div>
    <div class="btn-list">
      <div class="checkbox">
        <a-checkbox-group
          v-model:value="settingValue"
          :options="plainOptions"
          @change="checkboxGroupChange"
        />
      </div>
      <a-button type="primary" ghost @click="cancel" style="margin-right: 12px">取消</a-button>
      <a-button type="primary" @click="batchSaveChildrenDeListColl">确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import api from '@/api/projectDetail';
import { useVxeTableKeyDown } from '@/hooks/useVxeTableKeyDown';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { computed, nextTick, reactive, ref, watch } from 'vue';
const { vxeTableKeydown } = useVxeTableKeyDown();
const projectStore = projectDetailStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentInfo: {
    type: Object,
    default: () => null,
  },
  type: {
    type: Number,
    default: () => null,
  },
});
const emits = defineEmits(['update:visible', 'successHandler', 'quotasCancel']);
const state = reactive({
  selectedRowKeys: [],
  selectedRows: [],
});
const expandedRowKeys = ref([]);
const updateDeList = ref([]);
let originalDataList = ref([]);

const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emits('update:visible', val);
  },
});
let guideValue = ref('显示所有');

const title = computed(() => {
  return props.currentInfo?.bdCode + '的关联子目';
});
const cancel = () => {
  emits('quotasCancel');
};
const customCell = ({ column, record: row }) => {
  let className = '';
  if (['associate'].includes(column.dataIndex)) {
    className += `Virtual-pdLeft-s${row?.levelType} `;
  }
  return { class: className };
};
let variableGridOptions = reactive({
  columns: [
    { dataIndex: 'id', width: 50, title: '序号' },
    { dataIndex: 'variableName', width: 160, title: '变量描述' },
    {
      dataIndex: 'editVaue',
      title: '值',
      key: 'editVaue',
      editable: 'cellEditorSlot',
    },
  ],
  data: [],
});
const inputRefs = reactive({});
const setInputRef = (el, field) => {
  inputRefs[field + 'Input'] = el;
};
const openEditor = cellInfos => {
  //设置编辑状态选中内容
  nextTick(() => {
    console.log('cellInfos', cellInfos);
    const inputRef = inputRefs[cellInfos.column.field + 'Input'];
    if (inputRef) {
      // inputRef.focus();
      inputRef?.select();
    }
  });
};
// 表格单击事件
const tableCellClickEvent = ({ record, column }) => {
  //左侧表格中面积-体积不可编辑
  if (['S', 'S1', 'S2', 'V'].includes(record.variableCode)) return false;
  return true;
};

let tableOptions = reactive({
  columns: [
    {
      title: '序号',
      dataIndex: 'dispNo',
      width: 50,
    },
    {
      title: '关联',
      dataIndex: 'jobContent',
      slot: true,
      width: 50,
      customRender: ({ record }) => {
        if (record?.children?.length > 0) {
          return {
            props: {
              colSpan: 3,
            },
          };
        }
        return;
      },
    },
    {
      title: '项目编码',
      dataIndex: 'deCode',
      width: 100,
    },
    {
      title: '名称',
      dataIndex: 'deName',
      // autoHeight: true,
      width: 220,
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 60,
    },
    {
      title: '工程量',
      dataIndex: 'quantityExpression',
      key: 'quantityExpression',
      editable: 'cellEditorSlot',
    },
  ],
  data: [],
});

const guideClick = value => {
  guideValue.value = value;
  queryChildrenDeColl();
};
let guideList = ref([]);

let settingValue = ref();
const plainOptions = ref([
  {
    label: '不再显示该窗口',
    value: 1,
  },
  {
    label: '当前项已关联子目',
    value: 2,
  },
]);

const getSubitemGuidanceColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiDAta', apiData);
  api.getSubitemGuidanceColl(apiData).then(res => {
    console.log('获取子目列表', res);
    if (res.status === 200 && res.result) {
      guideList.value = res.result;
    }
  });
};

const queryVariableCoefficientColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiDAta', apiData);
  api.queryVariableCoefficientColl(apiData).then(res => {
    console.log('获取父定额规则', res);
    if (res.status === 200 && res.result) {
      res.result.map(a => {
        a.showValue = a.value;
        if (a.variableCode === 'GCL') {
          a.defaultValue = a.value;
          a.quantityExpression = a?.quantityExpressio || a.value;
          a.editVaue = a.quantityExpression;
        } else {
          a.editVaue = a.value;
        }
      });
      console.log('获取父定额规则', res.result);
      variableGridOptions.data = res.result;
      queryChildrenDeColl();
    }
  });
};

const updateVariableCoefficientColl = () => {
  let apiData = {
    queryVariableCoefficient: JSON.parse(JSON.stringify(variableGridOptions.data)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
  };
  console.log('updateVariableCoefficientColl', apiData, props.currentInfo);
  api.updateVariableCoefficientColl(apiData).then(res => {
    console.log('修改父级规则数据', res);
    if (res.status === 200 && res.result) {
      res.result.map(a => {
        a.showValue = a.value;
        a.editVaue = a.variableCode === 'GCL' ? a.quantityExpression : a.value;
      });
      variableGridOptions.data = res.result;
      queryChildrenDeColl();
    }
  });
};

const queryChildrenDeColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    deCoefficient: JSON.parse(JSON.stringify(variableGridOptions.data)),
    relationContent: guideValue.value,
    updateDeList: JSON.parse(JSON.stringify(updateDeList.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiData6666666', apiData);
  api.queryChildrenDeColl(apiData).then(res => {
    console.log('获取父定额规则', res);
    if (res.status === 200 && res.result) {
      expandedRowKeys.value = [];
      expandedRowKeys.value.push(res.result[0].sequenceNbr);
      res.result[0].children.forEach((item, index) => {
        if (index === 0) {
          expandedRowKeys.value.push(item.sequenceNbr);
        }
      });
      tableOptions.data = res.result;
      originalDataList.value = JSON.parse(JSON.stringify(res.result));
      console.log('expandedRowKeys', expandedRowKeys.value);
    }
  });
};
const sortIdsByTreeOrder = (tree, rows) => {
  const order = [];
  const idToIndex = new Map();
  const ids = rows.map(item => item.sequenceNbr);
  function dfs(node) {
    if (!node) return;
    if (ids.includes(node.sequenceNbr)) {
      order.push(node.sequenceNbr);
    }
    if (node.children) {
      for (const child of node.children) {
        dfs(child);
      }
    }
  }
  dfs(tree);
  order.forEach((id, index) => {
    idToIndex.set(id, index);
  });
  rows.sort((a, b) => idToIndex.get(a.sequenceNbr) - idToIndex.get(b.sequenceNbr));
  return rows;
};
const batchSaveChildrenDeListColl = () => {
  const deArray = sortIdsByTreeOrder(
    tableOptions.data[0],
    JSON.parse(JSON.stringify(state.selectedRows))
  );
  console.log('🚀 ~ batchSaveChildrenDeListColl ~ deArray:', deArray);
  let apiData = {
    deArray,
    zmQuantity: variableGridOptions.data.filter(x => x.variableCode === 'GCL')[0].value,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(props.currentInfo)),
    deCoefficient: JSON.parse(JSON.stringify(variableGridOptions.data)),
    kind: '04',
    indexId: null,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    libraryCode: props.currentInfo.libraryCode,
    fbfxOrCsxm: props.type === 1 ? 'fbfx' : 'csxm',
  };
  api.batchSaveChildrenDeListColl(apiData).then(res => {
    console.log('保存成功', res, apiData);
    if (res.status === 200 && res.result) {
      cancel();
      emits('successHandler', res.result);
    }
  });
};

const onSelectChange = (selectedRowKeys, selectedRows) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows);
  state.selectedRowKeys = selectedRowKeys;
  state.selectedRows = selectedRows;
};

const expand = (expanded, record) => {
  console.log('expanded1111111111', expanded, record);
  if (expanded) {
    expandedRowKeys.value.push(record.sequenceNbr);
  } else {
    let index = expandedRowKeys.value.findIndex(x => x === record.sequenceNbr);
    expandedRowKeys.value.splice(index, 1);
  }
};

const checkboxGroupChange = checkedValue => {
  console.log('checkedValue', checkedValue);
  if (checkedValue.includes(1)) {
    projectConvenientSetColl();
  }
  if (checkedValue.includes(2)) {
    findNodePathById();
  }
  if (!checkedValue.includes(2)) {
    tableOptions.data = originalDataList.value;
  }
};

// 筛选子定额选中数据
function findNodePathById() {
  let allList = JSON.parse(JSON.stringify(originalDataList.value));
  if (guideValue.value === '显示所有') {
    tableOptions.data = allList.map(list => {
      list.children = list.children.filter(item => {
        console.log('item', item, guideValue.value);
        item.children = item.children.filter(child => {
          return state.selectedRowKeys.includes(child.sequenceNbr);
        });
        return item.children.length > 0;
      });
      return list.children.length > 0 ? list : null;
    });
  } else {
    tableOptions.data = allList.map(list => {
      list.children = list.children.map(item => {
        console.log('item', item, guideValue.value);
        if (item.jobContent === guideValue.value) {
          item.children = item.children.filter(child => {
            return state.selectedRowKeys.includes(child.sequenceNbr);
          });
        }
        return item;
      });
      return list;
    });
  }
}

const projectConvenientSetColl = () => {
  let changeParams = {
    crdezsdeglzmtk: false,
  };
  projectStore
    .SET_GLOBAL_CONFIG(projectStore.currentTreeGroupInfo?.constructId, changeParams)
    .then(res => {
      if (res.result) {
        message.success('设置成功');
      } else {
        message.error('设置失败');
      }
    });
  // let apiData = {
  //   constructId: projectStore.currentTreeGroupInfo?.constructId,
  //   column: 'deGlTcFlag',
  //   value: false,
  // };
  // api.projectConvenientSetColl(apiData).then(res => {
  //   if (res.result) {
  //     message.success('设置成功');
  //   }
  // });
};
const quantityExpressionHandler = (type, quantityExpression, defaultValue) => {
  //子目工程量输入表达式限制和非子目工程量输入限制
  const errMsg = '计算式输入非法，请重新输入标准四则运算表达式或数值';
  let value = quantityExpression;
  if (!value.length) return [true, '工程量表达式不能为空'];
  const reg = /[^0-9a-zA-Z_|\-|\*|\+|\/|\.|(|)|（|）]/g;
  if (reg.test(value)) return [true, errMsg];
  let arr = value.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  if (arr === null) return [true, errMsg];
  let result = [];
  for (let f of arr) {
    if (isNaN(Number(f))) {
      if (
        (type === 'bds' && f.toUpperCase() !== 'QDL') ||
        (type === 'sz' && !!isNaN(parseFloat(f)))
      )
        return [true, errMsg];
      const reg = new RegExp(`${f}`, 'g');
      console.log('替换：', f, defaultValue);
      value = value.replace(reg, defaultValue);
    }
  }
  try {
    const runResult = new Function(`return ${value}`);
    if (!isFinite(runResult())) return [true, errMsg];
  } catch (error) {
    return [false, ''];
  }
  return [false, value];
};
const editClosedEvent = ({ row, column }, newValue, oldValue) => {
  //将QDL和qdl替换成对应的value值
  if (row.editVaue === newValue) return;
  let type = row.variableCode === 'GCL' ? 'bds' : 'sz';
  type === 'bds' ? (row.quantityExpression = newValue) : '';
  const [isSuccess, value] = quantityExpressionHandler(
    type,
    newValue,
    type === 'bds' ? row.defaultValue : row.value
  );
  if (isSuccess) {
    message.error(value);
    type === 'bds' ? (row.quantityExpression = oldValue) : '';
    return;
  } else {
    row.value = eval(type === 'bds' ? value : newValue);
  }
  updateVariableCoefficientColl();
};

const quantityEditClosedEvent = ({ row, column }, newValue, oldValue) => {
  console.log('1111111111', newValue, row);
  row.quantityExpression = newValue;
  row.quantity = row.quantityExpression;
  let status = updateDeList.value.some(x => x.sequenceNbr === row.sequenceNbr);
  if (status) {
    updateDeList.value.forEach(item => {
      if (item.sequenceNbr === row.sequenceNbr) {
        item = row;
      }
    });
  } else {
    updateDeList.value.push(row);
  }
  queryChildrenDeColl();
};

watch(
  () => props.visible,
  () => {
    console.log('watch进来不');
    if (props.visible) {
      guideValue.value = '显示所有';
      state.selectedRowKeys = [];
      state.selectedRows = [];
      updateDeList.value = [];
      getSubitemGuidanceColl();
      queryVariableCoefficientColl();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.btn-list {
  margin-top: 10px;
  display: flex;
  .checkbox {
    flex: 1;
  }
}
.s-table {
  ::v-deep(.surely-table) {
    border-radius: 0;
    font-size: 13px;
    height: 100%;
  }
}
.s-table1 {
  ::v-deep(.surely-table) {
    height: 220px;
  }
}
.variable-table {
  height: 220px;
}
.guide {
  height: calc(100% - 220px);
  .title {
    font-size: 14px;
    line-height: 24px;
    padding: 10px 0 5px;
    color: #000;
  }
  .guide-content {
    padding: 10px 6px 0px;
    border: 1px solid #b9b9b9;
    overflow-y: auto;
    height: calc(100% - 40px);
    .list {
      padding: 0 12px;
      font-size: 13px;
      line-height: 22px;
      color: #000;
      cursor: pointer;
      &.active {
        background: rgba(192, 217, 255, 0.39);
      }
    }
  }
}
.associate-sub-quotas {
  display: flex;
  height: calc(100% - 40px);
  .left {
    height: 100%;
    width: 330px;
    margin-right: 10px;
  }
  .right {
    height: 100%;
    width: calc(100% - 340px);
    ::v-deep(.surely-table-cell-content) {
      height: 22px !important;
      line-height: 22px !important;
      padding: 0 8px;
    }
  }
}
.ant-input {
  height: 100%;
  font-size: 12px;
  resize: none;
  padding: 0;
}
</style>
