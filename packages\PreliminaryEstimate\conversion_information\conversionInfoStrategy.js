const Kind3TypeBRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeBRuleHandler");
const Kind2RuleHandler = require("../standard_conversion/rule_handler/Kind2RuleHandler");
const Kind3RuleHandler = require("../standard_conversion/rule_handler/Kind3RuleHandler");
const Kind3TypeCRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeCRuleHandler");
const Kind4RuleHandler = require("../standard_conversion/rule_handler/Kind4RuleHandler");
const Kind0RuleHandler = require("../standard_conversion/rule_handler/Kind0RuleHandler");
const Kind1RuleHandler = require("../standard_conversion/rule_handler/Kind1RuleHandler");
const Kind5RuleHandler = require("../standard_conversion/rule_handler/Kind5RuleHandler");
const ConversionService = require("../standard_conversion/util/ConversionService");
const EE = require("../../../core/ee");
const ConstantUtil = require("../enums/ConstantUtil");
const {UnitRcjCacheUtil} = require("../standard_conversion/util/UnitRcjCacheUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
// const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
// const {RcjCalculateHandler} = require("../rcj_handle/calculate/RcjCalculateHandler");
const {RCJKind} = require("../enums/ConversionSourceEnum");
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const WildcardMap = require("../core/container/WildcardMap");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const CommonConstants = require("../constants/CommonConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const GsKind3TypeCRuleHandler = require("../standard_conversion/rule_handler/GsKind3TypeCRuleHandler");
const {GsDe} = require("../models/GsDe");

class ConversionInfoStrategy{
    static UNITE_RULE_KIND = "4";

    constructor() {
        this.service = EE.app.service;
        this.app = EE.app;
    }

    async init(constructId, singleId, unitId, deId) {

        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.deId = deId;

        this.unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        this.deLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
        const belong = "fbfx";
        this.deBeLong = belong;
        this.de = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        this.constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        this.subDe = await this.service.PreliminaryEstimate.gsProjectCommonService.findSubDeByDeId(constructId, unitId, deId, undefined);

        this.deUpDateObj = {
            // 换算信息
            conversionInfo: [],
            redArray: [],
            blackArray: [],
            nameSuffixArray: [],
            addedDes: [],
            deTypes: []
        }

        this.conversionService = new ConversionService();
    }

    async prepare(){
        this.deInitialRcjs = await this._deInitialRcjs(this.unitProject, this.de);
        await this._delDeAddByRule();
    }

    /**
     * 执行标准换算
     */
    async execute1(){
        let conversionInfos = this.de.conversionInfo.find(v => v.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE)

        if(ObjectUtils.isEmpty(conversionInfos.children)){
            return;
        }
        await this.prepare();

        for(let rule of conversionInfos.children){
            // 执行标准换算规则时可能会新增、替换人材机数据，所以在每次执行规则前重新获取定额的人材机数据
            let handler = this._getRuleHandler(rule);
            await handler.execute();
        }
        await this.after();
    }

    async execute(){
        let conversionInfos = this.de.conversionInfo
        let conversionInfoList = conversionInfos.filter(item => item.source === ConversionInfoUtil.UNITE_CONVERSION_SOURCE);
        await this.prepare();
        for (let conversionInfo of conversionInfos) {
            // 人材机明细
            if (String(conversionInfo.kind) === '5') {
                let handler = this._getRuleHandler(conversionInfo);
                await handler.execute();
            }

            // 标准换算
            if (conversionInfo.conversionString === ConversionInfoUtil.STARDARD_CONVERSION_SOURCE) {
                // 标准换算
                for (let rule of conversionInfo.children) {
                    let handler = this._getRuleHandler(rule);
                    await handler.execute();
                }
                // 统一换算
                for (let rule of conversionInfoList) {
                    let handler = this._getRuleHandler(rule);
                    await handler.execute();
                }
            }
        }
        await this.after();
    }


    /**
     * 标准换算执行后计算，其他材料/机械、单价构成、......
     */
    async after(){
        // if(ObjectUtils.isNotEmpty(this.deUpDateObj.addedDes)){
        //     for(let addedDe of this.deUpDateObj.addedDes){
        //         await new ConversionInfoStrategy(this.constructId,this.singleId,this.unitId,addedDe.sequenceNbr, [], null).execute();
        //     }
        // }

        this._upDateDeInfo();


        // TODO 将处理后的人材机替换单位工程中对应定额人材机
        let rcjs =this.constructProjectRcjs.filter(rcj => rcj.deId != this.de.sequenceNbr);
        rcjs.push(...this.deInitialRcjs);
        this.constructProjectRcjs = rcjs;

        for (let item of this.de.conversionInfo) {
            item.mathHandlers = []
        }

        // 重新计算人材机
        await ProjectDomain.getDomain(this.constructId).getDeDomain().notify(this.deLine);
        // 修改单价调差
        await this._updateDePrice();
        DeTypeCheckUtil.checkAllDeType(this.deLine, ProjectDomain.getDomain(this.constructId).getDeDomain().ctx);
        // 重新计算费用汇总
        try {
            await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: this.constructId,
                singleId: this.singleId,
                unitId: this.unitId,
                constructMajorType: this.deLine.libraryCode
            });
        } catch (error) {
            console.error("捕获到异常:", error);
        }

        // 处理人材机冻结状态：冻结的人材机不参与标准换算
        // this._dealFreeze();
    }

    async _updateDePrice() {
        let tcPrice = this.deUpDateObj.tcPrice;
        if (ObjectUtils.isNotEmpty(tcPrice)) {
            this.deLine.resourceTZ = CommonConstants.COMMON_YES;
            await this.service.PreliminaryEstimate.gsDeService.updatePrice(this.constructId, this.unitId, this.deLine.sequenceNbr, tcPrice, true);
        }
    }

    _dealFreeze(){
        // 1. 判断标准换算规则是否全部为初始默认状态
        let defaultInChanges = this.deAllRules.filter((v) => {
            return !this._isRuleChanged(v);
        })

        // 存在换算规则不为默认值
        if(defaultInChanges.length != this.deAllRules.length){
            return;
        }

        // 2. 如果所有的标准换算规则都是默认状态，删除人材机的冻结状态
        this.deRcjs.forEach((rcj) => {
            if(rcj.freezeRuleIds){
                delete rcj.freezeRuleIds;
            }
        });
    }

    _upDateDeInfo(){
        //换算信息
        this._resetDeName();

        this.de.redArray = this.deUpDateObj.redArray;
        this.de.codeSuffixHistory = this.deUpDateObj.redArray;
        this.de.blackArray = this.deUpDateObj.blackArray;
        this.de.nameSuffixHistory = this.deUpDateObj.nameSuffixArray;

        let nameSuffix = this.deUpDateObj.nameSuffixArray.join(" ");

        this.de.deName = `${this.de.deName} ${nameSuffix}`;
        this.deLine.deName = `${this.deLine.deName} ${nameSuffix}`;

    }

    _resetDeName() {
        let de = this.de;
        let deLine = this.deLine;
        if (
            Array.isArray(de.nameSuffixHistory) &&
            de.nameSuffixHistory.length > 0
        ) {
            // 恢复名称
            for (const history of de.nameSuffixHistory) {
                deLine.deName = deLine.deName.replace(history, "").trim();
            }
        }
    }

    _conversionRcjs(deRcjs){
        return deRcjs.filter((rcj) => {
            if (rcj.kind == RCJKind.主材 && !this.de.mainMatConvertMod) {
                return false;
            }
            if(ConstantUtil.SPECIAL_RCJ.includes(rcj.materialCode) && rcj.unit==ConstantUtil.BAIFENHAO){
                return false;
            }
            return true;
        });
    }

    /**
     * 处理定额人材机，并返回
     * @param unit
     * @param deId
     * @param changedRules
     * @private
     */
    async _resetDeRCJ(unit, de, changedRules){
        const deId = de.sequenceNbr;
        // 清除过程历史数据 本次执行换算会重新填充数据
        unit.constructProjectRcjs = unit.constructProjectRcjs
            .map((rcj) => {
                // 清除由标准换算 添加的数据 后续重新执行过程
                if (rcj.deId == deId && rcj.addFromConversionRuleId) {
                    return null;
                }
                if (rcj.deId == deId && rcj.materialReplaceHistory) {
                    rcj.materialCode = rcj.materialReplaceHistory.materialCode;
                    rcj.materialName = rcj.materialReplaceHistory.materialName;
                    rcj.dePrice = rcj.materialReplaceHistory.dePrice;
                }
                return rcj;
            })
            .filter((v) => !!v);

        let deRcjs = unit.constructProjectRcjs.filter((v) => v.deId == deId);

        // 将人材机的原始含量赋值给消耗量
        for(const rcj of deRcjs){
            //如果该人材机之前被替换过数据,并且没有被冻结  取消该记录 并改消耗量
            if(ObjectUtils.isNotEmpty(rcj.changeResQtyRuleIds) && ObjectUtils.isEmpty(rcj.freezeRuleIds)){
                rcj.changeResQtyRuleIds=[];
                rcj.resQty=de.standardConvertMod == StandardConvertMod.Default
                    ? rcj.initResQty
                    : rcj.consumerResQty || rcj.initResQty;
            }
            //如果由标准换算引起人材机替换
            if(ObjectUtils.isNotEmpty(rcj.editFromConversion)){
                let rule = this.deAllRules.find(r => r.sequenceNbr == rcj.editFromConversion.ruleId);
                if(rule.kind != "2"){ // kind=2引起的变化不做处理
                    let toRcj = await this.conversionService.getRcjByCodes(rcj.editFromConversion.fromRCJLibraryCode, rcj.editFromConversion.fromRCJCode)
                    await this.conversionService.editRcj(rcj, toRcj, this);
                    continue;
                }
            }
        }

        if(ObjectUtils.isNotEmpty(this.changedRules) && ObjectUtils.isEmpty(de.rcjBackup)){
            // 首次启用规则时备份一次人材机数据
            de.rcjBackup = deRcjs
        }

        return deRcjs;
    }

    async _delDeAddByRule(){
        let de = this.de;
        if (ObjectUtils.isEmpty(de.addByRuleDeIds)){
            return;
        }

        // 当恢复换算默认值时，删除上次新增定额
        for ( let deRuleIdObj of de.addByRuleDeIds) {
            let addDeId = deRuleIdObj.deId;
            let deAddByRule = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(this.constructId, this.unitId, addDeId);
            if(ObjectUtils.isNotEmpty(deAddByRule)) {
                await ProjectDomain.getDomain(this.constructId).getDeDomain().removeDeRow(addDeId)
            }
        }

        de.addByRuleDeIds = [];
    }

    _getRuleHandler(rule){
        if(rule.kind == "1"){
            return new Kind1RuleHandler(this, rule);
        }

        if(rule.kind == "2"){
            return new Kind2RuleHandler(this, rule);
        }

        if(rule.kind == "3"){
            if(rule.type == "b") {
                return new Kind3TypeBRuleHandler(this, rule);
            }if(rule.type == "c") {
                if (this.deLine.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ
                    || this.deLine.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    return new GsKind3TypeCRuleHandler(this, rule);
                } else {
                    return new Kind3TypeCRuleHandler(this, rule);
                }
            }else{
                return new Kind3RuleHandler(this, rule);
            }
        }

        if(rule.kind == "4"){
            return new Kind4RuleHandler(this, rule);
        }

        if(rule.kind == "0"){
            return new Kind0RuleHandler(this, rule);
        }

        if(rule.kind == "5"){
            return new Kind5RuleHandler(this, rule);
        }
    }

    _getChangedRules(deAllRules){
        return deAllRules.filter((v) => {
            return this._isRuleChanged(v);
        }).sort((a, b) => a.index - b.index);

    }

    _isRuleChanged(rule){
        if(rule.kind == "1"){
            return !!rule.selected;
        }

        if(rule.kind=="2"){
            return ObjectUtils.isNotEmpty(rule.clpb)
                && (
                    (rule.currentRcjCode != rule.clpb.detailsCode || rule.currentRcjLibraryCode != rule.clpb.libraryCode)
                    || (rule.defaultRcjCode != rule.clpb.detailsCode || rule.defaultRcjLibraryCode != rule.clpb.libraryCode)
                )
        }

        if(rule.kind == "3" || rule.kind == "4" || rule.kind == "0"){
            return ObjectUtils.isNotEmpty(rule.selectedRule)
                && rule.selectedRule != "NaN"
                && rule.selectedRule != rule.defaultValue;
        }

        throw new Error("错误的规则类型，kind=" + rule.kind);
    }

    _combineRules(
        unit,
        de,
        // 有可能是标准 有可能是统一
        currentRules
    ) {
        const combine = [];

        // kind=3,type=b规则新增的换算规则
        if(de.conversionAddByRule){
            combine.push(de.conversionAddByRule);
        }

        if(ObjectUtils.isNotEmpty(currentRules)) {
            combine.push(...currentRules);

            // 是否是统一换算
            let ifUnified = !!combine[0].isUniteRule;
            if (ifUnified) {
                combine.push(...de.conversionList);
            } else {
                combine.push(
                    ...this._formatRuleByUniteRules(unit.defaultConcersions[de.sequenceNbr], de)
                );
            }
        }

        return combine.sort((a, b) => a.index - b.index);
    }

    _formatRuleByUniteRules(unitRules, de) {

        if(ObjectUtils.isEmpty(unitRules)){
            return [];
        }

        const typeMaps = new Map([
            ["人工费", "R"],
            ["机械费", "J"],
            ["材料费", "C"],
            ["单价", ""]
        ]);

        return unitRules.map((unitRule, index) => {
            const type = typeMaps.get(unitRule.type);

            // 拼接 R*n C*n J*n
            let math = type + "*" + unitRule.val;
            // 默认的规则用 定额id + def +类型标识
            // 注释该seqNo, 直接使用主键newAlgorithm.sequenceNbr; //let seqNo = deId + "def" + type;
            return {
                sequenceNbr: unitRule.sequenceNbr,
                type: "0",
                kind: unitRule.kind || ConversionInfoStrategy.UNITE_RULE_KIND,
                math: math,
                relation: math,
                defaultValue: 1,
                selectedRule: unitRule.val,
                fbFxDeId: de.sequenceNbr, // 分部分项或措施项目定额id; ps:标准换算中有fbFxDeId,这里在统一换算中也加上,用于BS端在处理ysf文件时,通过deId+ruleId反查出operatingRecord.
                index: 999999 + index,
                libraryCode: de.libraryCode,
                isUniteRule: true,
            };
        });
    }

    _formatRuleByConversionList(conversionList){
        if(ObjectUtils.isEmpty(conversionList)){
            return [];
        }

        conversionList.forEach((r) => {
            r.isUniteRule = false;
            r.fbFxDeId = r.deId;
        });

        return conversionList;
    }

    _conversionSnapshot(de, currentRules) {
        // 查询列表时会初始化 如果这时未初始化 直接报错
        de.conversionList = de.conversionList || [];

        de.conversionList.forEach((target)=> {
            const srcRule = currentRules.find((r) => r.sequenceNbr == target.sequenceNbr);
            if(!srcRule){
                return;
            }

            if (typeof srcRule.selected == "string") {
                target.value = srcRule.selected;
            } else {
                target.selected = srcRule.selected;
            }
            target.index = srcRule.index;
            target.clpb = srcRule.clpb;
            target.selectedRule = srcRule.selectedRule;
            if(srcRule.clpb?.detailsCode){
                target.currentRcjCode =  srcRule.clpb.detailsCode;
                target.currentRcjLibraryCode = srcRule.clpb.libraryCode;
                target.rcjId = srcRule.clpb.standardId;
                target.ruleInfo = srcRule.clpb.details + " " + (ObjectUtils.isEmpty(srcRule.clpb.specification) ? "" : srcRule.clpb.specification);
                target.selectedRuleGroup = srcRule.clpb.groupName;
                target.topGroupType = srcRule.clpb.groupName;
            }
        });

        de.conversionList = de.conversionList.sort((a, b) => a.index - b.index);
    }

    async _deInitialRcjs(unitProject, de) {
        let rcjs = [];

        // if(ObjectUtils.isEmpty(de.jointStandardRcj)){
        //     return [];
        // }

        // TODO 二级定额处理
        let deRcjRelationList = await this.service.PreliminaryEstimate.gsBaseDeRcjRelationService.getDeRcjRelationByDeId(de.standardId);

        let codes = deRcjRelationList.map(r => r.materialCode);

        for (let i = 0; i < codes.length; i++) {
            let code = codes[i];
            // TODO 缓存处理
            let rcjMemory = await this.service.PreliminaryEstimate.gsRcjService.getRcjMemory(this.constructId, this.unitId);
            let rcj = ObjectUtil.cloneDeep(UnitRcjCacheUtil.getByCode(unitProject,code));
            if(rcj){
                rcj.deId = this.de.sequenceNbr;
                rcjs.push(rcj);
            }
        }

        let deRcjs = this.constructProjectRcjs.filter(f => f.deId === this.de.sequenceNbr);
        let deRcjIds = deRcjs.map(item => item.sequenceNbr)
        // 恢复-删除
        for (let rcj of deRcjs) {
            if (!this.deLine.initChildIds.includes(rcj.sequenceNbr)) {
                let param = {isConversionDeal: true}
                await this.service.PreliminaryEstimate.gsRcjService.deleteRcjByCodeData(this.deId, this.constructId, this.unitId, rcj.sequenceNbr, true, param);
            }
        }
        // 恢复-新增
        let insertRcjIds = this.deLine.initChildIds.filter(item => !(deRcjIds.includes(item)));
        for (let insertRcjId of insertRcjIds) {
            let conversion = this.de.conversionInfo?.find(item => item.rcjId === insertRcjId && item.rcjType === 'del')
            if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
                conversion = this.de.lastConversionInfo?.find(item => item.rcjId === insertRcjId && item.rcjType === 'del');
            }
            if (conversion) {
                let param = {
                    isConversionDeal: true,
                    sequenceNbr: conversion.rcjId
                };
                let baseRcjModel = await this.service.PreliminaryEstimate.gsBaseRcjService.getRcjBySequenceNbr(conversion.originalRcjId);
                await this.service.PreliminaryEstimate.gsRcjService.addRcjData(this.deId, baseRcjModel, this.constructId, this.singleId, this.unitId, this.deId, "", param);
                this.constructProjectRcjs = ProjectDomain.getDomain(this.constructId).getResourceDomain().getResource(WildcardMap.generateKey(this.unitId) + WildcardMap.WILDCARD);
                let replaceRcj = this.constructProjectRcjs.find(item => item.sequenceNbr === conversion.rcjId)
                ObjectUtils.copyProp(conversion.rcj, replaceRcj);
            }
        }
        // 恢复-替换
        let conversionReplaces = this.de.conversionInfo?.filter(item => item.rcjType === 'replace')
        if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
            conversionReplaces = this.de.lastConversionInfo?.filter(item => item.rcjType === 'replace');
            conversionReplaces = conversionReplaces?conversionReplaces:[]
        }
        for (let conversionReplace of conversionReplaces) {
            let replaceRcj = this.constructProjectRcjs.find(item => item.sequenceNbr === conversionReplace.rcjId)
            ObjectUtils.copyProp(conversionReplace.rcj, replaceRcj);
        }
        // 恢复-消耗量
        let conversionUpdateQtys = this.de.conversionInfo?.filter(item => item.rcjType === 'updateQty')
        if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
            conversionUpdateQtys = this.de.lastConversionInfo?.filter(item => item.rcjType === 'updateQty');
            conversionUpdateQtys = conversionUpdateQtys?conversionUpdateQtys:[]
        }
        for (let conversionUpdateQty of conversionUpdateQtys) {
            this.constructProjectRcjs.map(item => {
                if (item.sequenceNbr === conversionUpdateQty.rcjId) {
                    item.resQty = conversionUpdateQty.lastResQty
                }
            });
            this.constructProjectRcjs.find(item => item.sequenceNbr === conversionUpdateQtys[0].rcjId);
        }

        this.constructProjectRcjs = ProjectDomain.getDomain(de.constructId).resourceDomain.getResource(WildcardMap.generateKey(de.unitId) + WildcardMap.WILDCARD);
        return rcjs;
    }
}

module.exports = ConversionInfoStrategy;
