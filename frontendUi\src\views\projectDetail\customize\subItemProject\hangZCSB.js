/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-08-06 15:29:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-03-10 15:20:14
 */
import {ref} from 'vue'
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
/**
 * 下挂主材、设备
 */
export const hangZCSB = ({pageType='fbfx',refreshList=()=>{}}) => {
  const projectStore = projectDetailStore();
  let DEHangZCSBVisible = ref(false);
  const openDEHangZCSB = () => {
    DEHangZCSBVisible.value = true;
  }
  const hangMenuList = [
    {
      code: 'QDGenerateZC',
      name: '根据清单名称生成主材',
      visible: true,
      disabled: true,
    },
    {
      code: 'DEGenerateZC',
      name: '根据定额名称生成主材',
      visible: true,
      disabled: true,
    },
    {
      code: 'syncToDE',
      name: '同步名称至定额',
      visible: true,
      disabled: true,
    },
    {
      code: 'QDGenerateSB',
      name: '根据清单名称生成设备',
      visible: true,
      disabled: true,
    },
    {
      code: 'DEGenerateSB',
      name: '根据定额名称生成设备',
      visible: true,
      disabled: true,
    },
  ]
  /**
   * 右键菜单限制操作
   * @param {*} menu
   * @param {*} row
   */
  const hangMenuDisabledHandler = (menu, row) => {
    console.log(hangMenuList.find(item => item.code === menu.code))
    if ([94,95].includes(row.kind) && ['add', 'lock', 'tempDelete', 'batchDelete', 'MainList', 'noteList'].includes(menu.code)) {
      menu.disabled = true;
      if (menu.children?.length) {
        menu.children.forEach(item => {
          item.disabled = true;
        });
      }
    }
    if (!hangMenuList.find(item => item.code === menu.code)) return;
    menu.disabled = true;
    if (row.kind === '03') {
      if (['QDGenerateZC', 'QDGenerateSB'].includes(menu.code)) {
        menu.disabled = false;
      }
    }
    if (row.kind === '04' && row.rcjFlag !== 1) {
      if (['QDGenerateZC','DEGenerateZC', 'DEGenerateSB', 'QDGenerateSB'].includes(menu.code)) {
        menu.disabled = false;
      }
    }

    if ([94, 95].includes(row.kind)) {
      if (['syncToDE'].includes(menu.code) && row.customParent?.kind === '04') {
        menu.disabled = false;
      }
    }
  }


  let syncToDEVisible = ref(false); // 同步名称到定额
  /**
   * 右键菜单点击
   * @param {*} menu
   * @param {*} row
   */
  const hangZCSBMenuClick = async (menu, row, bdCode) => {
    if (!hangMenuList.find(item => item.code === menu.code)) return;
    if(menu.code === 'syncToDE') {
      syncToDEVisible.value = true;
    }
    if (row.kind === '03' && ['QDGenerateSB', 'QDGenerateZC'].includes(menu.code)) {
      // 清单行操作
      await QDGenerateZCSB(menu, row, bdCode)
    }
    if (row.kind === '04' && ['QDGenerateZC','DEGenerateZC', 'DEGenerateSB', 'QDGenerateSB'].includes(menu.code)) {
      // 定额行操作
      await DEGenerateZCSB(menu, row, bdCode)
    }
  }

  const DEGenerateZCSB = async (menu, row, bdCode) => {
    let materialCode = bdCode;
    let kind = getKindByMenuCode(menu.code);
    const taxRemoval = getTaxRemoval(kind);
    if (!materialCode) {
      materialCode = await getCodeColl(3, kind);
    }
    let {unit, resQty} = unitAndQtyHandler(row)
    const materialName = getGenerateMaterialNameByCode(row, menu.code)
    let apiParam = {
      kind,
      materialName, //项目名称
      materialCode, //项目编码
      specification: null, // 规格及型号
      unit, // 单位
      taxRemoval: 0, // 除税系数
      resQty, // 材料消耗量
      dePrice: 0, // 定额价
      marketPrice: 0, // 市场价
    }
    addMxqBcRcjData(apiParam, row, bdCode)
  }
  // 定额行添加人材机
  const addMxqBcRcjData = (inputData, row, bdCode, callback=()=>{}) => {
    inputData.isSupplement = 1;
    let deRow = row;
    if(row.kind !== '04') {
      // 不是定额选中主材设备取父级定额
      deRow = row.customParent;
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: row
        ? JSON.parse(JSON.stringify(row))
        : null,
      pageInfo: JSON.parse(JSON.stringify(inputData)),
      type: pageType === 'fbfx' ? 1 : 2,
      region: 1,
      rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      deItemId: deRow?.sequenceNbr,
    };
    console.log('定额生成主材设备参数', apiData);
    api.supplementRcjDetail(apiData).then(async res => {
      console.log('1111111111', res);
      if (res.status === 200 && res.result) {
        if (bdCode) {
          // 解决替换报错，新增完删除当前行
          let apiData = {
            sequenceNbr: row.sequenceNbr,
            unitId: projectStore.currentTreeInfo?.id,
            constructId: projectStore.currentTreeGroupInfo?.constructId,
            singleId: projectStore.currentTreeGroupInfo?.singleId,
            tempDeleteFlag: null,
            de: JSON.parse(JSON.stringify(row)),
          };
          console.log('删除apiData', apiData);
          await api.delDetail(apiData);
        }
        callback()
        message.success('操作成功');
        refreshList('Refresh');
      }
    });
  };
  /**
   * 清单行生成主材设备
   * @param {*} menu
   * @param {*} row
   * @param {*} bdCode
   */
  const QDGenerateZCSB = async (menu, row, bdCode) => {
    let materialCode = bdCode;
    let kind = getKindByMenuCode(menu.code);
    const taxRemoval = getTaxRemoval(kind);
    if (!materialCode) {
      materialCode = await getCodeColl(3, kind);
    }
    let {unit, resQty} = unitAndQtyHandler(row)
    const materialName = getGenerateMaterialNameByCode(row, menu.code)
    let apiParam = {
      kind,
      materialName, //项目名称
      materialCode, //项目编码
      specification: null, // 规格及型号
      unit, // 单位
      taxRemoval: 0, // 除税系数
      resQty, // 材料消耗量
      dePrice: 0, // 定额价
      marketPrice: 0, // 市场价
      zcFlag:true
    }
    addBjqBcRcjData(apiParam, row)
  }
  // 清单行分部分项 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = (param, row) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
    pageInfo: JSON.parse(JSON.stringify(param)),
    type: pageType === 'fbfx' ? 1 : 2,
    region: 0,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };
  console.log('生成主材设备参数：', apiData)
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('操作成功');
      refreshList('Refresh',res.result.data.sequenceNbr);
    }
  });
};
  const getKindByMenuCode = (menuCode) => {
     // 主材5，设备4
    const map = {
      QDGenerateZC: 5,
      QDGenerateSB: 4,
      DEGenerateSB:4,
      DEGenerateZC: 5
    }
    return map[menuCode];
  }
  const getTaxRemoval = (kind) => {
    let val = 0;
    switch (kind) {
      case 1:
        val = 0;
        break;
      case 2:
      case 5:
        val = 11.28;
        break;
      case 3:
        val = 8.66;
        break;
      case 4:
        val = 11.36;
        break;
    }
    return val;
  }
  const unitAndQtyHandler = (row) => {
    // 遇到100m类似的单位，单位取m,消耗量取100
    const num = parseFloat(row.unit)
    if (isNaN(num)) return {unit: row.unit, resQty: 1}
    const list = row.unit.split(num)
    return {unit: list[list.length - 1], resQty: num}
  }
  /**
   * 根据清单或者定额获取生成主材设备名
   */
  const getGenerateMaterialNameByCode = (row, code) => {
    if (['QDGenerateZC', 'QDGenerateSB'].includes(code) && row.kind !== '03') {
      return getParentInfoByKind(row)?.name;
    }
    return row.name;
  }
  const getParentInfoByKind = (row, kind = '03') => {
    if (!row.customParent) return null;
    if (row.customParent?.kind === kind) return row.customParent
    return getParentInfoByKind(row.customParent, kind)
  }
  const getCodeColl = async (type, kind = null) => {
    let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        type: type,
        clKind: kind,
    }
    const codeCollRes = await api.defaultCodeColl(apiData)
    if (codeCollRes.status === 200) {
      return codeCollRes.result
    }
    return ''
  }
  const isNotCostDe = (row) => {
    const { kind, isCostDe } = row || {};
    return (
      kind === '04' &&
      (!isCostDe ||
        isCostDe === 4 ||
        (projectStore.deStandardReleaseYear === '22' && isCostDe === 3))
    );
  };
  const isOtherMaterial = (materialRow) => {
    const { materialCode } = materialRow || {};
    return [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(materialCode);
  };
  const isSpecificationEdit = (row) => {
    if (row.kind === '04' && row.rcjFlag === 1) return true;
    return !isOtherMaterial(row) && isNotCostDe(row?.customParent)
  }



  return {
    hangMenuList,
    hangMenuDisabledHandler,
    DEHangZCSBVisible,
    openDEHangZCSB,
    hangZCSBMenuClick,
    syncToDEVisible,
    isSpecificationEdit,
    addMxqBcRcjData
  }
}
