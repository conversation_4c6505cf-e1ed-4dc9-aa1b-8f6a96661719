/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-06-09 10:33:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-06 09:21:14
 */

import { ref, nextTick } from 'vue';

const clickTimer = ref(null);
const isSelected = ref(false);
const iframeRefs = {};

export const useCellClick = ({ rowKey } = { rowKey: 'sequenceNbr' }) => {
  console.log('进入hooks');
  let currentCellData = ref({});
  let beforeCellEditStatus = ref(false);
  // 编辑触发之前勾子
  const cellBeforeEditMethod = () => {

    return beforeCellEditStatus.value;
  };
  // 是否选中
  const isSelectedCell = ({ $columnIndex, column, row }) => {
    if (!column?.editRender) return false;
    const { columnIndex: cIndex, rowId } = currentCellData.value;
    return row[rowKey] === rowId && $columnIndex === cIndex;
  };
  // 重置当前单元格数据
  const resetCellData = () => {
    isSelected.value = false
    currentCellData.value = {};
    document.removeEventListener('click', handleClickOutside);
  };
  const selectedClassName = ({ column, row, $columnIndex }) => {
    return isSelectedCell({ $columnIndex, row, column }) ? 'cell-selected' : '';
  };
  let lastClickTime = new Date().getTime();
  // 单击  clickEditFields点击一次触发编辑字段
  const useCellClickEvent = (
    cellData,
    callback = null,
    clickEditFields = []
  ) => {
    console.log(cellData);
    const { $table, $columnIndex, row, $event, column } = cellData;
    cellData.dbClickTime = new Date().getTime() - lastClickTime;
    lastClickTime = new Date().getTime();
    if (
      (typeof callback === 'function' && !callback(cellData)) ||
      !column?.editRender ||
      column.editRender?.notCanEdit
    ) {
      // column.editRender?.notCanEdit 为了解决v-for动态渲染表头出现得editRender错误，不可删除
      $table.clearEdit();
      currentCellData.value = {};
      isSelected.value = false
      beforeCellEditStatus.value = false;
      return;
    }
    const rowid = row[rowKey];
    if (!cellData.row.height) {
      // 设置值的地方在项目名称和 项目特征的自定义指令v-maxLineNumber里面，这里只做默认值设置
      cellData.row.height = 36;
    }
    // if (!cellData.row.height) {
    // cellData.row.height = $event.target.closest('tr').clientHeight - 4
    // }
    // 编辑字段执行底下
    let { columnIndex, clickNum, rowId } = currentCellData.value;
    const isSame = $columnIndex === columnIndex && rowId === rowid;
    if (!isSame) {
      $table.clearEdit();
      isSelected.value = false
    }
    currentCellData.value = {
      columnIndex: $columnIndex,
      rowId: rowid,
      clickNum: columnIndex === undefined || !isSame ? 1 : clickNum + 1,
    };
    beforeCellEditStatus.value =
      currentCellData.value.clickNum >= 2 ||
      clickEditFields.includes(column.property);
      setTimeout(() => {
        const input = document.querySelector('.cell-selected input');
        // console.log('输入框内的文字全部被选中',input.selectionStart,input.selectionEnd)
        if(input && !isSelected.value){
          input.select()
          isSelected.value = true
        }
      },10)
    runClickEvent();
  };
  // 双击清除
  const useCellDBLClickEvent = (cellData, callback) => {
    // $table.clearEdit();
    callback(cellData);
  };

  const handleClickOutside = event => {
    if (Object.keys(currentCellData.value).length) {
      console.log('点击body');
      resetCellData();
    }
  };
  let isRunEvent = false;
  const runClickEvent = () => {
    if (isRunEvent) return;
    isRunEvent = true;
    document.addEventListener('click', handleClickOutside);
    const tableAll = document.querySelectorAll('.table-edit-common');
    console.log('cellBeforeEditMethod',document.querySelector('.cell-selected input'))
    tableAll.forEach(table => {
      const bodyTables = table.querySelectorAll('.vxe-table--body');
      bodyTables.forEach(body => {
        body.addEventListener(
          'click',
          e => {
            e.stopPropagation();
          },
          false
        );
      });
    });
  };
  return {
    cellBeforeEditMethod,
    useCellClickEvent,
    useCellDBLClickEvent,
    selectedClassName,
    resetCellData,
    isSelectedCell,
    beforeCellEditStatus,
    currentCellData
  };
};

// 单击
export const useCellClickEvent = (cellData, vexTable, key, callback) => {
  if (!vexTable) return;
  if (!callback(cellData)) return;
  callback(cellData);
  clearTimeout(clickTimer.value);
  clickTimer.value = null;

  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }

  clickTimer.value = setTimeout(() => {
    const $table = vexTable;
    if ($table) {
      $table.setEditCell(cellData.row, cellData.column);
    }
  }, 200);
};

// 双击清除
export const useCellDBLClickEvent = (cellData, vexTable, key, callback) => {
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }
  clearTimeout(clickTimer.value);
  clickTimer.value = null;

  const $table = vexTable;
  $table.clearEdit();
  callback(cellData);
};

// 单击 清除
export const useCellClearClickEvent = (cellData, vexTable, key) => {
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }
  clearTimeout(clickTimer.value);
  clickTimer.value = null;
  const $table = iframeRefs[key];
  const isEditByRow = $table.isEditByRow(cellData.row);
  if (!isEditByRow) {
    $table.clearEdit();
  }
};

// 双击编辑
export const useCellEditDBLClickEvent = ({ row, column }, vexTable, key) => {
  clearTimeout(clickTimer.value);
  clickTimer.value = null;
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }

  clickTimer.value = setTimeout(() => {
    const $table = iframeRefs[key];
    if ($table) {
      $table.setEditCell(row, column);
    }
  }, 200);
};
