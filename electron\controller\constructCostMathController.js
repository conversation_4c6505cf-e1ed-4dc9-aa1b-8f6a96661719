const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");

/**
 *
 */
class ConstructCostMathController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 安文费---一键记取总价措施
     */
    async awfCostMath (arg) {
        const result = await this.service.constructCostMathService.awfCostMath(arg);
        return ResponseData.success(true);
    }

    /**
     * 安文费-----一键记取总价措施-获取总价措施费用分类列表
     */
    async zjcsClassList (arg) {
        const result = await this.service.constructCostMathService.zjcsClassList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安文费-----高台施工增加高度
     */
    async gtfResource () {
        const result = await this.service.constructCostMathService.gtfResource();
        return ResponseData.success(result);
    }



    /**
     * 安文费---明细
     */
    async awfDetails (arg) {
        const result =  this.service.constructCostMathService.awfDetails(arg);
        return ResponseData.success(result);
    }


    /**
     * 安文费总价措施----安文费总价措施参数缓存
     */
    async zjcsCostMathCache (arg) {
        const result = await this.service.constructCostMathService.zjcsCostMathCache(arg);
        return ResponseData.success(result);
    }

    async queryCalculateBaseDropDownList(arg) {
        const result = await this.service.constructCostMathService.queryCalculateBaseDropDownList(arg);
        return ResponseData.success(result);
    }

    /**
     * 总价措施是否有基数定额没参与记取   页面上的红点显示标识
     */
    async queryParticipationZjcsMatch(arg) {
        const result = await this.service.constructCostMathService.queryParticipationZjcsMatch(arg);
        return ResponseData.success(result);
    }

//--------------------------------------------------功能分割线------------------------------------------------------------

    /**
     * 装饰垂直运输----装饰垂运层高下拉框
     */
    async storeyList (args) {
        const result = await this.service.constructCostMathService.storeyList(args);
        return ResponseData.success(result);
    }

    /**
     * 装饰垂直运输----获取标准的垂运清单
     */
    async getCyQd(){
        const result = await this.service.constructCostMathService.getCyQd();
        return ResponseData.success(result);

    }

    /**
     * 装饰垂直运输----获取分部分项 单价措施下指定定额册的定额
     */
    async conditionDeList (arg) {
        const result = await this.service.constructCostMathService.conditionDeList(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰垂直运输----记取位置清单选择列表
     */
    async recordPosition (arg) {
        const result = await this.service.constructCostMathService.recordPosition(arg);
        return ResponseData.success(result);
    }

    /**
     * 装饰垂直运输/超高----判断清单下是否记取定额
     */
    async qdExistDe (arg) {
        const result = await this.service.constructCostMathService.qdExistDe(arg);
        return ResponseData.success(result);
    }



    /**
     * 装饰垂直运输----装饰垂运记取
     */
    async czysCostMath (arg,redo="执行装饰垂运费用计取") {
        const result = await this.service.constructCostMathService.czysCostMath(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰垂直运输----装饰垂运记取参数缓存
     */
    async cyCostMathCache (arg) {
        const result = await this.service.constructCostMathService.cyCostMathCache(arg);
        return ResponseData.success(result);
    }

//--------------------------------------------------功能分割线------------------------------------------------------------


    /**
     * 装饰工程超高----装饰超高层高下拉框
     */
    async cgStoreyList (args) {
        const result = await this.service.zsProjectCgCostMathService.cgStoreyList(args);
        return ResponseData.success(result);
    }





    /**
     * 装饰工程超高----获取标准的超高清单
     */
    async getCgQd(){
        const result = await this.service.zsProjectCgCostMathService.getCgQd();
        return ResponseData.success(result);

    }



    /**
     * 装饰工程超高----记取位置清单选择列表
     */
    async recordPositionCgList (arg) {
        const result = await this.service.zsProjectCgCostMathService.recordPositionCgList(arg);
        return ResponseData.success(result);
    }


    /**
     * 装饰工程超高----装饰超高记取
     */
    async cgCostMath (arg,redo="执行装饰超高费用计取") {
        const result = await this.service.zsProjectCgCostMathService.cgCostMath(arg);
        return ResponseData.success(result);
    }

    /**
     * 装饰工程超高----装饰超高记取缓存
     */
    async cgCostMathCache (arg) {
        const result = await this.service.zsProjectCgCostMathService.cgCostMathCache(arg);
        return ResponseData.success(result);
    }



//--------------------------------------------------功能分割线------------------------------------------------------------


    /**
     * 安装费用----费用记取列表
     */
    async azCostMathList (arg) {
        const result = await this. service.azCostMathService.azCostMathList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----基数定额列表查询
     */
    async baseDeList (arg) {
        const result = await this.service.azCostMathService.baseDeList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----清单列表查询
     */
    async qdList (arg) {
        const result = await this.service.azCostMathService.qdList(arg);
        return ResponseData.success(result);
    }

    /**
     * 安装费用----记取接口
     */
    async azCostMath (arg,redo="执行安装费用记取") {
        const result = await this.service.azCostMathService.azCostMath(arg);
        return ResponseData.success(result);
    }



    /**
     * 安装费用----查询清单列表对应的默认清单的值
     */
    async getDefaultQdValue (arg) {
        const result = await this.service.azCostMathService.getDefaultQdValue(arg);
        return ResponseData.success(result);
    }



    /**
     * 安装费用----安装记取缓存
     */
    async azCostMathCache (arg) {
        const result = await this.service.azCostMathService.azCostMathCache(arg);
        return ResponseData.success(result);
    }

    /**
     * 查询安装的基数定额对应的章节列表
     */
    async queryBaseDeChapter(args) {
        return ResponseData.success(await this.service.azCostMathService.queryBaseDeChapter(args));
    }
}

ConstructCostMathController.toString = () => '[class ConstructCostMathController]';
module.exports = ConstructCostMathController;