<!--
 * @Descripttion: 
 * @Author: kongweiqiang
 * @Date: 2024-11-20 16:01:51
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-02-19 11:00:45
-->
<template>
  <div id="child-modal">
    <div class="modal-header is--draggable is--ellipsis">
      <div class="move"></div>
      <div class="modal-header-title">{{ componentObj.name }}</div>
      <div class="modal-header-right">
        <i
          class="modal-zoom-btn trigger--btn vxe-icon-maximize"
          title="还原"
          v-if="isMax"
          @click="setModalState('unmaximize')"
        ></i
        >
        <i
          class="modal-zoom-btn trigger--btn vxe-icon-square"
          title="最大化"
          v-if="!isMax"
          @click="setModalState('max')"
        ></i
        >
        <i
          class="modal-close-btn trigger--btn vxe-icon-close"
          title="关闭"
          @click="setModalState('hide')"
        ></i>
      </div>
    </div>
    <div style="padding: 10px; height: 100%">
      <component
        ref="modalRef"
        :is="AsyncComponent"
        v-if="params && store"
        :params="params"
        :store="store"
      />
    </div>
  </div>
</template>
<script setup>
import { defineAsyncComponent, reactive, ref, provide, computed,onMounted } from 'vue';
import { useRoute } from 'vue-router';
const { ipcRenderer } = require('electron');
import modalData from '@/modal/modalData';
import redoApi from '@/api/redo';
const modalRef = ref();
const route = useRoute();
const modalType = route.query.type;
const componentObj = modalData.ys.find(item => item.type === modalType);
const AsyncComponent = defineAsyncComponent(componentObj.path);
const params = ref(null);
const store = ref(null);
const  isMax = ref(false);
onMounted(() => {
  window.addEventListener('resize', isMaxFun);
})
const isMaxFun = () => {
  setTimeout(() => {
    let { innerWidth, innerHeight } = window;
    let { availWidth, availHeight } = screen;
    isMax.value = innerWidth === availWidth && innerHeight === availHeight;
  }, 100);
};
const setModalState = showType => {
  if(showType === 'hide' || showType === 'close'){
    modalRef.value.close();
  }
  redoApi
    .setModalState({
      windowId: route.query.constructSequenceNbr,
      type: modalType,
      showType,
    })
    .then(() => {
      if(showType === 'hide' || showType === 'close'){
        params.value = null;
        store.value = null;
      }
      isMaxFun()
    });
};
ipcRenderer.on('getProps', (event, data) => {
  // 在渲染进程中处理数据
  console.log('收到数据：', data);
  params.value = data.props;
  store.value = JSON.parse(data.store);
});
</script>
<style lang="scss" scoped>
#child-modal {
  height: calc(100vh - 50px);
  .move {
    width: calc(100% - 80px);
    height: 50px;
    position: absolute;
    top: 0;
    -webkit-app-region: drag;
  }
  .modal-header {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    font-size: 1.1em;
    font-weight: 700;
    border-bottom: 1px solid var(--vxe-modal-border-color);
    background-color: var(--vxe-modal-header-background-color);
    border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%;
    line-height: 1.5;
    align-items: center;
    padding: 0 60px 0 24px;
    height: 50px;
    background: linear-gradient(
      90deg,
      #3f78ce 0%,
      rgba(51, 131, 252, 0.75) 100%
    );
    &-title {
      position: relative;
      color: #fff;
      text-align: left;
      line-height: 16px;
      flex-grow: 1;
      font-size: 14px;
      padding: 0 0 0 9px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-radius: 2px;
    }
    &-right {
      flex-shrink: 0;
      padding: 0.6em 1em 0.6em 0;
      height: 50px;
      color: #fff;
      line-height: 40px;
      position: absolute;
      top: -3px;
      right: -5px;
    }
  }
  .modal-header::after {
    content: '';
    position: absolute;
    right: 50px;
    top: 0;
    height: 100%;
    max-width: 322px;
    width: 100%;
    z-index: 0;
    background: url(../assets/img/dialog-title-bg.png) no-repeat 100% 0;
    background-size: 322px 50px;
  }
  .modal-header-title::before {
    position: absolute;
    content: '';
    width: 2px;
    height: 50px;
    left: 0;
    top: 0;
    background-color: #fff;
    border-radius: 1px;
  }
  .modal-zoom-btn,
  .modal-close-btn {
    cursor: pointer;
    margin-left: 0.6em;
  }
}
</style>
