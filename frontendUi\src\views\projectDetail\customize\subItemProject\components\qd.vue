<!--
 * @Descripttion: 整理子目 ==》 清单排序
-->
<script setup>
import {onMounted, onUnmounted, ref, toRaw} from 'vue';
import {message, Modal} from 'ant-design-vue';
import api from '@/api/projectDetail.js';
import {projectDetailStore} from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode';
import CommonModal from "@/components/global/commonModal/index.vue";
const loading = ref(false);
const props = defineProps(['subitemIdentification']);
const emits = defineEmits(['update:subitemIdentification', 'updateData']);
const projectStore = projectDetailStore();

const checkboxValue = ref([]);
const checkboxChange = (e) => {
  console.log(e, checkboxValue.value, '1111111111111')
}
//disabled
const disabled = ref(false);
//查询单位清单排序保存状态
const getUnitQdSortFlagColl = async (e) => {
  // let {constructId, singleId, unitId } = args;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id
  };
  try{
    console.log('查询单位清单排序保存状态参数', apiData);
    let res = await api.getUnitQdSortFlagColl(apiData);
    console.log('查询单位清单排序保存状态res', res);
    if (res.status === 200) {
      console.log('Boolean(res.result)', Boolean(res.result));
      disabled.value = !Boolean(res.result); // true 高亮； false 不亮
      // emits('updateData');
    }else{
      message.error(res.message);
    }
  }catch(e){
    console.error('查询单位清单排序保存状态',e);
  }
}
//确认 操作 1.保存清单排序；2.清单排序，两种情况，都会进此 确认 逻辑
//还原三种状态  sort 排序   sortSave保存清单顺序   sortReset 还原清单顺序
const restoreAndSureHandle = async(qdSaveFlag) => {
  if(checkboxValue.value.length > 0 || qdSaveFlag === 'sortReset'){
    try{
      let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        type:''//枚举的字典状态： sort 排序   sortSave保存清单顺序  sortReset 还原清单顺序
      }
      //sort 排序   sortSave保存清单顺序   sortReset 还原清单顺序
      if(qdSaveFlag === 'sortReset'){
        apiData.type = qdSaveFlag;
      }else if(checkboxValue.value.includes('1')){
        apiData.typeList = toRaw(checkboxValue.value);
        apiData.type = 'sortSave';
      }else if(checkboxValue.value.includes('2')){
        apiData.typeList = toRaw(checkboxValue.value);
        apiData.type = 'sort';
      }
      loading.value = true;
      console.log('修改单位保存清单排序参数',apiData);
      let res = await api.qdSortColl(apiData);//清单排序 确认
      console.log('修改单位保存清单排序res', res);
      if (res.status === 200) {
        disabled.value = false;
        emits('updateData');
        console.log('updateData', res);
        cancel();
      }else{
        message.error(res.message);
      }
    }catch(e){
      console.error('修改单位保存清单排序',e);
    }finally{
      loading.value = false;
    }
  }else{
    message.info('请先选择清单排序规则');
  }
}

let popUpStatus = ref(false);
onMounted(() => {
  popUpStatus.value = true;
  console.log('onMounted');
  getUnitQdSortFlagColl();
})
const cancel = () => {
  emits('update:subitemIdentification', '');
  popUpStatus.value = false;
};
</script>

<template>
  <div class="batch-delete">
    <common-modal className="dialog-comm" v-model:modelValue="popUpStatus" title="清单排序" :mask="true" :loading="loading"
                  :lockView="false" :lockScroll="false" width="600px" @cancel="cancel" @close="cancel">
      <div style="width: 100%;height: 373px;border: 1px solid #B9B9B9;padding: 13px 10px;color: #2A2A2A ">
        <a-radio-group v-model:value="checkboxValue" @change="checkboxChange">
          <a-row style="font-size: 14px;line-height: 30px;">
            <a-col :span="24">
              <a-radio value="1" style="margin-right: 0;">保存清单顺序</a-radio>
              <a-tooltip placement="rightTop" color="white">
                <template #title>
                  <span style="color: #000000; font-size: 12px">勾选后可保存修改前清单顺序</span>
                </template>
                <icon-font style="font-size: 12px;" type="icon-tishineirong"/>
              </a-tooltip>
            </a-col>
            <a-col :span="24">
              <a-radio value="2">清单排序</a-radio>
            </a-col>
          </a-row>
        </a-radio-group>
        <a-row style="font-size: 14px;line-height: 24px;margin-top: 5px;">
          <a-col :span="24">
            清单按以下规则执行排序:
          </a-col>
          <a-col :span="24" style="margin-top: 6px;">
            1、按“当前清单一借用清单一补充清单”顺序排序
          </a-col>
          <a-col :span="24">
            2、对当前清单按章节顺字排序
          </a-col>
          <a-col :span="24">
            3、对相同清单按输入的先后顺序排序
          </a-col>
          <a-col :span="24">
            4、对补充清单按编码排序
          </a-col>
        </a-row>
      </div>
      <a-row :span="24">
        <a-col :span="12">
          <div class="btn-list" style="justify-content: flex-start">
            <a-button :disabled="disabled" @click="restoreAndSureHandle('sortReset')">还原清单顺序</a-button>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="btn-list" style="justify-content: flex-end">
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" @click="restoreAndSureHandle()">确定</a-button>
          </div>
        </a-col>
      </a-row>
    </common-modal>
  </div>
</template>

<style scoped lang="scss">
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  width: 100%;

  button + button {
    margin-left: 10px;
  }
}
</style>