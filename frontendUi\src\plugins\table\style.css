@charset "UTF-8";
/*font*/
/*size*/
/*icon*/
/*color*/
/*input/radio/checkbox*/
/*popup*/
/*table*/
/*filter*/
/*menu*/
/*loading*/
/*validate*/
/*grid*/
/*toolbar*/
/*tooltip*/
/*pager*/
/*modal*/
/*checkbox*/
/*radio*/
/*button*/
/*input*/
/*textarea*/
/*form*/
/*select*/
/*switch*/
/*pulldown*/
[class*=vxe-]:after, [class*=vxe-]:before,
[class*=vxe-] *:after, [class*=vxe-] *:before, [class*=vxe-] {
  box-sizing: border-box;
}

.vxe-table--render-default .vxe-header--column.col--ellipsis:not(.col--active) > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--active) > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis:not(.col--active) > .vxe-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

[class*=vxe-] {
  font-variant: tabular-nums;
  font-feature-settings: "tnum";
}
.vxe-primary-color {
  color: var(--vxe-primary-color);
}

.vxe-success-color {
  color: var(--vxe-success-color);
}

.vxe-info-color {
  color: var(--vxe-info-color);
}

.vxe-warning-color {
  color: var(--vxe-warning-color);
}

.vxe-danger-color {
  color: var(--vxe-danger-color);
}

.vxe-perfect-color {
  color: var(--vxe-table-header-background-color);
}

.vxe-row:after {
  content: "";
  display: block;
  clear: both;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
.vxe-row > .vxe-col--1 {
  float: left;
  width: 4.16667%;
}
.vxe-row > .vxe-col--2 {
  float: left;
  width: 8.33333%;
}
.vxe-row > .vxe-col--3 {
  float: left;
  width: 12.5%;
}
.vxe-row > .vxe-col--4 {
  float: left;
  width: 16.66667%;
}
.vxe-row > .vxe-col--5 {
  float: left;
  width: 20.83333%;
}
.vxe-row > .vxe-col--6 {
  float: left;
  width: 25%;
}
.vxe-row > .vxe-col--7 {
  float: left;
  width: 29.16667%;
}
.vxe-row > .vxe-col--8 {
  float: left;
  width: 33.33333%;
}
.vxe-row > .vxe-col--9 {
  float: left;
  width: 37.5%;
}
.vxe-row > .vxe-col--10 {
  float: left;
  width: 41.66667%;
}
.vxe-row > .vxe-col--11 {
  float: left;
  width: 45.83333%;
}
.vxe-row > .vxe-col--12 {
  float: left;
  width: 50%;
}
.vxe-row > .vxe-col--13 {
  float: left;
  width: 54.16667%;
}
.vxe-row > .vxe-col--14 {
  float: left;
  width: 58.33333%;
}
.vxe-row > .vxe-col--15 {
  float: left;
  width: 62.5%;
}
.vxe-row > .vxe-col--16 {
  float: left;
  width: 66.66667%;
}
.vxe-row > .vxe-col--17 {
  float: left;
  width: 70.83333%;
}
.vxe-row > .vxe-col--18 {
  float: left;
  width: 75%;
}
.vxe-row > .vxe-col--19 {
  float: left;
  width: 79.16667%;
}
.vxe-row > .vxe-col--20 {
  float: left;
  width: 83.33333%;
}
.vxe-row > .vxe-col--21 {
  float: left;
  width: 87.5%;
}
.vxe-row > .vxe-col--22 {
  float: left;
  width: 91.66667%;
}
.vxe-row > .vxe-col--23 {
  float: left;
  width: 95.83333%;
}
.vxe-row > .vxe-col--24 {
  float: left;
  width: 100%;
}

/*animat*/
.is--animat .vxe-sort--asc-btn:before, .is--animat .vxe-sort--asc-btn:after,
.is--animat .vxe-sort--desc-btn:before,
.is--animat .vxe-sort--desc-btn:after,
.is--animat .vxe-filter--btn:before,
.is--animat .vxe-filter--btn:after {
  transition: border 0.1s ease-in-out;
}
.is--animat .vxe-input--wrapper .vxe-input {
  transition: border 0.1s ease-in-out;
}
.is--animat .vxe-table--expand-btn,
.is--animat .vxe-tree--node-btn {
  transition: transform 0.1s ease-in-out;
}
.is--animat .vxe-checkbox > input:checked + span,
.is--animat .vxe-radio > input:checked + span {
  transition: background-color 0.1s ease-in-out;
}

/**Variable**/
/*加载中*/
.vxe-loading {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: var(--vxe-loading-background-color);
}
.vxe-loading.is--visible {
  display: block;
}
.vxe-loading > .vxe-loading--chunk, .vxe-loading > .vxe-loading--wrapper {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  text-align: center;
  color: var(--vxe-loading-color);
}
.vxe-loading .vxe-loading--default-icon {
  font-size: 1.4em;
}
.vxe-loading .vxe-loading--text {
  padding: 0.4em 0;
}
.vxe-loading .vxe-loading--spinner {
  display: inline-block;
  position: relative;
  width: 56px;
  height: 56px;
}
.vxe-loading .vxe-loading--spinner:before, .vxe-loading .vxe-loading--spinner:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--vxe-primary-color);
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: bounce 2s infinite ease-in-out;
}
.vxe-loading .vxe-loading--spinner:after {
  animation-delay: -1s;
}
@keyframes bounce {
  0%, 100% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
}

.size--mini .vxe-loading .vxe-loading--spinner {
  width: 38px;
  height: 38px;
}

.size--small .vxe-loading .vxe-loading--spinner {
  width: 44px;
  height: 44px;
}

.size--medium .vxe-loading .vxe-loading--spinner {
  width: 50px;
  height: 50px;
}

.vxe-table--render-default .vxe-header--column.col--ellipsis,
.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column {
  height: var(--vxe-table-row-height-default);
}

.vxe-table--render-default.size--medium .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--medium .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--medium .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--medium .vxe-body--column {
  height: var(--vxe-table-row-height-medium);
}

.vxe-table--render-default.size--small .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--small .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--small .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--small .vxe-body--column {
  height: var(--vxe-table-row-height-small);
}

.vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--mini .vxe-body--column {
  height: var(--vxe-table-row-height-mini);
}

.vxe-table-slots,
.vxe-table--file-form {
  display: none;
}

.vxe-table--print-frame {
  position: fixed;
  bottom: -100%;
  left: -100%;
  height: 0;
  width: 0;
  border: 0;
}

.vxe-table--render-wrapper {
  background-color: var(--vxe-table-body-background-color);
}

.vxe-table--body-wrapper {
  scroll-behavior: auto;
}

.vxe-table--body-wrapper,
.vxe-table--fixed-left-body-wrapper,
.vxe-table--fixed-right-body-wrapper {
  overflow-y: auto;
  overflow-x: auto;
}

/*默认的渲染*/
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea {
  background-color: var(--vxe-table-body-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-select {
  outline: 0;
  padding: 0 2px;
  width: 100%;
  color: var(--vxe-font-color);
  border-radius: var(--vxe-border-radius);
  border: 1px solid var(--vxe-input-border-color);
}
.vxe-cell .vxe-default-input:focus,
.vxe-cell .vxe-default-textarea:focus,
.vxe-cell .vxe-default-select:focus,
.vxe-table--filter-wrapper .vxe-default-input:focus,
.vxe-table--filter-wrapper .vxe-default-textarea:focus,
.vxe-table--filter-wrapper .vxe-default-select:focus {
  border: 1px solid var(--vxe-primary-color);
}
.vxe-cell .vxe-default-input[disabled],
.vxe-cell .vxe-default-textarea[disabled],
.vxe-cell .vxe-default-select[disabled],
.vxe-table--filter-wrapper .vxe-default-input[disabled],
.vxe-table--filter-wrapper .vxe-default-textarea[disabled],
.vxe-table--filter-wrapper .vxe-default-select[disabled] {
  cursor: not-allowed;
  background-color: var(--vxe-input-disabled-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-select {
  height: var(--vxe-input-height-default);
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 4px;
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button, .vxe-cell .vxe-default-input[type=number]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=number]::-webkit-inner-spin-button {
  height: 24px;
}
.vxe-cell .vxe-default-input::-moz-placeholder, .vxe-table--filter-wrapper .vxe-default-input::-moz-placeholder {
  color: var(--vxe-input-placeholder-color);
}
.vxe-cell .vxe-default-input::placeholder,
.vxe-table--filter-wrapper .vxe-default-input::placeholder {
  color: var(--vxe-input-placeholder-color);
}
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-textarea {
  resize: none;
  vertical-align: middle;
}
.vxe-cell .vxe-input,
.vxe-cell .vxe-textarea,
.vxe-cell .vxe-select,
.vxe-table--filter-wrapper .vxe-input,
.vxe-table--filter-wrapper .vxe-textarea,
.vxe-table--filter-wrapper .vxe-select {
  width: 100%;
  display: block;
}
.vxe-cell .vxe-input > .vxe-input--inner,
.vxe-cell .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-wrapper .vxe-input > .vxe-input--inner,
.vxe-table--filter-wrapper .vxe-textarea > .vxe-textarea--inner {
  padding: 0 2px;
}
.vxe-cell .vxe-textarea--inner,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-textarea--inner,
.vxe-table--filter-wrapper .vxe-default-textarea {
  resize: none;
}

.vxe-table--checkbox-range,
.vxe-table--cell-main-area,
.vxe-table--cell-extend-area,
.vxe-table--cell-active-area,
.vxe-table--cell-copy-area {
  display: none;
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.vxe-table--fixed-left-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-right-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area {
  z-index: 2;
}

.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area[half="1"] {
  border-right: 0;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area[half="1"] {
  background-size: var(--vxe-table-cell-copy-area-border-width) 12px, 0 12px, 12px var(--vxe-table-cell-copy-area-border-width), 12px var(--vxe-table-cell-copy-area-border-width);
}

.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area[half="1"] {
  border-left: 0;
}
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area[half="1"] {
  background-size: 0 12px, var(--vxe-table-cell-copy-area-border-width) 12px, 12px var(--vxe-table-cell-copy-area-border-width), 12px var(--vxe-table-cell-copy-area-border-width);
}

/*复选框-范围选择*/
.vxe-table--checkbox-range {
  background-color: var(--vxe-table-checkbox-range-background-color);
  border: var(--vxe-table-checkbox-range-border-width) solid var(--vxe-table-checkbox-range-border-color);
}

.vxe-table--cell-area {
  height: 0;
  font-size: 0;
  display: none;
}
.vxe-table--cell-area > .vxe-table--cell-main-area {
  background-color: var(--vxe-table-cell-area-background-color);
  border: var(--vxe-table-cell-area-border-width) solid var(--vxe-table-cell-area-border-color);
}
.vxe-table--cell-area .vxe-table--cell-main-area-btn {
  display: none;
  position: absolute;
  right: -1px;
  bottom: -1px;
  width: 7px;
  height: 7px;
  border-style: solid;
  border-color: var(--vxe-table-cell-main-area-extension-border-color);
  border-width: 1px 0 0 1px;
  background-color: var(--vxe-table-cell-main-area-extension-background-color);
  pointer-events: auto;
  cursor: crosshair;
}
.vxe-table--cell-area .vxe-table--cell-extend-area {
  border: var(--vxe-table-cell-extend-area-border-width) solid var(--vxe-table-cell-extend-area-border-color);
}

@keyframes moveCopyCellBorder {
  to {
    background-position: 0 -12px, 100% 12px, 12px 0, -12px 100%;
  }
}
.vxe-table--cell-copy-area {
  background: linear-gradient(0deg, transparent 6px, var(--vxe-table-cell-copy-area-border-color) 6px) repeat-y, linear-gradient(0deg, transparent 50%, var(--vxe-table-cell-copy-area-border-color) 0) repeat-y, linear-gradient(90deg, transparent 50%, var(--vxe-table-cell-copy-area-border-color) 0) repeat-x, linear-gradient(90deg, transparent 50%, var(--vxe-table-cell-copy-area-border-color) 0) repeat-x;
  background-size: var(--vxe-table-cell-copy-area-border-width) 12px, var(--vxe-table-cell-copy-area-border-width) 12px, 12px var(--vxe-table-cell-copy-area-border-width), 12px var(--vxe-table-cell-copy-area-border-width);
  background-position: 0 0, 100% 0, 0 0, 0 100%;
  animation: moveCopyCellBorder 0.5s infinite linear;
}

.vxe-table--cell-active-area {
  border: var(--vxe-table-cell-active-area-border-width) solid var(--vxe-table-cell-active-area-border-color);
}

.vxe-table--cell-multi-area > .vxe-table--cell-main-area {
  background-color: var(--vxe-table-cell-area-background-color);
}

/*圆角*/
.vxe-table--render-default.is--round:not(.is--header):not(.is--footer) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round .vxe-table--border-line, .vxe-table--render-default.is--round .vxe-table--render-default.is--round {
  border-radius: var(--vxe-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.body--wrapper {
  border-radius: var(--vxe-table-border-radius) var(--vxe-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-left--wrapper {
  border-radius: var(--vxe-table-border-radius) 0 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-right--wrapper {
  border-radius: 0 var(--vxe-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
  border-radius: 0 0 var(--vxe-table-border-radius) var(--vxe-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-left--wrapper {
  border-radius: 0 0 0 var(--vxe-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-right--wrapper {
  border-radius: 0 0 var(--vxe-table-border-radius) 0;
}
/*header*/
.vxe-table {
  /*排序*/
}
.vxe-table .vxe-table--header-wrapper {
  color: var(--vxe-table-header-font-color);
}
.vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0;
  border-bottom: var(--vxe-table-border-width) solid var(--vxe-table-border-color);
}
.vxe-table .vxe-cell--sort {
  text-align: center;
  position: relative;
  padding: 0 0.1em 0 0.2em;
}
.vxe-table .vxe-cell--sort-vertical-layout {
  display: inline-flex;
  flex-direction: column;
  height: 1.8em;
  vertical-align: middle;
}
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--desc-btn {
  height: 0.6em;
}
.vxe-table .vxe-cell--sort-horizontal-layout {
  display: inline-flex;
  flex-direction: row;
}
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--desc-btn {
  width: 0.5em;
}
.vxe-table .vxe-sort--asc-btn,
.vxe-table .vxe-sort--desc-btn {
  color: var(--vxe-table-column-icon-border-color);
  cursor: pointer;
}
.vxe-table .vxe-sort--asc-btn:hover,
.vxe-table .vxe-sort--desc-btn:hover {
  color: var(--vxe-font-color);
}
.vxe-table .vxe-sort--asc-btn.sort--active,
.vxe-table .vxe-sort--desc-btn.sort--active {
  color: var(--vxe-primary-color);
}

.vxe-header--column {
  position: relative;
  font-weight: var(--vxe-table-header-font-weight);
}
.vxe-header--column.col--ellipsis > .vxe-cell {
  display: flex;
  align-items: center;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-header--column.col--ellipsis > .vxe-cell > i:not(.vxe-cell--title), .vxe-header--column.col--ellipsis > .vxe-cell > span:not(.vxe-cell--title) {
  flex-shrink: 0;
}
.vxe-header--column .vxe-cell--required-icon {
  display: inline-block;
  color: var(--vxe-table-validate-error-color);
  width: 0.8em;
  height: 1em;
  line-height: 1em;
  font-family: var(--vxe-icon-font-family);
  font-weight: normal;
  position: relative;
}
.vxe-header--column .vxe-cell--required-icon:before {
  content: "*";
  position: absolute;
  left: 0;
  top: 0.2em;
}
.vxe-header--column .vxe-cell--required-icon {
  margin-right: 0.1em;
}
.vxe-header--column .vxe-cell--edit-icon,
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
  margin-right: 0.2em;
}
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
  cursor: help;
}
.vxe-header--column .vxe-resizable {
  position: absolute;
  right: -7px;
  bottom: 0;
  width: 14px;
  height: 100%;
  text-align: center;
  z-index: 1;
  cursor: col-resize;
}
.vxe-header--column .vxe-resizable.is--line:before, .vxe-header--column .vxe-resizable.is--line:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
}
.vxe-header--column .vxe-resizable.is--line:before {
  width: 1px;
  height: 50%;
  background-color: var(--vxe-table-resizable-line-color);
}
.vxe-header--column .vxe-resizable.is--line:after {
  width: 0;
  height: 100%;
}

.vxe-table--fixed-right-wrapper .vxe-header--column .vxe-resizable {
  right: auto;
  left: -7px;
}

/*table*/
.vxe-table--render-default {
  position: relative;
  font-size: var(--vxe-font-size);
  color: var(--vxe-font-color);
  font-family: var(--vxe-font-family);
  direction: ltr;
  /*边框*/
  /*列宽线*/
  /*边框线*/
  /*树形节点*/
  /*展开行*/
  /*设置列高度*/
  /*溢出列*/
  /*暂无数据*/
  /*校验不通过*/
  /*已废弃，旧的校验样式**/
  /*单元格标记删除状态*/
  /*单元格编辑状态*/
  /*可编辑*/
}
.vxe-table--render-default .vxe-table--body-wrapper table {
  background-color: var(--vxe-table-body-background-color);
}
.vxe-table--render-default .vxe-table--footer-wrapper table {
  background-color: var(--vxe-table-footer-background-color);
}
.vxe-table--render-default .vxe-table--header,
.vxe-table--render-default .vxe-table--body,
.vxe-table--render-default .vxe-table--footer {
  border: 0;
  border-spacing: 0;
  border-collapse: separate;
  table-layout: fixed;
}
.vxe-table--render-default .vxe-table--header-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
  overflow-x: hidden;
  overflow-y: hidden;
}
.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper {
  overflow-x: scroll;
}
.vxe-table--render-default .vxe-body--row.row--stripe {
  background-color: var(--vxe-table-row-striped-background-color);
}
.vxe-table--render-default .vxe-body--row.row--radio {
  background-color: var(--vxe-table-row-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--checked {
  background-color: var(--vxe-table-row-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--current {
  background-color: var(--vxe-table-row-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover {
  background-color: var(--vxe-table-row-hover-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe {
  background-color: var(--vxe-table-row-hover-striped-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--radio {
  background-color: var(--vxe-table-row-hover-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--checked {
  background-color: var(--vxe-table-row-hover-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--current {
  background-color: var(--vxe-table-row-hover-current-background-color);
}
.vxe-table--render-default.drag--resize .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--resize .vxe-table--fixed-right-wrapper * {
  cursor: col-resize;
}
.vxe-table--render-default.drag--range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-right-wrapper *, .vxe-table--render-default.drag--area .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-right-wrapper * {
  cursor: default;
}
.vxe-table--render-default.drag--extend-range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-right-wrapper * {
  cursor: crosshair;
}
.vxe-table--render-default.column--highlight .vxe-header--column:not(.col--seq):hover {
  background-color: var(--vxe-table-column-hover-background-color);
}
.vxe-table--render-default.cell--area .vxe-table--main-wrapper {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default.drag--range .vxe-cell--checkbox {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-header--column,
.vxe-table--render-default .vxe-body--column,
.vxe-table--render-default .vxe-footer--column {
  position: relative;
  line-height: var(--vxe-table-row-line-height);
  text-align: left;
}
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis) {
  padding: var(--vxe-table-column-padding-default);
}
.vxe-table--render-default .vxe-header--column.col--current,
.vxe-table--render-default .vxe-body--column.col--current,
.vxe-table--render-default .vxe-footer--column.col--current {
  background-color: var(--vxe-table-column-current-background-color);
}
.vxe-table--render-default .vxe-header--column.col--center,
.vxe-table--render-default .vxe-body--column.col--center,
.vxe-table--render-default .vxe-footer--column.col--center {
  text-align: center;
}
.vxe-table--render-default .vxe-header--column.col--right,
.vxe-table--render-default .vxe-body--column.col--right,
.vxe-table--render-default .vxe-footer--column.col--right {
  text-align: right;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--center .vxe-cell {
  justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--right .vxe-cell {
  justify-content: flex-end;
}
.vxe-table--render-default .vxe-table--footer-wrapper {
  border-top: var(--vxe-table-border-width) solid var(--vxe-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper, .vxe-table--render-default.border--full .vxe-table--header-wrapper, .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
  background-color: var(--vxe-table-header-background-color);
}
.vxe-table--render-default.border--default .vxe-header--column,
.vxe-table--render-default.border--default .vxe-body--column,
.vxe-table--render-default.border--default .vxe-footer--column, .vxe-table--render-default.border--inner .vxe-header--column,
.vxe-table--render-default.border--inner .vxe-body--column,
.vxe-table--render-default.border--inner .vxe-footer--column {
  background-image: linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color));
  background-repeat: no-repeat;
  background-size: 100% var(--vxe-table-border-width);
  background-position: right bottom;
}
.vxe-table--render-default.border--full .vxe-header--column,
.vxe-table--render-default.border--full .vxe-body--column,
.vxe-table--render-default.border--full .vxe-footer--column {
  background-image: linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color)), linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color));
  background-repeat: no-repeat;
  background-size: var(--vxe-table-border-width) 100%, 100% var(--vxe-table-border-width);
  background-position: right top, right bottom;
}
.vxe-table--render-default.border--full .vxe-table--fixed-left-wrapper .vxe-body--column {
  border-right-color: var(--vxe-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--full .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--outer .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--inner .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter {
  background-image: linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color));
  background-repeat: no-repeat;
  background-size: 100% var(--vxe-table-border-width);
  background-position: right bottom;
}
.vxe-table--render-default.border--inner .vxe-table--header-wrapper, .vxe-table--render-default.border--none .vxe-table--header-wrapper {
  background-color: var(--vxe-table-header-background-color);
}
.vxe-table--render-default.border--inner .vxe-table--fixed-left-wrapper, .vxe-table--render-default.border--none .vxe-table--fixed-left-wrapper {
  border-right: 0;
}
.vxe-table--render-default.border--inner .vxe-table--border-line {
  border-width: 0 0 1px 0;
}
.vxe-table--render-default.border--none .vxe-table--border-line {
  display: none;
}
.vxe-table--render-default.border--none .vxe-table--header-border-line {
  display: none;
}
.vxe-table--render-default.size--medium {
  font-size: var(--vxe-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-table--empty-placeholder,
.vxe-table--render-default.size--medium .vxe-table--empty-block {
  min-height: var(--vxe-table-row-height-medium);
}
.vxe-table--render-default.size--medium .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--medium .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--medium .vxe-footer--column:not(.col--ellipsis) {
  padding: var(--vxe-table-column-padding-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-select {
  height: var(--vxe-input-height-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 3px;
}
.vxe-table--render-default.size--small {
  font-size: var(--vxe-font-size-small);
}
.vxe-table--render-default.size--small .vxe-table--empty-placeholder,
.vxe-table--render-default.size--small .vxe-table--empty-block {
  min-height: var(--vxe-table-row-height-small);
}
.vxe-table--render-default.size--small .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-footer--column:not(.col--ellipsis) {
  padding: var(--vxe-table-column-padding-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-select {
  height: var(--vxe-input-height-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 2px;
}
.vxe-table--render-default.size--mini {
  font-size: var(--vxe-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-table--empty-placeholder,
.vxe-table--render-default.size--mini .vxe-table--empty-block {
  min-height: var(--vxe-table-row-height-mini);
}
.vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--mini .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--mini .vxe-footer--column:not(.col--ellipsis) {
  padding: var(--vxe-table-column-padding-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-select {
  height: var(--vxe-input-height-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 1px;
}
.vxe-table--render-default .vxe-cell {
  white-space: pre-line;
  word-break: break-all;
  padding-left: var(--vxe-table-cell-padding-left);
  padding-right: var(--vxe-table-cell-padding-right);
}
.vxe-table--render-default .vxe-cell--placeholder {
  color: var(--vxe-table-cell-placeholder-color);
}
.vxe-table--render-default .vxe-cell--radio {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
  font-size: 1.4em;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
  color: var(--vxe-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-cell--radio.is--checked {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--radio.is--checked .vxe-radio--icon {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--radio.is--disabled {
  color: var(--vxe-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--radio.is--disabled .vxe-radio--icon {
  color: var(--vxe-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--label {
  padding-left: 0.5em;
  vertical-align: middle;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  font-size: 1.34em;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  color: var(--vxe-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-cell--checkbox.is--checked, .vxe-table--render-default .vxe-cell--checkbox.is--indeterminate {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox.is--checked .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox.is--indeterminate .vxe-checkbox--icon {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon {
  color: var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox.is--hidden {
  cursor: default;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled {
  color: var(--vxe-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled .vxe-checkbox--icon {
  color: var(--vxe-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--label {
  padding-left: 0.5em;
  vertical-align: middle;
}
.vxe-table--render-default .fixed--hidden {
  visibility: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
  width: 100%;
  position: absolute;
  top: 0;
  z-index: 5;
  overflow: hidden;
  background-color: inherit;
  transition: 0.3s box-shadow;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper {
  overflow-x: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper {
  width: calc(100% + 40px);
}
.vxe-table--render-default.is--header .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper:before,
.vxe-table--render-default.is--header .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper:before {
  display: none;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper {
  left: 0;
  width: 200px;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper.scrolling--middle {
  box-shadow: var(--vxe-table-fixed-left-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
  right: 0;
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper.scrolling--middle {
  box-shadow: var(--vxe-table-fixed-right-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--header-wrapper,
.vxe-table--render-default .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
  position: relative;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper, .vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
  position: absolute;
  top: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper {
  left: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
  right: 0;
  overflow-y: auto;
}
.vxe-table--render-default .vxe-body--x-space {
  width: 100%;
  height: 1px;
  margin-bottom: -1px;
}
.vxe-table--render-default .vxe-body--y-space {
  width: 0;
  float: left;
}
.vxe-table--render-default .vxe-table--resizable-bar {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  z-index: 9;
  cursor: col-resize;
}
.vxe-table--render-default .vxe-table--resizable-bar:before {
  content: "";
  display: block;
  height: 100%;
  background-color: var(--vxe-table-resizable-drag-line-color);
}
.vxe-table--render-default .vxe-table--border-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
  border: var(--vxe-table-border-width) solid var(--vxe-table-border-color);
}
.vxe-table--render-default .vxe-tree--line-wrapper {
  position: relative;
  display: block;
  height: 0;
}
.vxe-table--render-default .vxe-tree--line {
  content: "";
  position: absolute;
  bottom: -0.9em;
  width: 0.8em;
  border-width: 0 0 1px 1px;
  border-style: var(--vxe-table-tree-node-line-style);
  border-color: var(--vxe-table-tree-node-line-color);
  pointer-events: none;
}
.vxe-table--render-default .vxe-cell--tree-node {
  position: relative;
}
.vxe-table--render-default .vxe-tree--btn-wrapper {
  position: absolute;
  top: 50%;
  width: 1em;
  height: 1em;
  text-align: center;
  transform: translateY(-50%);
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
}
.vxe-table--render-default .vxe-tree--node-btn {
  display: block;
  color: var(--vxe-font-lighten-color);
}
.vxe-table--render-default .vxe-tree--node-btn:hover {
  color: var(--vxe-font-color);
}
.vxe-table--render-default .vxe-tree-cell {
  display: block;
  padding-left: 1.5em;
}
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-table--expanded {
  cursor: pointer;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
  display: inline-block;
  width: 1em;
  height: 1em;
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--vxe-font-lighten-color);
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn:hover {
  color: var(--vxe-font-color);
}
.vxe-table--render-default .vxe-table--expanded + .vxe-table--expand-label {
  padding-left: 0.5em;
}
.vxe-table--render-default .vxe-body--expanded-column {
  border-bottom: var(--vxe-table-border-width) solid var(--vxe-table-border-color);
}
.vxe-table--render-default .vxe-body--expanded-column.col--ellipsis > .vxe-body--expanded-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-body--expanded-cell {
  position: relative;
  z-index: 1;
}
.vxe-table--render-default .vxe-body--expanded-cell.is--ellipsis {
  overflow: auto;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis > .vxe-cell {
  max-height: var(--vxe-table-row-height-default);
}
.vxe-table--render-default.size--medium .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--medium .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--medium .vxe-footer--column.col--ellipsis > .vxe-cell {
  max-height: var(--vxe-table-row-height-medium);
}
.vxe-table--render-default.size--medium .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-medium);
}
.vxe-table--render-default.size--small .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--small .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--small .vxe-footer--column.col--ellipsis > .vxe-cell {
  max-height: var(--vxe-table-row-height-small);
}
.vxe-table--render-default.size--small .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-small);
}
.vxe-table--render-default.size--small .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-small);
}
.vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis > .vxe-cell {
  max-height: var(--vxe-table-row-height-mini);
}
.vxe-table--render-default.size--mini .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-mini);
}
.vxe-table--render-default .vxe-table--empty-placeholder,
.vxe-table--render-default .vxe-table--empty-block {
  min-height: var(--vxe-table-row-height-default);
  justify-content: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
  width: 100%;
  pointer-events: none;
}
.vxe-table--render-default .vxe-table--empty-block {
  display: none;
  visibility: hidden;
}
.vxe-table--render-default .vxe-table--empty-placeholder {
  display: none;
  position: absolute;
  top: 0;
  z-index: 5;
}
.vxe-table--render-default .vxe-table--empty-content {
  display: block;
  width: 50%;
  pointer-events: auto;
}
.vxe-table--render-default.is--empty .vxe-table--empty-block,
.vxe-table--render-default.is--empty .vxe-table--empty-placeholder {
  display: flex;
}
.vxe-table--render-default .vxe-body--column.col--selected {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-primary-color);
}
.vxe-table--render-default .vxe-body--column.col--active, .vxe-table--render-default .vxe-body--column.col--selected {
  position: relative;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-cell--valid-error-hint {
  width: 100%;
  position: absolute;
  left: 50%;
  font-size: 12px;
  line-height: 1.2em;
  transform: translateX(-50%);
  text-align: left;
  z-index: 4;
  padding-left: var(--vxe-table-cell-padding-left);
  padding-right: var(--vxe-table-cell-padding-right);
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-cell--valid-error-hint .vxe-cell--valid-error-msg {
  display: inline-block;
  border-radius: var(--vxe-border-radius);
  color: var(--vxe-table-validate-error-color);
  background-color: var(--vxe-table-validate-error-background-color);
  pointer-events: auto;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-input,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-textarea,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-select {
  border-color: var(--vxe-table-validate-error-color);
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-input > .vxe-input--inner {
  border-color: var(--vxe-table-validate-error-color);
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child .vxe-cell--valid-error-hint {
  bottom: 100%;
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child:first-child .vxe-cell--valid-error-hint {
  bottom: auto;
}
.vxe-table--render-default.valid-msg--full .vxe-body--row:last-child .vxe-cell--valid-error-hint {
  top: calc(100% - 1.3em);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column.col--valid-error .vxe-cell--valid-error-hint {
  width: 320px;
  position: absolute;
  bottom: calc(100% + 4px);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  pointer-events: none;
  z-index: 4;
}
.vxe-table--render-default.old-cell-valid .vxe-body--column.col--valid-error .vxe-cell--valid-error-hint .vxe-cell--valid-error-msg {
  display: inline-block;
  border-radius: 4px;
  padding: 8px 12px;
  color: #fff;
  background-color: #f56c6c;
  pointer-events: auto;
}
.vxe-table--render-default.old-cell-valid .vxe-body--row:first-child .vxe-cell--valid-error-hint {
  bottom: auto;
  top: calc(100% + 4px);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column:first-child .vxe-cell--valid-error-hint {
  left: 10px;
  transform: translateX(0);
  text-align: left;
}
.vxe-table--render-default .vxe-body--row.row--pending {
  color: var(--vxe-table-validate-error-color);
  text-decoration: line-through;
  cursor: no-drop;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column {
  position: relative;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0;
  border-bottom: 1px solid var(--vxe-table-validate-error-color);
  z-index: 1;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column {
  position: relative;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column:before {
  content: "";
  top: calc(var(--vxe-table-cell-dirty-width) * -1);
  left: calc(var(--vxe-table-cell-dirty-width) * -1);
  position: absolute;
  border-width: var(--vxe-table-cell-dirty-width);
  border-style: solid;
  border-color: transparent var(--vxe-table-cell-dirty-insert-color) transparent transparent;
  transform: rotate(45deg);
}
.vxe-table--render-default .vxe-body--column.col--dirty {
  position: relative;
}
.vxe-table--render-default .vxe-body--column.col--dirty:before {
  content: "";
  top: calc(var(--vxe-table-cell-dirty-width) * -1);
  left: calc(var(--vxe-table-cell-dirty-width) * -1);
  position: absolute;
  border-width: var(--vxe-table-cell-dirty-width);
  border-style: solid;
  border-color: transparent var(--vxe-table-cell-dirty-update-color) transparent transparent;
  transform: rotate(45deg);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-primary-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active.col--valid-error {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-table-validate-error-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-input,
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-textarea {
  border: 0;
  padding: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-input {
  height: var(--vxe-table-row-line-height);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-input .vxe-input--inner {
  border: 0;
  padding-left: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-textarea {
  height: calc(var(--vxe-table-row-line-height) - 1px);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-textarea .vxe-textarea--inner {
  border: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column {
  padding: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column.col--active {
  padding: 0;
}

/*valid error*/
div.vxe-table--tooltip-wrapper.vxe-table--valid-error {
  padding: 0;
  color: var(--vxe-table-validate-error-color);
  background-color: var(--vxe-table-validate-error-background-color);
}
div.vxe-table--tooltip-wrapper.vxe-table--valid-error.old-cell-valid {
  padding: 8px 12px;
  background-color: #f56c6c;
  color: #fff;
}

/*footer*/
.vxe-table--footer-wrapper {
  color: var(--vxe-table-footer-font-color);
  margin-top: -1px;
}
.vxe-table--footer-wrapper.body--wrapper {
  overflow-x: auto;
}

.vxe-footer--column.col--ellipsis > .vxe-cell {
  display: flex;
  align-items: center;
}
.vxe-footer--column.col--ellipsis > .vxe-cell .vxe-cell--item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}