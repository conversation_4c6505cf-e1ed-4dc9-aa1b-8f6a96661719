<!--
 * @Descripttion: 系统报表
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: sunchen
 * @LastEditTime: 2024-12-23 16:15:01
-->
<template>
  <common-modal
    className="dialog-comm sys-reportcommon"
    v-model:modelValue="dialogVisible"
    title="系统报表"
    @cancel="cancel"
    :width="800"
    :height="600"
    @close="cancel"
  >
    <div class="system-wrap">
      <div class="submit">
        <icon-font
          class="icon-font"
          type="icon-yingyongdaodangqianbaobiao"
        ></icon-font>
        <span class="title">应用到当前报表</span>
      </div>
      <div class="system-box">
        <sysSubAsideTree
          class="aside-tree"
          ref="subAsideTreeRef"
          @onPreview="Preview"
        ></sysSubAsideTree>
        <div class="iframeContent" ref="content" @contextmenu.prevent>
          <iframe
            v-if="fileUrl && PreviewData"
            id="myIframe"
            ref="iframeRef"
            :src="fileUrl"
            style="width: 100%; height: 100%; border: 2px solid #e8e8e7"
          />
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  nextTick,
  markRaw,
  defineExpose,
  onBeforeUnmount,
  defineAsyncComponent,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import projectDetailApi from '@/api/projectDetail';
import systemApi from '@/api/system';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import XEUtils from 'xe-utils';
const sysSubAsideTree = defineAsyncComponent(() =>
  import('./components/sysSubAsideTree.vue')
);

const props = defineProps(['modelValue']);
const emit = defineEmits(['update:modelValue']);
const store = projectDetailStore();
const route = useRoute();
const submitLoading = ref(false);
const dialogVisible = ref(false);
let PreviewData = ref(false); // 预览数据
const fileUrl = ref();

watch(
  () => props.modelValue,
  val => {
    dialogVisible.value = props.modelValue;
  },
  {
    immediate: true,
    deep: true,
  }
);

/**
 * 点击预览
 * @param {*} hasData 是否有报表数据
 */
const Preview = hasData => {
  fileUrl.value = null;
  PreviewData.value = hasData;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html`;
  });
};

const cancel = () => {
  emit('update:modelValue', false);
};

const save = async () => {
  if (!checkedKeys.value || !checkedKeys.value.length) {
    message.error('请选择要导出的工程');
    return;
  }

  try {
    submitLoading.value = true;
    const list = flattenTree(treeData.value)[0];
    const {} = exportPageConfig.isStart;
    const params = {
      lanMuName: useType.value,
      params: JSON.parse(JSON.stringify(list)),
      startPage: exportPageConfig.isStart ? exportPageConfig.startPage : '',
      totalPage: exportPageConfig.isTotal ? exportPageConfig.totalPage : '',
    };
    console.log('导出excel：', params, useTabType.value);
    let apiName = useTabType.value === 'pdf' ? 'exportPdfFile' : 'exportExcel';
    const res = await csProject[apiName](params);
    console.log('🚀 ~ file: exportFile.vue:185 ~ save ~ res:', res);
    if (res?.result) {
      message.success('导出成功！');
      cancel();
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};

defineExpose({
  cancel,
});
</script>
<style lang="scss">
.sys-reportcommon {
  .vxe-modal--content {
    padding: 0 !important;
  }
  .system-wrap {
    width: 100%;
    height: 100%;
    padding: 0 13px 10px 13px;
    .submit {
      display: flex;
      align-items: center;
      padding: 12px 22px;
    }
    .system-box {
      display: flex;
      height: 92%;
      .aside-tree {
        width: 250px;
      }
      .iframeContent {
        flex: 1;
      }
    }
  }
}
</style>
<style lang="scss" scoped></style>
