<!--
 * @Descripttion: 侧边树
 * @Author: renmingming
 * @Date: 2023-05-16 14:18:57
 * @LastEditors: renmingming
 * @LastEditTime: 2024-05-06 13:50:08
-->
<template>
  <div class="expand-menu" v-show="!expand">
    <a-tooltip placement="right">
      <template #title>编辑结构</template>
      <a-button type="text" @click="openVisible"
        ><icon-font type="icon-bianjijiegou"
      /></a-button>
    </a-tooltip>
    <!-- <a-button type="text" @click="test"
			><icon-font type="icon-bianjijiegou" />测试弹窗</a-button
		> -->
    <a-dropdown>
      <a-button type="text" @click.prevent>
        <a-tooltip placement="right">
          <template #title>导入/导出</template>
          <icon-font type="icon-daorudaochu1" />
        </a-tooltip>
      </a-button>
      <template #overlay>
        <a-menu @click="onOperate">
          <a-menu-item key="import">导入项目</a-menu-item>
          <a-menu-item key="export">导出项目</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- <a-divider /> -->
    <!-- <div v-for="row in props.treeData" :key="row.id">
      <span
        ><icon-font :type="getIconType(row.levelType)" />&nbsp;{{
          row.name
        }}</span
      >
    </div> -->
    <vxe-table
      class="expand-table"
      ref="expandVexTable"
      border="none"
      align="left"
      :show-header="false"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'id' }"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'cell',
      }"
      :data="props.treeData"
      :menu-config="rightClickInfo.tableMenu"
      @menu-click="contextMenuClickEvent"
      @current-change="currentChangeEvent"
    >
      <vxe-column
        field="name"
        class-name="sc-tree"
        :edit-render="{ autofocus: '.my-input' }"
      >
        <template #default="{ row }">
          <a-tooltip placement="right">
            <template #title>{{ row.name }}</template>
            <span class="expand-span"
              ><icon-font
                style="font-size: 14px"
                :type="getIconType(row.levelType)"
            /></span>
          </a-tooltip>
        </template>
        <template #edit="{ row }">
          <a-input
            :placeholder="getPlaceholder(row.levelType)"
            v-model:value="row.name"
            type="text"
            @keyup="row.name = removeSpecialChars(row.name)"
            @blur="saveInfo(row)"
            class="my-input"
          />
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <div class="operation-list" v-show="expand">
    <a-button type="text" @click="openVisible"
      ><icon-font type="icon-bianjijiegou" class="icon" />编辑结构</a-button
    >
    <a-dropdown>
      <a-button type="text" @click.prevent
        ><icon-font type="icon-daorudaochu1" class="icon" />导入/导出</a-button
      >
      <template #overlay>
        <a-menu @click="onOperate">
          <a-menu-item key="import">导入项目</a-menu-item>
          <a-menu-item key="export">导出项目</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
  <div class="tree-content" v-show="expand" ref="treeContent">
    <vxe-table
      ref="vexTable"
      border="none"
      align="left"
      :show-header="false"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'id' }"
      :tree-config="{
        children: 'children',
        transform: true,
      }"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'cell',
      }"
      :data="props.treeData"
      @current-change="currentChangeEvent"
      :menu-config="rightClickInfo.tableMenu"
      @menu-click="contextMenuClickEvent"
    >
      <vxe-column
        tree-node
        field="name"
        class-name="sc-tree"
        :edit-render="{ autofocus: '.my-input' }"
      >
        <template #default="{ row }">
          <span
            ><icon-font :type="getIconType(row.levelType)" />&nbsp;{{
              row.name
            }}</span
          >
        </template>
        <template #edit="{ row }">
          <a-input
            :placeholder="getPlaceholder(row.levelType)"
            v-model:value="row.name"
            type="text"
            @keyup="row.name = removeSpecialChars(row.name)"
            @blur="saveInfo(row)"
            class="my-input"
          />
          <!-- <vxe-input
						:clearable="false"
						:placeholder="getPlaceholder(row.levelType)"
						v-model.trim="row.name"
						type="text"
						@keyup="row.name = removeSpecialChars(row.name)"
						@blur="saveInfo(row)"
					></vxe-input> -->
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm"
    width="530px"
    v-model:modelValue="dialogVisible"
    :title="dialogTitle"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :label-col="{ span: 7 }"
      :wrapper-col="{ span: 15 }"
      autocomplete="off"
      @finish="handleOk"
    >
      <template v-if="dialogType === ConstructMenuOperator.unitLevel">
        <a-form-item
          label="单位工程名称"
          name="name"
          :rules="[{ required: true, message: '请输入单位工程名称!' }]"
        >
          <a-input
            :maxlength="50"
            v-model:value.trim="formData.name"
            @input="
              () => {
                formData.name = removeSpecialChars(formData.name);
              }
            "
            placeholder="请输入单位工程名称"
          />
        </a-form-item>
        <a-form-item
          label="工程专业"
          name="type"
          :rules="[{ required: true, message: '请选择工程专业!' }]"
        >
          <a-select
            v-model:value="formData.type"
            placeholder="请选择工程专业"
            :options="engineerMajorList"
            @change="typeChange"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="主定额册名称" name="type" v-if="formData.type">
          <a-select
            v-model:value="formData.libraryCode"
            placeholder="请选择主定额册名称"
            :options="majorTypeDropdownList"
            :fieldNames="{
              label: 'libraryName',
              value: 'libraryCode',
            }"
          >
          </a-select>
        </a-form-item>
        <a-form-item
          label="安装工程二级专业"
          name="type"
          v-if="formData.type === '安装工程'"
        >
          <a-select
            v-model:value="formData.secondInstallationProjectName"
            placeholder="请选择安装工程二级专业"
            :options="secondDropdownList"
            :fieldNames="{ label: 'rateName', value: 'rateName' }"
          >
          </a-select>
        </a-form-item>
      </template>
      <a-form-item
        v-else
        label="单项工程名称"
        name="name"
        :rules="[{ required: true, message: '请输入单项工程名称!' }]"
      >
        <a-input
          :maxlength="50"
          v-model:value.trim="formData.name"
          @input="
            () => {
              formData.name = removeSpecialChars(formData.name);
            }
          "
          placeholder="请输入单项工程名称"
        />
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 7, span: 15 }">
        <a-button @click="dialogVisible = false">取消</a-button>
        <a-button
          style="margin-left: 10px"
          type="primary"
          html-type="submit"
          :loading="submitLoading"
          >确定</a-button
        >
      </a-form-item>
    </a-form>
  </common-modal>
  <edit-project-structure
    v-model:visible="visible"
    :majorList="engineerMajorList"
    @success="handleSuccess"
  ></edit-project-structure>

  <!-- 上传组件 -->
  <!-- <a-upload
    class="file-important"
    name="file"
    :maxCount="1"
    :showUploadList="false"
    :customRequest="uploadFile"
    :before-upload="beforeUpload"
    accept=".ysf"
    v-show="false"
  >
  </a-upload> -->

  <ExportFileAsyncComponent
    v-if="OperateStatus === 'export'"
    @closeDialog="closeDialog"
    ref="ExportFileRef"
  ></ExportFileAsyncComponent>
  <importFileAsyncComponent
    @closeDialog="closeDialog"
    :importList="importList"
    v-if="OperateStatus === 'ImportFileRef'"
  ></importFileAsyncComponent>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  defineAsyncComponent,
  watch,
  watchEffect,
  nextTick,
  computed,
  toRaw,
} from 'vue';
import { useRoute } from 'vue-router';
import { ConstructMenuOperator } from '@/components/editProjectStructure/ConstructMenuOperator';
import csProject from '../../../api/csProject';
import feePro from '../../../api/feePro';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { removeSpecialChars } from '@/utils/index';
import { VXETable } from 'vxe-table';
import { proModelStore } from '@/store/proModel.js';
import infoMode from '@/plugins/infoMode.js';
import operateList from './operate';
import { useCheckProjectBefore } from '@/hooks/useCheckProjectBefore';
const { showInfo, isProjectComplete } = useCheckProjectBefore();
const importList = ref(null);
const test = () => {
  infoMode.show({
    infoText: '确认删除吗',
    descText: '删除后不能复原！',
    confirm: () => {
      console.log('确认事件触发');
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
      console.log('取消事件触发');
    },
  });
};

const ExportFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/exportFile.vue')
); //导出项目组建
const importFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/importFile.vue')
); //导出项目组建

const editProjectStructure = defineAsyncComponent(() =>
  import('@/components/editProjectStructure/index.vue')
);
const route = useRoute();
const constructSequenceNbr = route.query.constructSequenceNbr;
const store = projectDetailStore();
const vexTable = ref();
const expandVexTable = ref();

let visible = ref(false);
let loading = ref(false);
let submitLoading = ref(false);
const proStore = proModelStore();
// 弹框操作
const dialogInfo = {
  [ConstructMenuOperator.singleLevel]: {
    title: '添加单项工程',
  },
  [ConstructMenuOperator.unitLevel]: {
    title: '添加单位工程',
  },
};
let dialogVisible = ref(false);
let dialogTitle = ref('');
let dialogType = ref('');
let engineerMajorList = ref([]);
let majorTypeDropdownList = ref([]);
let secondDropdownList = ref([]);
let currentInfo = ref({});
const props = defineProps(['treeData', 'isExpand']);
const emit = defineEmits(['getTreeList']);
let unifyData = ref(); //统一应用按钮是否禁用
const openVisible = () => {
  visible.value = true;
};

watchEffect(() => {
  if (proStore.openEditModalStatus) {
    openVisible();
    // 编辑弹窗还是自动打开，则关闭
    proStore.onEditModal(false);
  }
});

watch(
  () => props.treeData,
  (newVal, oldVal) => {
    xeUtils.clone(props.treeData).map(i => {
      if (i.name) {
        i.copyName = i.name;
      }
      return i;
    });
    store.projectTree = newVal;

    console.log('newVal', newVal);
    console.log('oldVal', oldVal);
    let change;
    if (newVal && oldVal && newVal.length > oldVal.length) {
      let change = newVal.filter(x => !oldVal.some(y => y.id === x.id));
      if (change) {
        change && setCheckRow(change[0]);
        //默认展开一级节点，所以此处只需要判断添加的是单位展开其父节点
        if (change[0].levelType === 3) {
          let parant = newVal.filter(x => x.id === change[0].parentId);
          console.log('parant', parant);
          setTimeout(() => {
            const $table = vexTable.value;
            $table.setTreeExpand(parant[0], true);
          }, 0);
        }
      }
    } else if (newVal && oldVal && newVal.length < oldVal.length) {
      change = oldVal.filter(x => !newVal.some(y => y.id === x.id))[0];
      const select = newVal.filter(item => item.id === change.parentId);
      select && setCheckRow(select[0]);
    }
  }
);
// 表单
let formData = reactive({
  name: '',
  type: '',
  clickId: '',
  clickLevelType: '',
  libraryCode: '',
  secondInstallationProjectName: '',
});
const expand = computed(() => {
  // console.log(store.$state.currentTreeInfo)
  if (vexTable.value || expandVexTable.value) {
    // setCheckRow();
    vexTable.value.setCurrentRow(store.$state.currentTreeInfo);
    expandVexTable.value.setCurrentRow(store.$state.currentTreeInfo);
  }
  return props.isExpand;
});
const formRef = ref(null);
const setDialogInfo = type => {
  formData = reactive({
    name: '',
    type: '',
    libraryCode: '',
    secondInstallationProjectName: '',
  });
  dialogTitle.value = dialogInfo[type].title;
  dialogType.value = type;

  dialogVisible.value = true;
  formRef.value?.resetFields();
};
const handleOk = () => {
  console.log(formData, '点击确定按钮');
  if (submitLoading.value) return;
  submitLoading.value = true;
  if (dialogType.value === ConstructMenuOperator.singleLevel) {
    addSingle();
  } else {
    addUnit();
  }
};

// 导入导出项目
const OperateStatus = ref(null);
const ExportFileRef = ref(null);
const ImportFileRef = ref(null);

const onOperate = ({ key }) => {
  if (!showInfo()) return;
  switch (key) {
    case 'import':
      upFile();
      break;
    case 'export':
      OperateStatus.value = 'export';
      break;
    default:
      break;
  }
};

// 导入弹窗回调
const closeDialog = e => {
  OperateStatus.value = null;
};

// 上传前置判断
const beforeUpload = file => {
  const fileType = file.name
    .substring(file.name.lastIndexOf('.') + 1)
    .toLowerCase();
  if (!['ysf'].includes(fileType)) {
    message.error('上传文件格式不正确!');
    return false;
  }
  return true;
};

// 选择文件
const upFile = () => {
  csProject.importYsfFile(route.query.constructSequenceNbr).then(res => {
    console.log(
      '🚀 ~ file: AsideTree.vue:497 ~ csProject.importYsfFile ~ res:',
      res
    );
    if (res?.status) {
      message.error(res.message);
    } else {
      importList.value = res;
      OperateStatus.value = 'ImportFileRef';
    }
  });
};

const uploadFile = () => {};

const saveInfo = row => {
  // 移开鼠标清除编辑状态
  const $table = vexTable.value;
  const field = 'name';
  $table.clearEdit();

  row.name = removeSpecialChars(row.name);
  row.name = row.name.trim();
  let value = row.name;
  if (!row.name) {
    row.name = row.copyName;
    return;
  }
  //判断值是否变化
  if (row.name === row.copyName) {
    return;
  }
  //字符长度50限制
  if (row.name.length > 50) {
    row.name = value.slice(0, 50);
  }
  if (row.levelType === ConstructMenuOperator.singleLevel) {
    updateSingleProject(row);
  } else if (row.levelType === ConstructMenuOperator.unitLevel) {
    updateUnitProject(row);
  } else {
    updateConstructProject(row);
  }
  row.copyName = row.name;
};

const updateConstructProject = row => {
  let apiData = {
    constructId: row.id,
    constructName: row.name,
  };
  csProject.editConstructProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      // emit('getTreeList')
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
      store.SET_PROJECT_NAME(row.name);
    }
  });
};
const updateSingleProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.id,
    singleName: row.name,
  };
  csProject.updateSingleProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      // emit('getTreeList')
    }
  });
};

const updateUnitProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.parentId,
    unitId: row.id,
    unitName: row.name,
  };
  csProject.updateUnitProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
      // emit('getTreeList')
    }
  });
};

const deleteTip = (title, content) => {
  new Promise((resolve, reject) => {
    VXETable.modal
      .confirm({
        content: content,
        className: 'dialog-comm confirm-dialog',
        status: 'error',
        title,
        iconStatus: 'vxe-icon-info-circle',
      })
      .then(res => {
        if (res === 'confirm') {
          if (currentInfo.value.levelType === 2) {
            deleteSingleProject();
          } else {
            deleteUnitProject();
          }
          resolve(true);
        } else {
          resolve(false);
        }
      });
  });
};

const getIconType = levelType => {
  const map = {
    1: 'icon-gongchengxiangmu',
    2: 'icon-danxianggongcheng',
    3: 'icon-danweigongcheng1',
  };
  return map[levelType];
};
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : '人材机数据已修改，是否应用整个工程项目?';
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
      infoMode.hide();
    },
    close: () => {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
  // Modal.confirm({
  //   title: `${infoText}`,
  //   onOk() {
  //     store.humanUpdataData.name === 'unify'
  //       ? feeTotalSave(oldVal)
  //       : humanSave(oldVal);
  //   },
  //   onCancel() {
  //     resetHumanData(oldVal);
  //     unifyData.value.disabled = true;
  //   },
  // });
};
const humanSave = oldVal => {
  let apiData = {
    constructId: store.currentTreeInfo?.id,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(store.humanUpdataData.updataData)
    ),
  };
  csProject.changeRcjConstructProject(apiData).then(res => {
    console.log('asideTree统一应用接口返回结果', res);
    if (res.status === 200) {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
    }
  });
};
const feeTotalSave = oldVal => {
  if (store.humanUpdataData.updataData.policy) {
    feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};
const resetHumanData = oldVal => {
  store.SET_HUMAN_UPDATA_DATA(null);
  vexTable.value.setCurrentRow(oldVal.newValue);
  currentChangeEvent(oldVal);
};
const currentChangeEvent = newValue => {
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    console.log(
      '-----------currentChangeEvent',
      store.humanUpdataData.updataData,
      newValue.newValue,
      newValue.oldValue
    );
    vexTable.value.setCurrentRow(newValue.oldValue); //人材机数据有需要保存的，先确定是否要先保存
    saveHumanData(newValue);
    return;
  }
  console.log('newValue', newValue.newValue, newValue.oldValue);
  let obj = {
    constructId: '',
    name: '',
    singleId: '',
    singleName: '',
  };
  if (newValue.row.levelType === 3) {
    props.treeData.forEach(item => {
      if (item.levelType === 1) {
        obj.constructId = item.id;
        obj.name = item.name;
      } else if (item.id === newValue.row.parentId) {
        obj.singleId = item.id;
        obj.singleName = item.name;
      }
    });
  } else if (newValue.row.levelType === 2) {
    props.treeData.forEach(item => {
      obj.constructId = item.id;
      obj.name = item.name;
    });
    obj.singleId = newValue.row.id;
    obj.name = newValue.row.name;
  } else {
    obj.constructId = newValue.row.id;
    obj.name = newValue.row.name;
  }
  console.log('newValue', newValue);
  console.log(store);
  store.SET_CURRENT_TREE_INFO(newValue.row);
  store.SET_CURRENT_TREE_GROUP_INFO(obj);
};

const getPlaceholder = levelType => {
  const map = {
    1: '项目工程',
    2: '单项工程',
    3: '单位工程',
  };
  return `请输入${map[levelType]}名称`;
};

// 右键操作
const menuOperator = new ConstructMenuOperator();

const rightClickInfo = reactive({
  tableMenu: {
    body: {
      options: [[]],
    },
    visibleMethod({ row, type, options }) {
      if (type === 'body') {
        let parent = vexTable.value?.getParentRow(row) || {};
        row.parent = parent;
        menuOperator.resetMenus(row);
        menuOperator.menusJson();
        options.forEach(list => {
          list.forEach(item => {
            item.disabled = !item.isValid;
          });
        });
      }
      return true;
    },
  },
});
rightClickInfo.tableMenu.body.options = [menuOperator.menus];

const contextMenuClickEvent = async ({ menu, row }) => {
  setCheckRow(row);
  const menuLevel = menu.menuLevel;
  currentInfo.value = row;
  if (menuLevel === ConstructMenuOperator.delete) {
    const title = '提示';
    const content =
      row.levelType === ConstructMenuOperator.singleLevel
        ? '是否确定删除，删除后将会将单项工程下关联的所有的数据删除？'
        : '是否删除该单位工程？';
    await deleteTip(title, content);
    return;
  }
  setDialogInfo(menuLevel);
};

const treeContent = ref();
let treeHeight = ref(400);
onMounted(() => {
  treeHeight.value = treeContent.value.clientHeight;
  queryEngineerMajorList();
  setCheckRow(props.treeData[0]); //默认选中第一条数据
  //设置侧边树每一项的copyName
  xeUtils.clone(props.treeData).map(i => {
    if (i.name) {
      i.copyName = i.name;
    }
    return i;
  });
  //默认展开第一项
  setTimeout(() => {
    const $table = vexTable.value;
    let single = props.treeData.find(
      item => item.levelType === 2 && item.children && item.children.length > 0
    );
    single
      ? $table.setTreeExpand([props.treeData[0], single], true)
      : $table.setTreeExpand(props.treeData[0], true); //展开第一个有children的单项
  }, 0);
});

//设置默认选中数据
const setCheckRow = checkRow => {
  let newValue = { row: { ...checkRow } };
  currentChangeEvent(newValue); //将选中的当前行存储信息
  vexTable.value.setCurrentRow(checkRow);
  expandVexTable.value.setCurrentRow(checkRow);
  // console.log( vexTable.value)
  // vexTable.value.setCurrentRow(checkRow)
};

// 添加单项工程
const addSingle = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleName: formData.name,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  csProject
    .addSingleProject(apiData)
    .then(res => {
      console.log('添加单项', res);
      if (res.status === 200) {
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
const init = () => {
  dialogVisible.value = false;
  emit('getTreeList');
};
// 添加单位工程
const addUnit = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleId:
      currentInfo.value.levelType === 3
        ? currentInfo.value.parentId
        : currentInfo.value.levelType === 2
        ? currentInfo.value.id
        : '',
    unitName: formData.name,
    constructMajorType: formData.type,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
    libraryCode: formData.libraryCode,
    secondInstallationProjectName: formData.secondInstallationProjectName,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  if (currentInfo.value.levelType === 3) {
    apiData.oldUnitId = currentInfo.value.id;
  }
  csProject
    .addUnitProject(apiData)
    .then(res => {
      console.log('添加单位', res);
      if (res.status === 200) {
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
//获取专业列表下拉列表
const queryEngineerMajorList = () => {
  csProject.getEngineerMajorList({}).then(function (response) {
    if (response.status === 200) {
      response.result.map(item => {
        engineerMajorList.value.push({
          key: item.sequenceNbr,
          value: item.unitProjectName,
        });
      });
    }
  });
};

// 删除单项工程
const deleteSingleProject = () => {
  csProject
    .delSingleProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};

// 删除单位工程
const deleteUnitProject = () => {
  csProject
    .delUnitProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.parentId,
      unitId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};

const handleSuccess = () => {
  emit('getTreeList');
};

const typeChange = value => {
  formData.secondInstallationProjectName = null;
  if (value === '安装工程') {
    secondInstallationProjectNameByDropdownList();
  }
  constructMajorTypeDropdownList();
};

const constructMajorTypeDropdownList = () => {
  csProject
    .getMainDeLibrary({
      constructMajorType: formData.type,
    })
    .then(res => {
      if (res.status === 200) {
        majorTypeDropdownList.value = res.result;
        majorTypeDropdownList.value.forEach(item => {
          if (item.defaultDeFlag === 1) {
            formData.libraryCode = item.libraryCode;
          }
        });
      }
    });
};

const secondInstallationProjectNameByDropdownList = () => {
  csProject
    .getSecondInstallationProjectName({
      constructMajorType: formData.type,
    })
    .then(res => {
      if (res.status === 200) {
        secondDropdownList.value = res.result;
        const hasDq = res.result.some(i => {
          return i.rateName === '电气设备安装工程';
        });
        if (hasDq) {
          formData.secondInstallationProjectName = '电气设备安装工程';
        }
      }
    });
};

defineExpose({ setCheckRow });
</script>

<style lang="scss" scoped>
.expand-menu {
  padding-top: 5px;
  width: 47px;
  height: 100%;
  background-color: white;
  :deep(.ant-btn) {
    position: relative;
    left: -1px;
  }
}
.expand-table {
  margin-top: 10px;
  background-color: #f8fbff;
}
.expand-span {
  display: block;
  text-align: center;
}
.sc-tree {
  .vxe-cell {
    .dropdown-context-menu {
      display: flex;
    }
    .ant-dropdown-trigger {
      flex: 1;
    }
  }
}

.dropdown-context-menu {
  :deep(.ant-dropdown) {
    .disabled {
      background: #dfdfdf;
    }
  }
}

.operation-list {
  display: flex;
  align-items: center;
  height: 42px;
  border-bottom: 2px solid #e8ecf1;
  background: #f2f6fc;
  overflow: hidden;
  button {
    font-size: 12px;
    .icon {
      font-size: 14px;
    }
  }
  :deep(.ant-btn) {
    padding: 4px 10px;
  }
  :deep(.ant-btn > .anticon + span, .ant-btn > span + .anticon) {
    margin-left: 3px;
  }
}

.tree-content {
  padding: 10px 0 0;
  height: calc(100% - 42px);
  width: 225px;
  // overflow-y: auto;
  // background: #f2f6fc;
  // &::-webkit-scrollbar {
  //   width: 6px;
  //   height: 6px;
  // }
  // &::-webkit-scrollbar-track {
  //   border-radius: 3px;
  //   background: rgba(0, 0, 0, 0.06);
  //   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.08);
  // }
  // /* 滚动条滑块 */
  // &::-webkit-scrollbar-thumb {
  //   border-radius: 3px;
  //   background: rgba(0, 0, 0, 0.12);
  //   -webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
  // }
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  background: #f2f6fc;
  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    //滚动条的设置
    background-color: var(--table-webkit-scrollbar-thumb);
    background-clip: padding-box;
    min-height: 28px;
    border-radius: 5px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--table-webkit-scrollbar-thumb-hover);
  }
  :deep(.vxe-table) {
    background: transparent;
    font-size: 14px;
    .vxe-table--body-wrapper {
      background-color: transparent;
      table {
        background-color: transparent;
      }
    }
    .vxe-body--column {
      // padding: 5px 0;
      cursor: pointer;
      padding: 0px 0 !important;
      margin-bottom: 5px;
      height: 24px !important ;
    }
    .vxe-icon-caret-right {
      color: var(--primary-color);
    }
    .row--current {
      background-color: #deeaff;
    }
  }
}
</style>
