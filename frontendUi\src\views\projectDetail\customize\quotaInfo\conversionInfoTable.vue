<!--
 * @Descripttion: 换算信息
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-17 11:29:48
-->
<template>
  <div class="head">
    <a-button
      type="text"
      @click="updateDeConversionInfo('up')"
      :disabled="!currentInfo || Number(currentInfo?.sortNumber.split('.')[0]) === 1">
      <icon-font type="icon-biaodan-charu"></icon-font>
      上移
    </a-button>
    <a-button
      type="text"
      @click="updateDeConversionInfo('down')"
      :disabled="
        !currentInfo || Number(currentInfo?.sortNumber.split('.')[0]) === props.tableData.length
      ">
      <icon-font type="icon-biaodan-charu"></icon-font>
      下移
    </a-button>
    <a-button type="text" @click="updateDeConversionInfo('delete')" :disabled="isDelete">
      <icon-font type="icon-biaodan-shanchu"></icon-font>
      删除
    </a-button>
  </div>
  <div class="content">
    <vxe-table
      ref="vexTable"
      :data="props.tableData"
      height="auto"
      class="table-scrollbar"
      :tree-config="{
        children: 'children',
        expandAll: true,
      }"
      :row-style="rowStyle"
      :row-config="{ isCurrent: true, keyField: 'sortNumber' }"
      @current-change="currentChangeEvent"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData, null);
        }
      "
      @keydown="keyDownHandler">
      <vxe-column width="60" field="sortNumber"></vxe-column>
      <vxe-column title="换算串" field="conversionString" tree-node></vxe-column>
      <vxe-column title="说明" field="conversionExplain"></vxe-column>
      <vxe-column title="来源" field="source"></vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import api from '@/api/projectDetail';

import { useCellClick } from '@/hooks/useCellClick.js';
import { projectDetailStore } from '@/store/projectDetail.js';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } = useCellClick();

const props = defineProps(['tableData', 'currentInfo']);
const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();
const currentInfo = ref(null);
let vexTable = ref(null);

const isDelete = computed(() => {
  return !currentInfo.value || currentInfo.value.kind === 6;
});
const rowStyle = ({ row }) => {
  if (row.kind === 6) {
    return { backgroundColor: '#FFBE88' };
  }
  return {};
};

// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};
const keyDownHandler = event => {
  let code = event.$event.code;
  if (code == 'Delete') {
    updateDeConversionInfo('delete');
  }
};
const updateDeConversionInfo = operate => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: operate,
  };
  console.log('apiData', apiData);
  api.updateDeConversionInfo(apiData).then(res => {
    console.log('res11111', res);
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};
defineExpose({
  vexTable,
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: calc(100% - 40px);
}
</style>
