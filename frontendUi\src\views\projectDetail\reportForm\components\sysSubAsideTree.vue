<!--
 * @Descripttion: 报表左侧
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-24 17:31:27
-->
<template>
  <div class="aside-wrap-content">
    <div class="aside-wrap">
      <div class="list-item" v-for="(v, k) in treeList" :key="k">
        <div class="mode-title" @click="changeItem(k)">
          <down-outlined
            class="icon-down"
            :class="{ 'rotate-icon': useKey !== k }"
          />
          <icon-font class="icon-font" type="icon-xiangmu"></icon-font>
          <span class="label">{{ v.desc }}</span>
        </div>
        <a-tree
          v-if="useKey === k"
          v-model:expandedKeys="expandedKeys"
          treeDefaultExpandAll="true"
          v-model:selectedKeys="selectedKeys"
          :tree-data="v.baoBiaoList"
          :fieldNames="{
            children: 'children',
            title: 'headLine',
            key: 'headLine',
          }"
          class="report-forms-tree"
          show-icon
          @select="clickTree"
        >
          <template #switcherIcon="{ switcherCls }"
            ><down-outlined class="sub-icon-down" :class="switcherCls"
          /></template>

          <template #title="{ headLine, isManual }">
            <a-tooltip placement="rightTop">
              <template #title>{{ headLine }}</template>
              <span class="ellipsis"> {{ headLine }}</span>
            </a-tooltip>
          </template>

          <template #icon="{ children }">
            <template v-if="children && children.length">
              <icon-font class="icon-font" type="icon-baobiao"></icon-font>
            </template>
          </template>
        </a-tree>
      </div>
    </div>
  </div>
</template>

<script setup>
import { DownOutlined } from '@ant-design/icons-vue';
import csProject from '@/api/csProject';
import XEUtils from 'xe-utils';
import { markRaw, ref, watchEffect, watch, toRaw, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useRoute } from 'vue-router';
import {
  globalData,
  removeLocalStorage,
  setLocalStorage,
} from './../reportFrom';

const emit = defineEmits(['onPreview', 'onSelectReport']);
const props = defineProps({
  storageName: {
    type: String,
    default: 'reportForm',
  },
});

const route = useRoute();
const store = projectDetailStore();
let expandedKeys = ref([]);
let selectedKeys = ref([]);
const treeList = ref([]);
const useKey = ref(0);
const itemLevelList = markRaw(['project', 'single', 'unit']);

let currentTreeInfo = ref(null); // 当前节点信息

let posData = ref(''); //定位
// 获取左侧菜单数据
const getTreeList = (pos = '') => {
  posData.value = pos;
  const { constructObj, itemLevel } = JSON.parse(
    JSON.stringify(globalData.treeParams)
  );
  csProject.moreTemplateQuery({ ...constructObj, itemLevel }).then(res => {
    removeLocalStorage(props.storageName);
    treeList.value = res.result;
    // setLocalStorage(JSON.stringify(res.result), 'baoBiaoTreeList');
    setSheetData();
  });
};

// 切换，更新最右侧的表单
const setSheetData = async () => {
  const list = treeList.value[useKey.value];
  if (posData.value) {
    const headLine =
      posData.value === 'last'
        ? list?.baoBiaoList.at(-1)?.headLine
        : posData.value;
    selectedKeys.value = [headLine];
    // globalData.headLine = headLine;
  }

  emit('onSelectReport', {
    isOpen: false,
    lanMuName: list.desc,
  });

  if (list) {
    const TreeArrayData = XEUtils.toTreeArray(list.baoBiaoList).filter(i => {
      return i.headLine === selectedKeys.value[0];
    });
    if (TreeArrayData && TreeArrayData[0]) {
      const preview = toRaw(TreeArrayData[0]);
      const data = await getShowSheetStyle(preview);
      currentTreeInfo.value = {
        ...TreeArrayData[0],
        lanMuName: treeList.value[useKey.value].desc,
      };
      emit('onPreview', {
        hasData: !!data,
        currentTreeInfo: currentTreeInfo.value,
      });
      // globalData.excelDataTemplate = TreeArrayData[0]?.excelDataTemplate;

      data
        ? setLocalStorage(data, props.storageName)
        : removeLocalStorage(props.storageName);
    } else {
      emit('onPreview', {
        hasData: false,
        currentTreeInfo: currentTreeInfo.value,
      });
    }
  }
};

const init = XEUtils.debounce(() => {
  let params = {
    itemLevel: 'project',
    constructObj: {
      constructId: route.query?.constructSequenceNbr,
      singleId: null,
      unitId: null,
    },
  };
  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    params.itemLevel = itemLevelList[levelType - 1];

    switch (levelType) {
      case 1:
        params.constructObj.constructId = route.query?.constructSequenceNbr;
        params.constructObj.singleId = null;
        break;
      case 2:
        params.constructObj.singleId = store.currentTreeInfo.id;
        params.constructObj.unitId = null;
        break;
      case 3:
        params.constructObj.singleId = store.currentTreeInfo?.parentId;
        params.constructObj.unitId = store.currentTreeInfo.id;
        break;
    }
  }
  // globalData.treeParams = params;
  getTreeList();
}, 300);

watch(
  () => store.currentTreeInfo,
  () => {
    init();
  },
  {
    immediate: true,
  }
);

const list = {
  ZB: 0,
  TB: 1,
  DW: 2,
};
useKey.value = list[store.projectType] || 0;

const changeItem = k => {
  if (k === useKey.value) {
    useKey.value = -1;
    return;
  } else {
    useKey.value = k;
    selectedKeys.value = [];
    expandedKeys.value = [];
  }

  emit('onSelectReport', {
    isOpen: false,
    lanMuName: treeList.value[useKey.value].desc,
  });
};

const clickTree = async (keys, { node }) => {
  currentTreeInfo.value = {
    ...node.dataRef,
    lanMuName: treeList.value[useKey.value].desc,
  };
  const nodeKey = node.dataRef.headLine;
  // globalData.headLine = nodeKey;
  console.log('🚀 ~ clickTree ~ globalData.headLine:', currentTreeInfo.value);
  // globalData.excelDataTemplate = node.dataRef?.excelDataTemplate;

  if (!node.expanded) {
    expandedKeys.value.push(nodeKey);
  } else {
    expandedKeys.value = expandedKeys.value.filter(key => {
      return key !== nodeKey;
    });
  }

  if (!node?.children) {
    const data = await getShowSheetStyle(node.dataRef);
    emit('onPreview', {
      hasData: !!data,
      currentTreeInfo: currentTreeInfo.value,
    });
    data
      ? setLocalStorage(data, props.storageName)
      : removeLocalStorage(props.storageName);
  }
};

// 获取表单数据
const getShowSheetStyle = sheetInfo => {
  return new Promise((resolve, reject) => {
    let postData = {
      itemLevel: globalData.treeParams.itemLevel,
      lanMuName: treeList.value[useKey.value].desc,
      sheetName: sheetInfo.headLine,
      constructObj: {
        ...toRaw(globalData.treeParams.constructObj),
        jsonData: toRaw(currentTreeInfo.value?.excelDataTemplate),
      },
      sheetType: sheetInfo.sheetType,
    };
    console.log('🌶sysSubAsideTree.vue|257====>', postData);
    csProject.showSheetStyle(postData).then(res => {
      if (res.code == 200) {
        resolve(res.result);
      }
    });
  });
};

defineExpose({ getTreeList });
</script>
<style lang="scss" scoped>
.list-item {
  margin-bottom: 8px;
}

.icon-down {
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  transition: all 0.2s;
}

.rotate-icon {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}
.sub-icon-down {
  color: rgba(178, 177, 177, 1);
  font-size: 13px;
}
.mode-title {
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
  .icon-font {
    margin: 0 4px 0 6px;
    font-size: 15px;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #131414;
  }
}
.aside-wrap-content {
  width: 100%;
  background-color: #f8fbff;
  overflow: hidden;
  padding: 0 0 8px 0;
  display: flex;
  flex-direction: column;
  .aside-wrap {
    flex: 1;
    flex-shrink: 0;
    padding: 8px 4px;
    overflow-y: auto;
  }
  :deep(.report-forms-tree) {
    width: 100%;
    position: relative;
    background-color: #f8fbff;
    .ant-tree-node-selected {
      background-color: #dae7f4;
    }
    .ant-tree-treenode {
      width: 100%;
      overflow: hidden;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      display: flex;
      overflow: hidden;
    }
    .ant-tree-title {
      width: 100%;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.tab-bar {
  width: 100%;
  height: 37px;
  padding-left: 20px;
  border-bottom: 1px solid rgba(214, 214, 214, 1);
  display: flex;
  align-items: center;
  .add-report {
    display: flex;
    align-items: center;
    cursor: pointer;
    .icon {
      font-size: 16px;
    }
    .add-name {
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      margin-left: 7px;
      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
