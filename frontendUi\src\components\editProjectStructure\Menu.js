/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-23 11:29:54
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-04-09 15:49:18
 */
export const MenuLevel = {
  One: 1,
  Two: 2,
  Three: 3,
  Four: 4,
  Five: 5,
  Six: 6,
  Seven: 7,
  Delete: 9999,
  BatchDelete: 9998,
  TwoToFive: 2.5,
  Copy: 66,
  CopyTo: 66.5,
  Paste: 67,
  BatchModify: 100,
  OptionLock: 110,
  QuickNewUnit: 120,
  Rename: 130
};

export class Menu {
  _name;
  _isValid;
  _menuLevel;

  constructor(name, isValid, menuLevel) {
    this._name = name;
    this._isValid = isValid;
    this._menuLevel = menuLevel;
  }

  get name() {
    return this._name;
  }

  set name(value) {
    this._name = value;
  }

  get isValid() {
    return this._isValid;
  }

  set isValid(value) {
    this._isValid = value;
  }

  get menuLevel() {
    return this._menuLevel;
  }

  set menuLevel(value) {
    this._menuLevel = value;
  }
}

export class MenuOperator {
  _menus;

  constructor() {
    this._menus = [];
  }

  addMenu(menu) {
    this._menus.push(menu);
  }

  get menus() {
    return this._menus;
  }
  menusJson() {
    return this.menus.map(item => {
      return {
        name: item.name,
        isValid: item.isValid,
        menuLevel: item.menuLevel,
      };
    });
  }
}
