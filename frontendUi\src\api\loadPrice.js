/*
 * @Author: wangru
 * @Date: 2023-05-17 09:28:32
 * @LastEditors: wangru
 * @LastEditTime: 2024-10-29 15:30:30
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export default {
  /**
   * 批量载价获取地区列表
   * @param {*} params
   * @returns
   */
  queryLoadPriceAreaDate(params) {
    return ipc.invoke(ipcApiRoute.queryLoadPriceAreaDate, params);
  },

  doBatchLoadingPrice(params) {
    return ipc.invoke(ipcApiRoute.loadingPrice, params);
  },

  updateLoadPrice(params) {
    return ipc.invoke(ipcApiRoute.updateLoadPrice, params);
  },
  applyLoadingPriceInRcjDetails(params) {
    return ipc.invoke(ipcApiRoute.applyLoadingPriceInRcjDetails, params);
  },

  loadPriceEditPage(params) {
    return ipc.invoke(ipcApiRoute.loadPriceEditPage, params);
  },
  loadPriceList(params) {
    return ipc.invoke(ipcApiRoute.loadPriceList, params);
  },

  clearLoadPrice(params) {
    return ipc.invoke(ipcApiRoute.clearLoadPrice, params);
  },

  queryLoadPriceReportTarget(params) {
    return ipc.invoke(ipcApiRoute.queryLoadPriceReportTarget, params);
  },

  queryLoadPriceReportRcj(params) {
    return ipc.invoke(ipcApiRoute.queryLoadPriceReportRcj, params);
  },
  loadPriceStatus(params) {
    return ipc.invoke(ipcApiRoute.loadPriceStatus, params);
  },
  loadPriceStatusOriginal(params) {
    return ipc.invoke(ipcApiRoute.loadPriceStatusOriginal, params);
  },
  getRcjTypeTree(params) {
    return ipc.invoke(ipcApiRoute.getRcjTypeTree, params);
  },
  getZtzjRcj(params) {
    return ipc.invoke(ipcApiRoute.getZtzjRcj, params);
  },
  useZtzjRcj(params) {
    return ipc.invoke(ipcApiRoute.useZtzjRcj, params);
  },
  getDimRegion(params) {
    return ipc.invoke(ipcApiRoute.getDimRegion, params);
  },

  saveAvgRule(params) {
    return ipc.invoke(ipcApiRoute.saveAvgRule, params);
  },
  getAvgRule(params) {
    return ipc.invoke(ipcApiRoute.getAvgRule, params);
  },
  getZtzjRcjAvg(params) {
    return ipc.invoke(ipcApiRoute.getZtzjRcjAvg, params);
  },
  getLoadPriceCache(params) {
    return ipc.invoke(ipcApiRoute.getLoadPriceCache, params);
  },
  queryUnitConversion(params) {
    return ipc.invoke(ipcApiRoute.queryUnitConversion, params);
  },
};
