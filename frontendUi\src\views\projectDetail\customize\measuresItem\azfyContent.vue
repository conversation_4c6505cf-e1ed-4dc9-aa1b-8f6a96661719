<!--
 * @@Descripttion: 安装费用
 * @Author: wangru
 * @Date: 2023-08-04 15:56:53
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-19 17:47:17
-->
<template>
  <div>
    <div class="table-content tableFirst">
      <div class="upContent">
        <div class="upContent-left">
          <p class="selectContent">
            <a-button
              type="primary"
              style="display: inline-block; margin: -10px 20px 0 0"
              @click="revertData"
              ref="target"
            >恢复系统默认</a-button>
            <a-button
              type="primary"
              style="display: inline-block; margin:-10px 20px 0 0"
              @click="advancedModel = true"
              ref="target"
            >高级选项</a-button>
            <span>高层建筑高</span>
            <a-input-number
              v-model:value="layerInterval"
              style="width: 100px"
              :min="0"
              :max="9999"
              ref="target"
              :disabled="!isEdit"
              @keyup="updateTableList"
            >
              <template #addonBefore>
                <span
                  @click="queryData('layerInterval++')"
                  class="btnAdd"
                >+</span>
              </template>
              <template #addonAfter>
                <span
                  @click="queryData('layerInterval--')"
                  class="btnSub"
                >-</span>
              </template>
            </a-input-number>
            <span class="fontsingle">层，</span>
            <span>或</span>
            <a-input-number
              v-model:value="heightRange"
              style="width: 100px"
              :min="0"
              :max="9999"
              :disabled="!isEdit"
              @keyup="updateTableList"
            >
              <template #addonBefore>
                <span
                  @click="queryData('heightRange++')"
                  class="btnAdd"
                >+</span>
              </template>
              <template #addonAfter>
                <span
                  @click="queryData('heightRange--')"
                  class="btnSub"
                >-</span>
              </template>
            </a-input-number>
            <span class="fontsingle">米 </span>
          </p>
          <div class="upContent-left-table">
            <vxe-table
              :column-config="{ resizable: true, isCurrent: true }"
              :row-config="{ isCurrent: true ,height: 25}"
              :data="tableDataFirst"
              height="100%"
              ref="upTable"
              keep-source
              :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            showLine: true,
            showIcon: true,
            expandAll: true,
            iconOpen: 'icon-caret-down',
            iconClose: 'icon-caret-right',
            }"
              @current-change="currentChange"
              @cell-click="cellClickEvent"
              :cell-class-name="cellClassName"
              :row-class-name="rowClassNameFirstTable"
              show-overflow
            >
              <vxe-column
                field="idx"
                width="50"
                title="序号"
              >
                <template #default="{ row }">
                  <span v-if="!['az','fwxs'].includes(row.id)">{{ row.idx-1 }}</span>
                  <span v-else></span>

                </template>
              </vxe-column>
              <vxe-column
                field="feeName"
                width="310"
                title="总价措施名称"
                align="left"
                tree-node
              >
                <template #default="{ row }">
                  <span style="margin-left: 20px;">{{ row.feeName }}</span>
                </template>
              </vxe-column>
              <vxe-column
                field="type"
                min-width="200"
                title="计取方式"
                align="left"
              >
                <template #default="{ row }">
                  <span
                    style="display: block;padding: 0 10px;"
                    v-if="!['az','fwxs'].includes(row.id)"
                  >
                    {{typeList.find(a=>a.classCode===row.type)?.className}}
                    <span
                      style="float:right;margin: auto;width:17px;border-radius: 5px;cursor:pointer;border: 1px transparent solid;"
                      @click="openSelectTypeModal(row)"
                    >
                      ...
                    </span>
                  </span>
                  <span v-else>
                  </span>
                </template>
              </vxe-column>
              <vxe-column
                field="baseDeScope"
                min-width="170"
                title="基数定额计取范围"
              >
                <template #default="{ row }">
                  <span
                    class="detailTitle"
                    v-if="!['az','fwxs'].includes(row.id)"
                  >{{ row.baseDeScope }}</span>
                  <vxe-button
                    v-if="!['az','fwxs'].includes(row.id)"
                    status="primary"
                    class="detailBtn"
                    content="详情"
                    @click="setlocationModel(row)"
                  ></vxe-button>
                  <span v-else></span>
                </template>
              </vxe-column>
              <vxe-column
                field="isCheck"
                min-width="80"
                title="是否计取"
              >
                <template #default="{ row }">
                  <vxe-checkbox
                    v-if="!['az','fwxs'].includes(row.id)"
                    v-model="row.isCheck"
                    :checked-value="true"
                    :unchecked-value="false"
                    :disabled="!isEdit"
                  ></vxe-checkbox>
                  <span v-else></span>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
        <div class="asideTree">
          <p class="asideTree-title">
            应用范围：
            <a-button
              size="small"
              type="primary"
              @click="selectSameMajor"
              style="margin-top: -3px;"
            >
              <span style="font-size:12px;">
                选择同专业
              </span>
            </a-button>
          </p>
          <div class="asideTree-content">
            <a-tree
              v-model:checkedKeys="rightInfo.checkedKeys"
              v-model:expandedKeys="rightInfo.expandedKeysRight"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              checkable
              :tree-data="treeData"
              :blockNode="true"
            >
              <template #title="{ levelType, id, name, whetherNew, children }">
                <a-tooltip placement="leftTop">
                  <template #title>{{ name }}</template>
                  <span class="check-labels">{{ name }}</span>
                </a-tooltip>
              </template>
            </a-tree>
          </div>
        </div>
      </div>
      <vxe-table
        v-if="tableNo2Loading"
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true ,height:25}"
        :data="tableDataSecond"
        height="180"
        class="tableNo2"
        ref="tableNo2"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
        @edit-closed="editClosedEvent"
        keep-source
        show-overflow="tooltip"
        :merge-cells="mergeCells"
        :cell-class-name="cellClassNameCum"
        @current-change="tableRowChange"
        @cell-click="cellClickDownEvent"
      >
        <vxe-column
          field="classLevel1Name"
          min-width="110"
          title="定额分册"
        >
        </vxe-column>
        <vxe-column
          field="isDefaultRow.classLevel2"
          min-width="75"
          title="对应章节"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            {{row.isDefaultRow.classLevel2}}
          </template>
          <template #edit="{ row }">
            <span style="float:left;width:calc(100% - 20px); white-space: nowrap;overflow:hidden;text-overflow: ellipsis;">
              {{row.isDefaultRow.classLevel2}}
            </span>
            <span
              style="float:right;margin: auto;width:17px;border-radius: 5px;cursor:pointer;border: 1px gray solid;"
              @click="openSelectCom(row)"
            >
              ...
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="isDefault"
          min-width="285"
          title="对应当前单位措施定额"
        >
          <template #default="{ row }">
            <vxe-select
              v-model="row.isDefault"
              transfer
              @change="selectChange(row, 'isDefault')"
              placeholder="请选择对应当前单位措施定额"
            >
              <vxe-option
                v-for="item in row.deList"
                :key="item.sequenceNbr"
                :value="item.sequenceNbr"
                :label="item.deName"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column
          field="isDefaultRow.allocationMethod"
          min-width="95"
          title="基数分摊方式"
        >
          <template #header="{ column }">
            <a-tooltip
              placement="rightTop"
              color="white"
            >
              <template #title>
                <div
                  style="color: #000000; font-size: 12px"
                  class="tooltip"
                >
                  <p>
                    {{currentRow.id.indexOf('az')!==-1?'其中机械设备安装工程超高费按非分摊计取，其余费用按照分摊计取（默认）':'各项费用按照分摊计取（默认）'}}

                  </p>
                </div>
              </template>
              基数分摊方式<icon-font
                type="icon-bangzhu"
                style="margin:0 5px;"
              ></icon-font>
            </a-tooltip>
          </template>
          <template #default="{ row }">
            <vxe-select
              v-model="row.isDefaultRow.allocationMethod"
              transfer
              @change="selectChange(row, 'allocationMethod')"
              placeholder="请选择基数分摊方式"
            >
              <vxe-option
                v-for="item in allocationMethodList"
                :key="item.classCode"
                :value="item.classCode"
                :label="item.className"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column
          field="isDefaultRow.calculateBase"
          min-width="140"
          title="计算基数"
        >
          <template #header="{ column }">
            <a-tooltip
              placement="rightTop"
              color="white"
            >
              <template #title>
                <div
                  style="color: #000000; font-size: 12px"
                  class="tooltip"
                >
                  <p>
                    分摊计取时，取安装费用基数中的人工费+机械费之和乘以乘以相应系数计算（默认）
                  </p>
                </div>
              </template>
              计算基数选择<icon-font
                type="icon-bangzhu"
                style="margin:0 5px;"
              ></icon-font>
            </a-tooltip>
          </template>
          <template #default="{ row }">
            <span v-if="+row.isDefaultRow.allocationMethod === 0">
              分别按人、材、机取费
            </span>
            <vxe-select
              v-if="+row.isDefaultRow.allocationMethod === 1"
              v-model="row.isDefaultRow.calculateBase"
              transfer
              @change="selectChange(row, 'calculateBase')"
              placeholder="请选择计算基数"
            >
              <vxe-option
                v-for="item in calculateBaseList"
                :key="item.classCode"
                :value="item.classCode"
                :label="item.className"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column
          field="isDefaultRow.rate"
          min-width="60"
          title="费率"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span v-if="row.isDefaultRow.allocationMethod === 0"> - </span>
            <span v-if="row.isDefaultRow.allocationMethod !== 0">
              {{ row.isDefaultRow.rate }}
            </span>
          </template>
          <template #edit="{ row }">
            <span v-if="row.isDefaultRow.allocationMethod === 0"> - </span>
            <vxe-input
              :clearable="false"
              v-model="row.isDefaultRow.rate"
              v-if="row.isDefaultRow.allocationMethod !== 0"
              @blur="
                  (row.isDefaultRow.rate = pureNumber(
                    row.isDefaultRow.rate,
                    2
                  )),
                    clear()
                "
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-colgroup title="其中">
          <vxe-column
            field="isDefaultRow.rRate"
            min-width="60"
            title="人工%"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.isDefaultRow.rRate"
                @blur="
                    clear(),
                      (row.isDefaultRow.rRate = pureNumber(
                        row.isDefaultRow.rRate,
                        2
                      ))
                  "
              ></vxe-input> </template></vxe-column>
          <vxe-column
            field="isDefaultRow.cRate"
            min-width="60"
            title="材料%"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.isDefaultRow.cRate"
                @blur="
                    (row.isDefaultRow.cRate = pureNumber(
                      row.isDefaultRow.cRate,
                      2
                    )),
                      clear()
                  "
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column
            field="isDefaultRow.jRate"
            min-width="60"
            title="机械%"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.isDefaultRow.jRate"
                @blur="
                    (row.isDefaultRow.jRate = pureNumber(
                      row.isDefaultRow.jRate,
                      2
                    )),
                      clear()
                  "
              ></vxe-input>
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-column
          field="isDefaultRow"
          width="205"
          title="具体清单"
          header-align="center"
          align="left"
        >
          <template #default="{ row }">
            <a-tooltip
              placement="top"
              v-if="
                  currentRow.type !== 4 ? row.isDefaultRow.showQdName : ''
                "
            >
              <template #title>
                {{
                    currentRow.type !== 4 ? row.isDefaultRow.showQdName : ''
                  }}
              </template>
              <span class="detailQD">{{
                  currentRow.type !== 4 ? row.isDefaultRow.showQdName : ''
                }}</span>
            </a-tooltip>
            <vxe-button
              v-if="!(outputType === 1 && currentRow.type === 0)"
              class="detailBtn"
              status="primary"
              content="详情"
              @click="getDetailQD(row)"
              :disabled="currentRow.type === 4"
            ></vxe-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <p
      class="btnsOut"
      style="width: 200px !important"
    >
      <a-button
        type="primary"
        @click="emits('close')"
        ghost
      >取消</a-button>
      <a-button
        type="primary"
        @click="submit()"
        :loading="submitLoading"
      >确定</a-button>
    </p>
  </div>

  <common-modal
    title="基数定额计取范围设置"
    width="750"
    height="550"
    className="dialog-comm"
    v-model:modelValue="locationModel"
  >
    <div style="height:100%">
      <p class="selectContent">
        <span>安装专业：</span>
        <a-select
          v-model:value="deBookValue"
          :size="size"
          style="width: 230px; margin-right: 20px"
          :options="deBookList"
          :field-names="{ label: 'label', value: 'value' }"
          placeholder="请选择安装专业"
          @change="selectChange({}, 'deBookValue')"
        ></a-select>
        <span>章节筛选：</span>
        <a-select
          v-model:value="chapterValue"
          :size="size"
          style="width: 230px"
          :options="chapterList"
          :field-names="{ label: 'label', value: 'value' }"
          placeholder="请选择章节"
          @change="selectChange({}, 'chapterValue')"
        ></a-select>
      </p>
      <div class="tableNo3">
        <div class="tableNo3-table">
          <vxe-table
            align="center"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true, height: 30 }"
            :data="tableDataThird"
            height="auto"
            width="450"
            ref="tableNo3"
            class='table-line'
            :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            line: true,
            showIcon: true,
            expandAll: true,
            iconOpen: 'icon-caret-down',
            iconClose: 'icon-caret-right'
          }"
            :expand-config="{
              expandAll:true
            }"
            :row-class-name="rowClassName"
            :cell-class-name="cellClassNameTableNo3"
            show-overflow
            keep-source
          >
            <vxe-column
              field="bdCode"
              width="20%"
              title="项目编码"
              tree-node
            >
            </vxe-column>
            <vxe-column
              field="type"
              width="10%"
              title="类型"
            > </vxe-column>
            <vxe-column
              field="name"
              width="30%"
              title="名称"
            > </vxe-column>
            <vxe-column
              field="projectAttr"
              width="30%"
              title="项目特征"
            >
              <template #header>
                <span>项目特征<icon-font
                    type='icon-a-fangdasuoxiao1'
                    class='projectAttrIcon'
                    @click="changeLineHeight"
                  ></icon-font></span>
              </template>
              <template #default="{ column, row, $columnIndex }">
                <div
                  class="nameEdit"
                  v-if="row.kind == '03'"
                >
                  <pre
                    class="pre-name"
                    v-html="row.projectAttr"
                  ></pre>
                </div>
                <span v-else></span>
              </template>
            </vxe-column>
            <vxe-column
              field="isCheck"
              width="10%"
              title="是否计取"
            >
              <template #default="{ row }">
                <vxe-checkbox
                  v-if="!(row.kind==='0' && !isShowUnit)"
                  v-model="row.isCheck"
                  :checked-value="true"
                  :unchecked-value="false"
                  @change="selectChange(row, 'table3check')"
                ></vxe-checkbox>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <p class="footer">
          <a-radio-group
            v-model:value="radioListValue"
            class="radioList"
            @change="radioChange"
          >
            <a-radio :value="1">全选</a-radio>
            <a-radio :value="2">反选</a-radio>
          </a-radio-group>
          <span class="btnList">
            <a-button
              @click="locationModel = false"
              style="margin-right: 20px"
            >取消</a-button>
            <a-button
              type="primary"
              @click="sureLocation()"
            >确定</a-button>
          </span>
        </p>
      </div>
    </div>

  </common-modal>
  <common-modal
    title="指定具体清单"
    width="750"
    height="510"
    className="dialog-comm"
    v-model:modelValue="detailModel"
  >
    <div class="table-content tableNo4">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, height: 30 }"
        :data="tableDataFourth"
        height="auto"
        width="430"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          expandAll: true,
        }"
        :row-class-name="rowClassName"
        show-overflow
        ref="tableNo4"
      >
        <vxe-column
          field="fxCode"
          min-width="180"
          title="项目编码"
          tree-node
        >
        </vxe-column>
        <vxe-column
          field="type"
          min-width="80"
          title="类型"
        > </vxe-column>
        <vxe-column
          field="name"
          min-width="180"
          title="名称"
        > </vxe-column>
        <vxe-column
          field="isCheck"
          min-width="180"
          title="计取至该清单"
        >
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.isCheck"
              :checked-value="true"
              :unchecked-value="false"
              @change="selectChange(row, 'qdisCheck')"
              :disabled="row.type !== '清'"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <p
      class="btns"
      style="width: 200px !important"
    >
      <a-button
        type="primary"
        @click="detailModel = false"
        ghost
      >取消</a-button>
      <a-button
        type="primary"
        @click="sureJQ()"
      >确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="选择计算范围"
    width="550"
    height="450"
    className="dialog-comm"
    v-model:modelValue="selectCompute"
  >
    <div class="ComputeContent">
      <s-table
        size="small"
        ref="selectComTable"
        class="s-table"
        bordered
        :expandedRowKeys="expandedRowKeys"
        :columns="tableOptions.columns"
        :scroll="{ y: 280 }"
        :animateRows="false"
        :pagination="false"
        rowKey="idx"
        :data-source="tableOptions.data"
        :loading="comTableLoading"
        :row-height="35"
        :rowSelection="{
            checkStrictly:false,
            hideSelectAll:true,
            selectedRowKeys: defineCheckRowKeys,
            onChange: onSelectChange,
            onSelect:setSelectIScheck
          }"
        childrenColumnName="childrenList"
        :custom-cell="customCell"
        :rowClassName="(row, index) => rowClassNameTree(row, index, tableOptions.data)"
      >
        <template #expandIcon="props">
          <span
            v-if="props.record.childrenList?.length > 0"
            class="cell-line-break-el"
          >
            <div
              v-if="props.expanded"
              style="display: inline-block; "
              @click="expandFun(1,props.record,props)"
            >
              <i class="ag-vxe-icon-caret-down"></i>
            </div>
            <div
              v-else
              style="display: inline-block; "
              @click="
              expandFun(2,props.record,props)"
            >
              <i class="ag-vxe-icon-caret-right"></i>
            </div>
          </span>
          <span
            v-else
            style="margin-right:29px"
          ></span>
        </template>
        <template #bodyCell="{
              text,
              record: row,
              index,
              column,
              key,
              openEditor,
              closeEditor,
            }">
        </template>
      </s-table>
    </div>
    <p class="selectRightBtn">
      <a-button
        @click="selectBtnChange('1')"
        style="margin-right: 20px"
      >全选</a-button>
      <a-button
        @click="selectBtnChange('2')"
        style="margin-right: 20px"
      >反选</a-button>
    </p>
    <p class="selectBtn">
      <a-button
        @click="selectCompute = false"
        style="margin-right: 20px"
      >取消</a-button>
      <a-button
        type="primary"
        :disabled="defineCheckRowKeys?.length===0"
        @click="sureSelectCompute()"
      >确定</a-button>
    </p>
  </common-modal>
  <info-modal
    v-model:infoVisible="infoVisible"
    :infoText="infoText"
    :isSureModal="isSureModal"
    :iconType="iconType"
    @updateCurrentInfo="updateCurrentInfo"
  ></info-modal>
  <info-modal
    v-model:infoVisible="editInfo"
    infoText="费用分摊项应该在0~100之间，并且和等于100，请重新输入"
    :isSureModal="true"
    @update:infoVisible="close"
    iconType="icon-querenshanchu"
    :isFunction="false"
  ></info-modal>
  <common-modal
    title="高级选项"
    width="450"
    height="300"
    className="dialog-comm"
    v-model:modelValue="advancedModel"
  >
    <div class="advanced-content">
      <div class="single">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-jieyongzimuanzhuangfeiyongjiqufangshi"
          ></icon-font>借用子目安装费用计取方式
        </div>
        <vxe-checkbox
          v-model="borrowRule"
          :checked-value="true"
          :unchecked-value="false"
        >使用借用的库的安装费用规则</vxe-checkbox>
      </div>
      <div class="single">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-anzhuangfeiyongshuchufangshi"
          ></icon-font>安装费用输出方式
        </div>
        <a-radio-group
          v-model:value="outputType"
          class="radioList"
          @change="radioChange"
        >
          <a-radio :value="1">清单费用按每个分部分别计取</a-radio>
          <a-radio :value="2">清单费用按整个工程统一修改</a-radio>
        </a-radio-group>
      </div>
    </div>

    <p class="btns">
      <a-button
        @click="advancedModel = false"
        style="margin-right: 20px"
      >取消</a-button>
      <a-button
        type="primary"
        @click="advancedSure"
      >确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="计取位置"
    width="370"
    height="220"
    :position="{ top: 'calc(50% - 110px)', left: 'calc(50% - 150px)' }"
    className="dialog-comm"
    v-model:modelValue="selectTypeModal"
    @close="closeTypeModal"
  >
    <div class="select-type">
      <p>
        <span>计取位置：</span>
        <!-- <a-radio-group
          v-model:value="selectTypeValue.type"
          class="radioList"
          @change="selectChange(selectTypeValue, 'type')"
          v-for="item in typeList"
        >
          <a-radio :value="item.classCode">{{ item.className }}</a-radio>
        </a-radio-group> -->
        <a-select
          v-model:value="selectTypeValue.type"
          :size="'small'"
          style="width: 280px"
          :options="typeList"
          :field-names="{ label: 'className', value: 'classCode' }"
          placeholder="请选择章节"
          @change="changeSelectType"
        ></a-select>
      </p>
      <p class="select-type-jtx">
        <span>具体项：</span>
      <p class="select-type-jtx-ipt">
        <a-tooltip :title="selectTypeValueQd.relationList">
          <span class="select-type-jtx-ipt-text">{{ selectTypeValueQd.relationList }}</span>
        </a-tooltip>
        <span
          :class="selectTypeValue.type===4||(outputType === 1 && selectTypeValue.type === 0)?'select-type-jtx-ipt-disSpan':'select-type-jtx-ipt-span'"
          @click="getDetailQD(null)"
        >···
        </span>
      </p>
      </p>
    </div>

    <p class="btns">
      <a-button
        type="primary"
        @click="selectTypeSure"
        :disabled="(selectTypeValue.type===3||(!outputType === 1 && selectTypeValue.type === 0))&&!selectTypeValueQd.relationList"
      >确定</a-button>
    </p>
  </common-modal>
</template>
<script setup>
import { constructLevelTreeStructureList } from '@/api/csProject';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import { pureNumber } from '@/utils/index';
import {
  nextTick,
  onMounted,
  reactive,
  ref,
  shallowReactive,
  shallowRef,
  watch,
  watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import xeUtils from 'xe-utils';
import api from '../../../../api/projectDetail';

const route = useRoute();
const store = projectDetailStore();
const target = ref(null); //点击输入框之外的地方可进行查询
let locationModel = ref(false); //基数定额详情弹框
let detailModel = ref(false); //具体清单详情弹框
let layerInterval = ref(0); //高层建筑高-层
let heightRange = ref(0); //高层建筑高-米
let radioListValue = ref(1); //全选，反选--单选按钮
const emits = defineEmits(['updateData', 'close']);
let deBookList = ref([]); //安装工程下拉框
let chapterList = ref([]); //章节下拉框
let deBookValue = ref(); //默认安装工程下拉框选中值
let chapterValue = ref(); //默认章节下拉框下拉框选中值
let tableDataThird = ref([]); //基数定额详情表格数据
let tableNo3 = ref(); //基数定额记取弹框
let tableNo4 = ref(); //具体清单
let tableNo2 = ref(); //下表格
let tableDataFourth = ref([]); //具体清单弹框data
let changeList = ref([]); //弹框改变的数据
let currentRow = ref(); //上表格选中行
let oldlayerInterval = ref(0); //高层建筑高-层
let oldheightRange = ref(0); //高层建筑高-米
let table2DetailRow = ref(); //下表格选中行的详情
let infoVisible = ref(false); // 提示信息框是否显示
let infoText = ref(''); // 提示信息框的展示文本
let iconType = ref(''); // 提示信息框的图标
let isSureModal = ref(false); // 提示信息框是否为确认提示框
let table2current = ref(); //下表格选中行数据
let isEdit = ref(true); //是否可以编辑分摊三种费率
let editInfo = ref(false);
let submitLoading = ref(false); //点击确定按钮loading
let defaultDownData = ref(); //默认展示的下表格数据
let cgCacheData = ref(); //缓存数据
let upTableCheckList = ref([]); //上表格选中数据
let selectCompute = ref(false); //章节选择范围
let selectTypeModal = ref(false); //选择计取位置弹框
let selectTypeValue = ref(null);
let selectTypeValueQd = reactive({
  relationListId: null, //默认清单id
  relationList: null, //默认清单id+名字
  // //编辑选中清单信息
  // selectQdId: null,
  // selectQdName: null,
}); //计取位置具体项value
const upTable = ref();
let tableDataFirst = ref([]);
let tableDataSecond = ref([]);
let mergeCells = ref(null);
let mergeCellsUpTable = ref(null); //上表格数据合并处理--存在全国统一安装工程预算定额河北省消耗量定额（2012）树结构
//高级设置弹参数
let advancedModel = ref(false); // 高级选项弹框是否展示
let borrowRule = ref(true); // 使用借用的库的安装费用规则
let outputType = ref(2); // 安装费用输出方式
const advancedSure = () => {
  getTableDownData();
  advancedModel.value = false;
};
let oldSelectType = null;
const openSelectTypeModal = row => {
  selectTypeModal.value = true;
  currentRow.value = row;
  selectTypeValue.value = row;
  oldSelectType = row.type;
  refreshTypeValueQD(); //刷新具体项
};
const initSelectQdValue = () => {
  //初始化selectTypeValueQd
  for (let key in selectTypeValueQd) {
    selectTypeValueQd[key] = null;
  }
};
const closeTypeModal = () => {
  //关闭计取位置弹框--右上角关闭，此时type恢复至原来
  selectTypeValue.value.type = oldSelectType;
  selectTypeModal.value = true;
};
const changeSelectType = () => {
  initSelectQdValue();
  selectChange(selectTypeValue.value, 'type');
};
const selectTypeSure = () => {
  selectTypeModal.value = false;
  if (selectTypeValueQd.relationListId && selectTypeValueQd.relationList) {
    //新的选中项和旧清单不一样需要重置
    tableDataSecond.value.map(b => {
      setDowntableQd(b, selectTypeValueQd.relationListId, selectTypeValueQd.relationList);
    });
    tableDataSecond.value.map(item => {
      saveJqResult(upTable.value.getCurrentRecord(), item);
    });
    //存储此时对应的下表格数据
    let newValue = tableDataSecond.value;
    getLastDefaultDownDataRow(currentRow.value, newValue);
  }
};
let initDownData = ref([]); //初始表格数据值
const cellClassNameCum = ({ column, row, $columnIndex }) => {
  let className = ' ';
  let lineRow = row.isDefaultRow;
  let secondTableData = initDownData.value?.find(
    item =>
      item.feeCode === currentRow.value.feeCode &&
      item.id === currentRow.value.id
  );
  let currentZhangjie = secondTableData?.classLevelList.find(
    item =>
      item.classLevel1Name == row.classLevel1Name &&
      (item.classLevel2Name == row.classLevel2NameOld ||
        item.classLevel2Name == row.classLevel2Name ||
        item.classLevelCgCache == row.classLevel2Name)
  );
  if (!currentZhangjie) {
    currentZhangjie = secondTableData?.classLevelList.find(
      item =>
        item.classLevel1Name == row.classLevel1Name &&
        item.classLevelCgCache == row.classLevel2NameOld
    );
  }
  const currentLine = currentZhangjie?.deList.find(
    item => item.sequenceNbr === row.isDefault
  );
  if (!currentLine) {
    return;
  }
  let fieldList = {
    'isDefaultRow.calculateBase': 'calculateBase',
    'isDefaultRow.allocationMethod': 'allocationMethod',
    'isDefaultRow.rate': 'rate',
    'isDefaultRow.rRate': 'rRate',
    'isDefaultRow.cRate': 'cRate',
    'isDefaultRow.jRate': 'jRate',
  };
  for (let key in fieldList) {
    if (column.field === key) {
      //isDefaultRow.calculateBase为字符串   其余字段可能为数据需要转化一下
      let flag = key === 'isDefaultRow.calculateBase';
      if (
        (flag && lineRow[fieldList[key]] != currentLine[fieldList[key]]) ||
        (!flag && +lineRow[fieldList[key]] != +currentLine[fieldList[key]])
      ) {
        // console.log(lineRow[fieldList[key]], currentLine[fieldList[key]]);
        className += ' redColor';
      }
    }
  }
  return className;
};
const setMergeCells = () => {
  //名称相同的行合并
  let sameIndex = -1;
  // let sameList = [];
  let mergeList = [];
  tableDataSecond.value.forEach((item, index) => {
    if (index > sameIndex) {
      let flag = mergeList.filter(a => a.name === item.classLevel1Name);
      let target = flag && flag[flag.length - 1];
      if (target && target.idx === index - 1) {
        target.rowspan += 1;
        target.idx += 1;
      } else {
        mergeList.push({
          name: item.classLevel1Name,
          idx: index,
          row: index,
          col: 0,
          rowspan: 1,
          colspan: 1,
        });
      }
      sameIndex++;
    }
  });
  mergeCells.value = mergeList;
};
const close = () => {
  tableNo2.value.setCurrentRow(table2current.value);
  editInfo.value = false;
};
let isEditInput = ref(false); //是否编辑建筑层高等
let table1current = ref(null); //表格1选中数据
//改变高层建筑层高和米数，实时查询数据展示
const updateTableList = () => {
  if (
    oldlayerInterval.value !== layerInterval.value ||
    oldheightRange.value !== heightRange.value
  ) {
    console.log('updateTableList');
    upTableCheckList.value = [];
    tableDataFirst.value.map(item => {
      upTableCheckList.value.push({
        id: item.id,
        type: item.type,
        feeCode: item.feeCode,
        isCheck: item.isCheck,
      });
    });
    table1current.value = upTable.value.getCurrentRecord();
    getTableUpData(true, true);
    oldlayerInterval.value = layerInterval.value;
    oldheightRange.value = heightRange.value;
  }
};

const typeList = reactive([
  //记取方式列表
  {
    className: '指定措施清单',
    classCode: 3,
  },
  {
    className: '指定分部分项清单',
    classCode: 0,
  },
  {
    className: '对应分部分项清单',
    classCode: 4,
  },
]);
const allocationMethodList = reactive([
  //基数分摊方式列表
  {
    className: '非分摊计取',
    classCode: 0,
  },
  {
    className: '分摊计取',
    classCode: 1,
  },
]);
const calculateBaseList = reactive([
  //计算基数下拉列表
  {
    className: '人工费',
    classCode: 'RGF',
  },
  {
    className: '机械费',
    classCode: 'JXF',
  },
  {
    className: '材料费',
    classCode: 'CLF',
  },
  {
    className: '人工费+机械费',
    classCode: 'RGF+JXF',
  },
  {
    className: '人工费+材料费',
    classCode: 'RGF+CLF',
  },
  {
    className: '机械费+材料费',
    classCode: 'JXF+CLF',
  },
  {
    className: '人工费+机械费+材料费',
    classCode: 'RGF+CLF+JXF',
  },
  // {
  //   className: '人工费+机械费+材料费',
  //   classCode: 'RGF，CLF，JXF',
  // },
  // {
  //   className: '人工费+机械费+材料费',
  //   classCode: 'RGF+CLF+JXF',
  // },
]);

//点击恢复系统默认按钮
const revertData = () => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '确认恢复系统默认吗？',
    confirm: () => {
      layerInterval.value = 0;
      heightRange.value = 0;
      getTableUpData(true);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
onMounted(() => {
  getList(); //右侧同专记取专业树
  getTableUpData();
});

let tableNo2Loading = ref(true);
let pos = reactive({
  scrollLeft: 0,
  scrollTop: 0,
});
let downCurrenRow = ref();
let isInit = ref(false);
// 处理合并数据
const handleMerge = () => {
  let mergeCells = [];
  tableDataSecond.value.forEach((item, index) => {
    mergeCells.push({
      row: index,
      col: 4,
      rowspan: 1,
      colspan: !item.isDefaultRow.allocationMethod ? 2 : 1,
    });
  });

  if (!isInit.value) {
    isInit.value = true;
    setTimeout(() => {
      tableNo2.value?.setMergeCells(mergeCells);
    }, 500);
  } else {
    tableNo2Loading.value = false;
    nextTick(() => {
      tableNo2Loading.value = true;
      setTimeout(() => {
        tableNo2.value?.setMergeCells(mergeCells);
        tableNo2.value?.setCurrentRow(downCurrenRow.value);
        tableNo2.value?.scrollTo(pos.scrollLeft, pos.scrollTop);
      }, 100);
    });
  }
};

watchEffect(() => {
  if (tableDataSecond.value.length > 0) {
    handleMerge();
  }
});

const clear = () => {
  //清除编辑状态
  const $table = tableNo2.value;
  $table?.clearEdit();
};
const getTableUpData = (reset = false, isEdit = false) => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  isEditInput.value = isEdit;
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    layerInterval: Number(layerInterval.value),
    heightRange: Number(heightRange.value),
  };
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  // console.log('列表数据getTableUpData传参', formData);
  api.azCostMathList(formData).then(res => {
    if (res.status === 200 && res.result) {
      res.result = res.result.filter(
        a => a.viewFlag
        // || a.libraryCode === store.currentTreeInfo.deLibrary
      );
      res.result = xeUtils.toTreeArray(res.result);
      let index = 1;
      res.result.map(a => {
        if (['az', 'fwxs'].includes(a.id)) {
          index = 1;
          a.idx = index;
          a.type = null;
          a.baseDeScope = null;
          a.isCheck = null;
          index++;
        }
        if (a.parentId && !a.id) {
          a.id = `${a.parentId}${a.feeCode}`;
          a.idx = index;
          index++;
        }
        a.sequenceNbr = a.id;
      });
      console.log('安装费用-缓存数据', res);
      defaultDownData.value = JSON.parse(JSON.stringify(res.result));
      initDownData.value = JSON.parse(JSON.stringify(res.result));
      defaultList(); //处理默认数据
      //获取是否有缓存数据
      api.azCostMathCache(apiData).then(a => {
        if (a.status === 200) {
          console.log('安装费用-缓存数据', a);
          let list = [];
          if (a.result) {
            list = [...a.result.data.az, ...a.result.data.fwxs];
          }
          cgCacheData.value = [...list];
          // debugger;
          if (reset) {
            cgCacheData.value = [];
            //恢复系统默认将存储的对应章节数据清空
            table2SelectZJ.value = [];
          } else {
            //将一些设置更改为缓存数据
            layerInterval.value = a.result?.layerInterval || 0;
            heightRange.value = a.result?.heightRange || 0;
            oldlayerInterval.value = a.result?.layerInterval || 0;
            oldheightRange.value = a.result?.heightRange || 0;
            borrowRule.value = a.result?.borrowRule;
            outputType.value = a.result?.outputType || 2;
          }
          getFinallyData(res.result);
        }
      });
    }
  });
};
let checkTypeList = reactive([]); //表格1的每行数据记取方式
const getFinallyData = data => {
  //对比缓存数据和获取到的列表数据
  if (!data.find(a => a.viewFlag)) {
    //上表格无展示数据-下表格置为空
    tableDataSecond.value = [];
    return;
  }
  if (isEditInput.value) {
    console.log(checkTypeList);
    //更改高层建筑不刷新表1的记取方式
    data.forEach(i => {
      const tar = upTableCheckList.value.find(a => a.feeCode === i.feeCode && a.id === i.id);
      i.type = tar ? tar.type : i.type;
      i.isCheck = tar.isCheck;
    });
  }
  tableDataFirst.value = data;
  // if (upTableCheckList.value.length > 0) {
  //   tableDataFirst.value.map(
  //     item =>
  //       (item.isCheck = upTableCheckList.value.includes(item.id) ? true : false)
  //   );
  // }
  console.log(
    'tableDataFirst.value && tableDataFirst.value[0]',
    tableDataFirst.value,
    table1current.value
  );
  if (isEditInput.value) {
    let target = tableDataFirst.value.findIndex(i => i.id === table1current.value.id);
    currentRow.value = tableDataFirst.value[target];
  } else {
    currentRow.value = tableDataFirst.value[1];
  }
  upTable.value.setCurrentRow(currentRow.value);

  //有缓存数据下表格数据根据缓存数据展示，没有下表格数据接口获取
  if (cgCacheData.value.length > 0) {
    tableDataFirst.value.map(item => {
      let same = cgCacheData.value.find(cg => cg.feeCode === item.feeCode && cg.id === item.id);
      if (same) {
        item.isCheck = same.isCheck;
        item.classLevelList = same.classLevelList;
        item.baseDeScope = same.baseDeScope;
        item.type = same.type;
        item.baseDeList = same.baseDeList;
        item.notSelectDeList = same.notSelectDeList;
        item.createFeeQdId = same.createFeeQdId;
      }
    });
    setInitDataZJ();
    // getDefaultRow(tableDataSecond.value);-已有defaultRow不需要设置
    checkType(currentRow.value.type);
  } else {
    getTableDownData();
  }
  nextTick(() => {
    upTable.value.setAllTreeExpand(true);
  });
};
const setInitDataZJ = () => {
  //处理缓存对应章节数据
  initDownData.value.map(item => {
    let same = cgCacheData.value.find(
      cg => cg.feeCode === item.feeCode && cg.id === item.id
    );
    if (same) {
      item.classLevelList.map((a, idx) => {
        a.classLevelCgCache = same.classLevelList[idx]['classLevel2Name'];
      });
    }
  });
};
//获取下表格数据的默认行
const getDefaultRow = list => {
  list.forEach(item => {
    item.isDefaultRow = item.deList.find(de => +de.isDefault === 1);
    item.isDefault = item.isDefaultRow?.sequenceNbr;
    item.isDefaultRow.allocationMethod = Number(item.isDefaultRow?.allocationMethod);
  });
};
const currentChange = ({ newValue, oldValue }) => {
  //基数定额表格点击行下表格切换对应的数据
  if (table2current.value || ['az', 'fwxs'].includes(newValue.id)) {
    upTable.value.setCurrentRow(oldValue);
    return;
  }
  currentRow.value = newValue;
  upTable.value.setCurrentRow(currentRow.value);
  getTableDownData();
};
const cellClickEvent = ({ row, column }) => {
  //iscurrent点击currentChange事件不会触发
  if (['az', 'fwxs'].includes(row.id)) {
    return;
  }
  currentRow.value = row;
  upTable.value.setCurrentRow(currentRow.value);
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
};
const cellClickDownEvent = ({ row, column }) => {
  const field = column.field;
  let list = [
    'isDefaultRow.allocationMethod',
    'isDefaultRow.rRate',
    'isDefaultRow.cRate',
    'isDefaultRow.jRate',
  ];
  if (table2current.value && !list.includes(field)) {
    upTable.value.revertData(row, field);
    editInfo.value = true;
    return;
  }
};
//记取方式切换
const checkType = async type => {
  //切换记取方式重新获取下表格的具体清单
  let typeTar = checkTypeList.find(
    i => i.feeCode === currentRow.value.feeCode && i.id === currentRow.value.id
  );
  if (typeTar) {
    typeTar.type = type;
  } else {
    checkTypeList.push({
      feeCode: currentRow.value.feeCode,
      type: type,
      id: currentRow.value.id,
    });
  }
  // if (type === 4) return;//暂时注释-----下表格选择对应分部不展示的问题
  let hasList = defaultDownData.value.find(
    item =>
      item.feeCode === currentRow.value.feeCode &&
      item.id === currentRow.value.id &&
      item.type === currentRow.value.type &&
      !item.isfirst
  ); //与下表格缓存的编辑历史数据
  let cacheRow = cgCacheData.value?.find(
    a => a.feeCode === currentRow.value.feeCode && a.id === currentRow.value.id
    // &&a.type === currentRow.value.type
  ); //下表格有缓存数据
  let hasList2 = defaultDownData.value.find(
    item =>
      item.feeCode === currentRow.value.feeCode &&
      item.id === currentRow.value.id &&
      item.type === currentRow.value.type &&
      item.isfirst
  ); //下表格有默认数据
  // debugger;
  // if (outputType.value === 1 && selectTypeValue.value.type === 0) {
  //   //如果是高级选项设置了清单按分部计算且计取方式为制定分部分项具体清单--下表格具体枪弹展示默认数据
  // }
  // debugger;
  if (hasList) {
    tableDataSecond.value = hasList.classLevelList;
  } else if (cacheRow) {
    currentRow.value.classLevelList = JSON.parse(JSON.stringify(cacheRow.classLevelList));
    if (!currentRow.value.classLevelList[0].hasOwnProperty('isDefaultRow')) {
      //当前行未设置isDefaultRow需要重新设置
      getDefaultRow(currentRow.value.classLevelList);
    }
    currentRow.value.classLevelList.map(item => {
      let id = item.isDefaultRow.selectQdId || item.isDefaultRow.relationListId;
      let name = item.isDefaultRow.selectQdName || item.isDefaultRow.relationList;
      setDowntableQd(item, id, name, true);
    });
    tableDataSecond.value = currentRow.value.classLevelList;
  } else if (!hasList && !cacheRow && hasList2) {
    //此处编辑层高建筑应该取缓存数据-和上面切换层高清空缓存矛盾    判断是否修改层高 isEditInput.value
    getDefaultRow(hasList2.classLevelList);
    hasList2.classLevelList.map(item => {
      let id = item.isDefaultRow.selectQdId || item.isDefaultRow.relationListId;
      let name = item.isDefaultRow.selectQdName || item.isDefaultRow.relationList;
      setDowntableQd(item, id, name, true);
    });
    tableDataSecond.value = hasList2.classLevelList;
  } else {
    // 都没有的话需要接口查询;
    let list = [];
    let dataList = defaultDownData.value.find(
      item =>
        item.feeCode === currentRow.value.feeCode && item.id === currentRow.value.id && item.isfirst
    );
    dataList &&
      dataList.classLevelList.forEach(item => {
        list.push(item.isDefaultRow);
      });
    let formData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      data: JSON.parse(JSON.stringify(list)),
      type: currentRow.value.type,
      feeCode: currentRow.value.feeCode,
    };
    await api.getDefaultQdValue(formData).then(res => {
      console.log('getDefaultQdValue', res, formData);
      if (res.status === 200 && res.result) {
        // debugger;
        getDefaultRow(currentRow.value.classLevelList);
        // debugger;
        currentRow.value.classLevelList.map(item => {
          let QD = res.result.find(qd => qd.sequenceNbr === item.isDefault);
          let name;
          let id;
          if (QD) {
            name = QD?.relationList;
            id = QD?.relationListId;
          } else {
            name = item.isDefaultRow.selectQdName || item.isDefaultRow.relationList;
            id = item.isDefaultRow.selectQdId || item.isDefaultRow.relationListId;
          }
          setDowntableQd(item, id, name, true);
        });
      }
      tableDataSecond.value = currentRow.value.classLevelList;
      tableNo2.value.reloadData(tableDataSecond.value);
      getLastDefaultDownDataRow(
        currentRow.value,
        JSON.parse(JSON.stringify(tableDataSecond.value))
      );
    });
  }
  tableDataSecond.value.map(
    a => (a.isDefaultRow.allocationMethod = +a.isDefaultRow.allocationMethod)
  );
  setMergeCells();
  refreshTypeValueQD();
  // refreshTypeValueQD();
};
const refreshTypeValueQD = () => {
  if (selectTypeValue.value?.type === 4) {
    initSelectQdValue();
  } else {
    if (tableDataSecond.value.length === 0 || !selectTypeModal.value) return;
    let first = tableDataSecond.value[0].isDefaultRow;
    let second = tableDataSecond.value.find(
      a => a.sequenceNbr !== first.sequenceNbr && a.isDefaultRow.showQdId !== first.showQdId
    );
    if (second) {
      //下表格具体清单列数据不一致展示null
      initSelectQdValue();
    } else {
      selectTypeValueQd.relationList = first.showQdName;
      selectTypeValueQd.relationListId = first.showQdId;
    }
    console.log('切换计取方式', first, second, selectTypeValueQd);
  }
};
//获取最初的默认清单数据，具体清单不勾选展示默认数据-无缓存
const defaultList = () => {
  defaultDownData.value.forEach(h => {
    if (!['az', 'fwxs'].includes(h.id)) {
      h.isfirst = true;
      h.classLevelList.forEach(item => {
        let target = item.deList.find(de => +de.isDefault === 1);
        item.isDefaultRow = target;
        item.isDefault = target?.sequenceNbr;
        item.isDefaultRow.allocationMethod = Number(item.isDefaultRow.allocationMethod);
        let id = item.isDefaultRow.selectQdId || item.isDefaultRow.relationListId;
        let name = item.isDefaultRow.selectQdName || item.isDefaultRow.relationList;
        setDowntableQd(item, id, name, true);
        item.defaultName = item.isDefaultRow.showQdName;
        item.defaultID = item.isDefaultRow.showQdId;
      });
    }
  });
};

//安装费用列表下表格数据获取
const getTableDownData = () => {
  const $table = tableNo2.value;
  // debugger;
  if ($table && outputType.value === 1 && currentRow.type === 0) {
    $table.revertData(tableDataSecond.value);
  }
  console.log('------------', currentRow.value);
  if (currentRow.value.type === 4) {
    getDefaultRow(currentRow.value.classLevelList); //设置下表格isDefaultRow
    tableDataSecond.value = currentRow.value.classLevelList;
    setMergeCells();
    refreshTypeValueQD();
  } else {
    checkType(currentRow.value.type);
  }
  setTable2Select(true);
  tableDataSecond.value.map(
    a => (a.isDefaultRow.allocationMethod = +a.isDefaultRow.allocationMethod)
  );
  console.log('------------', tableDataSecond.value);
};

//点击记取按钮
const submit = () => {
  if (tableDataFirst.value && tableDataFirst.value.length === 0) {
    emits('close');
    return;
  }
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  let unitList = initTreeData.value.map(a => {
    return a.id;
  });
  const unitIdList = rightInfo.checkedKeys.filter(a => unitList.includes(a));
  let list = JSON.parse(JSON.stringify(tableDataFirst.value.filter(item => item.isCheck === true)));
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    layerInterval: Number(layerInterval.value),
    heightRange: Number(heightRange.value),
    borrowRule: borrowRule.value,
    outputType: outputType.value,
    unitIdList: unitIdList,
  };
  let infoList = [];
  let data = {
    az: [],
    fwxs: [],
  };
  list &&
    list.map(item => {
      infoList.push(item.feeName);
      item.classLevelList.forEach(de => {
        //设置isDefaultRow
        if (!de.hasOwnProperty('isDefaultRow')) {
          de.isDefault = de.deList.find(de => +de.isDefault === 1)?.sequenceNbr;
          de.isDefaultRow = de.deList.find(de => +de.isDefault === 1);
        }
        if (item.type === 4) {
          //对应分部分项设置isDefaultRow具体清单展示
          setDowntableQd(de, null, null);
          de.isDefaultRow.selectQdName = null;
          de.isDefaultRow.selectQdId = null;
        }
      });
      data[item.parentId].push(item);
    });
  apiData.data = data;
  console.log(list, data);
  // return;
  // debugger;
  infoList = Array.from(new Set(infoList));
  console.log('安装费用结果', defaultDownData.value);
  console.log('安装费用传参', apiData);
  if (submitLoading.value) return;
  submitLoading.value = true;
  api
    .azCostMath(apiData)
    .then(res => {
      console.log('安装费用结果', res, apiData);
      // debugger;
      if (infoList.length === 0 && res.status === 200) {
        emits('updateData');
        emits('close');
      } else if (infoList.length > 0 && res.status === 200) {
        let info = infoList.join();
        infoVisible.value = true;
        isSureModal.value = true;
        infoText.value = `${info}计取成功！`;
        iconType.value = 'icon-ruotixing';
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
const updateCurrentInfo = type => {
  //更新数据
  if (!infoVisible.value) return;
  infoVisible.value = false;
  if (type === 1) {
    emits('updateData');
  } else {
    emits('updateData');
    emits('close');
  }
};

//基数定额记取范围设置确定
const sureLocation = () => {
  !currentRow.value.hasOwnProperty('baseDeList') || !currentRow.value.baseDeList
    ? (currentRow.value.baseDeList = [])
    : '';
  currentRow.value.notSelectDeList = [];
  let checklist = [];
  tableDataThird.value.map(item => {
    if (item.type === '定' && item.isCheck) {
      checklist.push(item.sequenceNbr);
    }
    if (item.type === '定' && !item.isCheck) {
      currentRow.value.notSelectDeList.push(item.sequenceNbr);
    }
  });
  if (checklist.length > 0) {
    currentRow.value.baseDeList = checklist;
  }
  let select = upTable.value.getCurrentRecord();
  let tar = table3SelectList.find(
    i => i.table1row.feeCode === select.feeCode && i.table1row.id === select.id
  );
  if (tar) {
    tar.selectList = tableDataThird.value.filter(i => i.isCheck);
  } else {
    table3SelectList.push({
      table1row: select,
      selectList: tableDataThird.value.filter(i => i.isCheck),
    });
  }
  locationModel.value = false;
};
//更新存储的默认数据放
const getLastDefaultDownDataRow = (row, newValue) => {
  let list = defaultDownData.value.find(
    item =>
      item.feeCode === row.feeCode && item.id === row.id && item.type === row.type && !item.isfirst
  );
  console.log('defaultDownData.value', defaultDownData.value);
  if (list) {
    defaultDownData.value.map(item => {
      if (
        item.feeCode === row.feeCode &&
        item.id === row.id &&
        item.type === row.type &&
        !item.isfirst
      ) {
        item.classLevelList = JSON.parse(JSON.stringify(newValue));
      }
    });
  } else {
    defaultDownData.value.push({
      ...row,
      classLevelList: JSON.parse(JSON.stringify(newValue)),
      isfirst: false,
    });
  }
};
const setDowntableQd = (row, id, name, isOnlyShow = false) => {
  //设置下表格具体清单勾选行
  row.isDefaultRow.showQdId = id;
  row.isDefaultRow.showQdName = name;
  if (!isOnlyShow) {
    row.isDefaultRow.selectQdId = id;
    row.isDefaultRow.selectQdName = name;
  }
  row.deList.map(a => {
    if (a.isDefault) {
      a.showQdId = id;
      a.showQdName = name;
      if (!isOnlyShow) {
        a.selectQdId = id;
        a.selectQdName = name;
      }
    }
  });
};
//基数定额记取范围确定
let table4SelectList = reactive([]);
const sureJQ = () => {
  let table4change = tableDataFourth.value.find(item => item.isCheck);
  // debugger;
  if (table4change) {
    //具体清单有勾选
    if (isSetAllQDid.value) {
      //记住勾选项
      selectTypeValueQd.relationList = `${table4change.fxCode} ${table4change.name}`;
      selectTypeValueQd.relationListId = table4change.sequenceNbr;
    } else {
      //设置下表格对应行具体清单数据
      let id = table4change.sequenceNbr;
      let name = `${table4change.fxCode} ${table4change.name}`;
      setDowntableQd(table2DetailRow.value, id, name);
    }
  } else {
    //具体清单无勾选展示默认值
    let defaultDownDataRow = defaultDownData.value.find(
      item =>
        item.feeCode === currentRow.value.feeCode &&
        item.id === currentRow.value.id &&
        item.isfirst === true
    );
    defaultDownDataRow.classLevelList.map(item => {
      if (
        item.classLevel1Name === table2DetailRow.value.classLevel1Name &&
        item.classLevel2Name === table2DetailRow.value.classLevel2Name
      ) {
        setDowntableQd(table2DetailRow.value, item.default, item.defaultName, true);
      }
    });
  }
  // debugger;
  let select = upTable.value.getCurrentRecord();
  let select2 = tableNo2.value.getCurrentRecord();
  console.log('sdfsdfdsfdsfdsfd', select2);
  if (!select?.classLevelList[0].hasOwnProperty('isDefault')) {
    //上表格没有处理isDefault-进行处理
    getDefaultRow(select?.classLevelList);
  }
  let tpTargetRow = select.classLevelList?.find(a => a?.isDefault === select2?.isDefault);
  tpTargetRow && !isSetAllQDid.value
    ? setDowntableQd(tpTargetRow, select2.isDefaultRow.showQdId, select2.isDefaultRow.showQdName)
    : '';
  if (!isSetAllQDid.value) {
    //单条设置
    saveJqResult(select, select2);
    let newValue = tableDataSecond.value;
    getLastDefaultDownDataRow(currentRow.value, newValue);
  }
  // debugger;
  detailModel.value = false;
};
const saveJqResult = (select, select2) => {
  let tar = table4SelectList.find(
    i =>
      i.table1row.feeCode === select.feeCode &&
      i.table1row.id === select.id &&
      i.table2row.sequenceNbr === select2.sequenceNbr
  );
  if (tar) {
    tar.selectList = tableDataFourth.value.filter(i => i.isCheck);
  } else {
    table4SelectList.push({
      table1row: select,
      table2row: select2,
      selectList: tableDataFourth.value.filter(i => i.isCheck),
    });
  }
};
//上表格行切换
const tableRowChange = ({ newValue, oldValue }) => {
  console.log('newValue', newValue, 'oldValue', oldValue);
  let total = 0;
  oldValue
    ? (total =
        Number(oldValue.isDefaultRow?.cRate) +
        Number(oldValue.isDefaultRow?.rRate) +
        Number(oldValue.isDefaultRow?.jRate))
    : '';
  if (oldValue && oldValue.isDefaultRow?.allocationMethod === 1 && total !== 100) {
    editInfo.value = true;
    table2current.value = oldValue;
  }
};
watch(
  () => table2current.value,
  () => {
    if (table2current.value) {
      let total =
        Number(table2current.value.isDefaultRow.cRate) +
        Number(table2current.value.isDefaultRow.rRate) +
        Number(table2current.value.isDefaultRow.jRate);
      if (total === 100) {
        isEdit.value = true;
        table2current.value = null;
      } else {
        isEdit.value = false;
      }
    }
  }
);
const editClosedEvent = ({ row, column }) => {
  const $table = tableNo2.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (
    row.isDefaultRow.allocationMethod === 1 &&
    (field === 'isDefaultRow.cRate' ||
      field === 'isDefaultRow.jRate' ||
      field === 'isDefaultRow.rRate')
  ) {
    if (table2current.value) {
      let total =
        Number(table2current.value.isDefaultRow.cRate) +
        Number(table2current.value.isDefaultRow.rRate) +
        Number(table2current.value.isDefaultRow.jRate);
      total === 100
        ? ((isEdit.value = true), (table2current.value = null))
        : (isEdit.value = false);
    } else {
      let total =
        Number(row.isDefaultRow.cRate) +
        Number(row.isDefaultRow.rRate) +
        Number(row.isDefaultRow.jRate);
      total === 100 ? '' : ((isEdit.value = false), (table2current.value = row));
    }
    //编辑完更新默认下表格数据
    getLastDefaultDownDataRow(currentRow.value, tableDataSecond.value);
  }
  updateUptableData(row, field.replace('isDefaultRow.', ''));
};
const setTotalRate = (item, row) => {
  let total = Number(item.rRate) + Number(item.jRate) + Number(item.cRate);
  if (item.allocationMethod === 1 && total !== 100) {
    isEdit.value = false;
    table2current.value = row;
  } else if (item.allocationMethod !== 1) {
    if (table2current.value && table2current.value.isDefaultRow.sequenceNbr === item.sequenceNbr) {
      table2current.value = null;
      isEdit.value = true;
    }
  }
};

//高层建筑高切换
const queryData = type => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  switch (type) {
    case 'layerInterval++':
      layerInterval.value++;
      break;
    case 'layerInterval--':
      layerInterval.value--;
      break;
    case 'heightRange++':
      heightRange.value++;
      break;
    case 'heightRange--':
      heightRange.value--;
      break;
  }
  updateTableList();
};
const selectChange = (row, type) => {
  downCurrenRow.value = row;
  switch (type) {
    case 'type':
      checkType(row.type);
      break;
    case 'isDefault':
      // 对应当前单位措施定额
      row.isDefaultRow = row.deList.find(item => item.sequenceNbr === row.isDefault);
      //处理基数分摊方式为number类型
      row.isDefaultRow.allocationMethod = row.isDefaultRow.allocationMethod / 1;
      break;
    case 'allocationMethod':
      // 基数分摊方式
      if (row.isDefaultRow.calculateBase === 'RGF，CLF，JXF')
        row.isDefaultRow.calculateBase = 'RGF+CLF+JXF';
      setTotalRate(row.isDefaultRow, row);
      const { scrollLeft, scrollTop } = tableNo2.value?.getScroll();
      pos.scrollLeft = scrollLeft;
      pos.scrollTop = scrollTop;
      break;
    case 'calculateBase':
      // 基数计算基数
      break;
    case 'chapterValue':
    case 'deBookValue':
      deSetCheck.flag = true;
      type === 'chapterValue' ? getBaseDeList() : getBaseDeList(false, true);
      break;
    case 'table3check':
      row.children && row.children.length > 0
        ? fatherCheck(row, tableDataThird.value)
        : childCheck(row, tableDataThird.value);
      childCheck(row, tableDataThird.value);
      fatherCheck(row, tableDataThird.value);
      // 基数定额记取范围设置多选框
      //存储未选择的选项
      let list = [];
      if (row.kind !== '04') {
        let tree = xeUtils.toTreeArray([row]);
        list = tree.filter(a => a.kind === '04');
      } else {
        list = [row];
      }
      list &&
        list.map(a => {
          setDeCheckList(a, row.isCheck);
        });
      break;
    case 'tableSelectCom':
      row.children && row.children.length > 0
        ? fatherCheck(row, selectComData.value)
        : childCheck(row, selectComData.value);
      childCheck(row, selectComData.value);
      fatherCheck(row, selectComData.value);
    case 'qdisCheck':
      console.log('tableDataFourth', tableDataFourth.value);
      if (row.isCheck) {
        //具体清单记取只可以选择一项
        tableDataFourth.value.map(item =>
          item.type === '清' && item.sequenceNbr !== row.sequenceNbr && item.isCheck
            ? (item.isCheck = false)
            : ''
        );
      }
      break;
  }
  if (['isDefault', 'allocationMethod', 'calculateBase'].includes(type)) {
    updateUptableData(row, type);
  }
};
const updateUptableData = (row, type) => {
  let select = upTable.value.getCurrentRecord();
  let upSelect = tableDataFirst.value.find(a => a.id === select.id);
  let select2 = tableNo2.value.getCurrentRecord();
  let upSelectRow = upSelect.classLevelList?.find(
    a =>
      a?.classLevel1Name === select2?.classLevel1Name &&
      a?.classLevel2Name === select2?.classLevel2Name
  );
  if (type === 'isDefault') {
    upSelectRow.deList.map(a => {
      if (a.sequenceNbr === row.isDefault) {
        a.isDefault = 1;
      } else {
        a.isDefault = 0;
      }
    });
    getDefaultRow([upSelectRow]);
    let id = row.isDefaultRow.selectQdId || row.isDefaultRow.relationListId;
    let name = row.isDefaultRow.selectQdName || row.isDefaultRow.relationList;
    setDowntableQd(row, id, name, true);
  } else {
    if (!upSelectRow.isDefaultRow) {
      getDefaultRow([upSelectRow]);
    }
    upSelectRow.isDefaultRow[type] = row.isDefaultRow[type];
  }
  let newValue = tableDataSecond.value;
  getLastDefaultDownDataRow(select, newValue);
};
const setDeCheckList = (row, isCheck) => {
  if (!locationModel.value) return;
  if (isCheck && deSetCheck.list.includes(row.sequenceNbr)) {
    let idx = deSetCheck.list.findIndex(a => a === row.sequenceNbr);
    deSetCheck.list.splice(idx, 1);
  }
  if (!isCheck && !deSetCheck.list.includes(row.sequenceNbr)) {
    deSetCheck.list.push(row.sequenceNbr);
  }
  console.log(deSetCheck.list);
};
//父节点选中取消-子节点都随父选中取消
const fatherCheck = (father, tableData) => {
  console.log('fatherCheck', father);
  // debugger;
  if (!['清', '定', '部'].includes(father.type)) {
    tableData.map(item => (item.isCheck = father.isCheck));
    return;
  }
  let childList = tableData.filter(item => item.parentId === father.sequenceNbr);
  if (childList.length === 0) return;
  if (childList.length > 0) {
    childList.map(item => {
      item.isCheck = father.isCheck;
      fatherCheck(item, tableData);
    });
  }
};

//子节点选中取消-判断父节点是否需要选中取消操作
const childCheck = (child, tableData) => {
  if (tableData.filter(item => item.sequenceNbr === child.parentId).length === 0) return;
  let childFather = tableData.filter(item => item.sequenceNbr === child.parentId)[0];
  let childList = tableData.filter(item => item.parentId === childFather.sequenceNbr);
  if (childList.filter(item => item.isCheck === false).length === 0) {
    childFather.isCheck = true;
    childCheck(childFather, tableData);
  } else {
    childFather.isCheck = false;
    childCheck(childFather, tableData);
  }
};

//获取基数定额记取详情弹框安装专业下拉列表数据
const setlocationModel = async row => {
  currentRow.value = row;
  upTable.value.setCurrentRow(row);
  getTableDownData();
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  //true-刚打开弹框需要获取章节列表和安装专业列表+选项默认全部专业及全部章节
  deSetCheck.flag = false;
  let list = currentRow.value.hasOwnProperty('notSelectDeList')
    ? currentRow.value.notSelectDeList || []
    : [];
  // debugger;
  deSetCheck.list = [...list];
  await getBaseDeList(true, true);
  locationModel.value = true;
};
const getSelectList = (list, getDe, getZj) => {
  //获取安装专业和章节列表下拉
  let DEList = list.filter(a => a.kind === '04');
  const map = new Map();
  if (getZj) {
    let chapterData = [
      { label: '全部章节', value: '' },
      ...DEList.map(a => {
        return {
          label: a.classifyLevel2,
          value: a.classifyLevel2,
        };
      }),
    ];
    chapterList.value = chapterData.filter(v => !map.has(v.label) && map.set(v.label, v));
    chapterValue.value = chapterList.value[0].value;
  }
  if (getDe) {
    let deBookData = [
      { label: '全部专业', value: '' },
      ,
      ...DEList.map(a => {
        return {
          label: a.classifyLevel1,
          value: a.classifyLevel1,
        };
      }),
    ];
    deBookList.value = deBookData.filter(v => !map.has(v.label) && map.set(v.label, v));
    deBookValue.value = deBookList.value[0].value;
  }
  console.log(chapterList.value, deBookList.value);
};

//获取基数定额记取详情表格数据
let table3SelectList = reactive([]);
let deSetCheck = reactive({
  list: [],
  flag: true,
  tableData: [],
});
let isShowUnit = ref(true);
const getBaseDeList = async (getDe = false, getZj = false) => {
  if (getZj) chapterValue.value = '';
  // debugger;
  if (!deBookValue.value && !chapterValue.value) {
    isShowUnit.value = true;
  } else {
    isShowUnit.value = false;
  }
  let formData = {
    feeCode: currentRow.value.feeCode,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    azType: deBookValue.value,
    zjType: chapterValue.value,
    classLevelList: JSON.parse(JSON.stringify(tableDataSecond.value)),
    azClassLevelType: currentRow.value.parentId,
  };
  await api.baseDeList(formData).then(async res => {
    console.log('基数定额框章节列表', formData, res);
    if (res.status === 200 && res.result) {
      res.result.forEach(item => {
        // if (item.kind === '0') item.isShowUnit = isShowUnit.value;
        if (item.kind === '03') item.lineHeight = false;
      });
      let tableDataThirdList = res.result;
      if (getDe || getZj) {
        getSelectList(res.result, getDe, getZj);
      }
      if (deSetCheck.flag) {
        tableDataThirdList.map(item => {
          if (item.type === '定') {
            item.isCheck = !deSetCheck.list.includes(item.sequenceNbr) ? true : false;
            childCheck(item, tableDataThirdList);
          }
        });
      } else {
        if (currentRow.value.notSelectDeList && currentRow.value.notSelectDeList.length > 0) {
          tableDataThirdList.map(item => {
            if (item.type === '定') {
              item.isCheck = !currentRow.value.notSelectDeList.includes(item.sequenceNbr)
                ? true
                : false;
              childCheck(item, tableDataThirdList);
            }
          });
        } else if (!isEditInput.value) {
          tableDataThirdList.map(item => (item.isCheck = true));
        } else if (isEditInput.value) {
          let select = upTable.value.getCurrentRecord();
          console.log(table1current.value, currentRow.value, select, table3SelectList);

          let tar = table3SelectList.find(
            i => i.table1row.feeCode === select.feeCode && i.table1row.id === select.id
          );
          if (tar) {
            tableDataThirdList.forEach(i => {
              i.isCheck = tar.selectList.find(a => a.sequenceNbr === i.sequenceNbr) ? true : false;
            });
          } else {
            tableDataThirdList.map(item => (item.isCheck = true));
          }
        }
      }

      nextTick(() => {
        tableDataThird.value = tableDataThirdList;
        tableNo3.value?.reloadData(tableDataThird.value);
      });
    }
  });
};
const radioChange = () => {
  //全选反选-只针对定额
  if (radioListValue.value === 1) {
    tableDataThird.value.map(item => {
      if (item.type === '定') {
        item.isCheck = true;
        childCheck(item, tableDataThird.value);
        setDeCheckList(item, item.isCheck);
      }
    });
  } else {
    tableDataThird.value.map(item => {
      if (item.type === '定') {
        item.isCheck = !item.isCheck;
        childCheck(item, tableDataThird.value);
        setDeCheckList(item, item.isCheck);
      }
    });
  }
};

//具体清单表格数据查询
let isSetAllQDid = ref(false); //是否设置全部下表格具体清单
const getDetailQD = row => {
  console.log('selectTypeValue.value', selectTypeValue.value, row);
  selectTypeValue.value = upTable.value.getCurrentRecord();
  if (
    selectTypeValue.value.type === 4 ||
    (outputType.value === 1 && selectTypeValue.value.type === 0)
  )
    return; //计取方式为分部分项时不可打开具体清单弹框
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  detailModel.value = true;
  table2DetailRow.value = row;
  isSetAllQDid.value = false; //是否设置下表格全部数据
  if (!row) {
    row = tableDataSecond.value[0];
    isSetAllQDid.value = true;
  }
  let formData = {
    feeCode: currentRow.value.feeCode,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    type: currentRow.value.type,
    selectQdId: row.isDefaultRow.selectQdId,
    relationListId: row.isDefaultRow.relationListId,
  };
  console.log('getDetailQD--qdList', formData);
  api.qdList(formData).then(res => {
    if (res.status === 200 && res.result) {
      res.result &&
        res.result.forEach(i => {
          if (!i.type && i.kind) {
            i.type = ['01', '02'].includes(i.kind) ? '部' : '清';
          }
        });
      tableDataFourth.value = res.result;
      let select = upTable.value.getCurrentRecord();
      let select2 = tableNo2.value.getCurrentRecord();
      let tar = table4SelectList.find(
        i =>
          i.table1row.feeCode === select.feeCode &&
          i.table1row.id === select.id &&
          i.table2row.sequenceNbr === select2?.sequenceNbr
      );
      if (isEditInput.value && tar) {
        tableDataFourth.value.forEach(i => {
          i.isCheck = tar.selectList.find(a => a.sequenceNbr === i.sequenceNbr) ? true : false;
        });
      } else {
        // let selected = tableDataFourth.value.find(
        //   a =>
        //     a.isCheck ||
        //     (selectTypeModal.value &&
        //       a.sequenceNbr === selectTypeValueQd?.relationListId)
        // );
        let selected;
        if (selectTypeModal.value) {
          //从计取位置打开具体清单--根据具体项selectTypeValueQd选中
          selected = tableDataFourth.value.find(
            a => a.sequenceNbr === selectTypeValueQd?.relationListId
          );
        } else {
          //从具体清单详情按钮打开具体清单--选中为当前行的具体清单数据
          selected = tableDataFourth.value.find(a => a.isCheck);
        }
        console.log('tableDataFourth.value', tableDataFourth.value, row);
        if (!selected && !isSetAllQDid.value) {
          let selectItem = tableDataFourth.value.find(a => a.sequenceNbr === row.showQdId);
          if (selectItem) selectItem.isCheck = true;
        }
        if (selected) selected.isCheck = true;
      }
      tableNo4.value.reloadData(tableDataFourth.value);
    } else {
      tableDataFourth.value = [];
    }
  });
};
const rowClassNameFirstTable = ({ row }) => {
  if (['az', 'fwxs'].includes(row.id)) {
    return 'title-row-bgColor';
  }
};
const rowClassName = ({ row }) => {
  let ClassStr = '';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.lineHeight && row.kind === '03') {
    ClassStr = ClassStr + ' row-moreLine';
  }
  if (row.parentId == 1) {
    return `row-tree-title  ${ClassStr}`;
  } else {
    return ClassStr;
  }
};
const selectChangeEvent = ({ checked, row }) => {
  row.isCheck = checked;
};
const selectBtnChange = type => {
  console.log('selectBtnChange', type);
  if (type === '1') {
    defineCheckRowKeys.value = selectComData.value.map(item => {
      item.isCheck = 1;
      return item.idx;
    });
  } else {
    //反选
    let noChildList = selectComData.value.filter(a => !a.childrenList?.length);
    noChildList.map(a => (a.isCheck = !a.isCheck));
    let isCheckList = noChildList.filter(a => a.isCheck);
    let selectKeysList = isCheckList.map(item => {
      return item.idx;
    }); //设置选中行
    defineCheckRowKeys.value = selectKeysList;
    let list = [...isCheckList];
    let noCheckParlist = selectComData.value.filter(a => a.childrenList?.length > 0);
    let parIdx = null;
    noCheckParlist.map(a => {
      if (parIdx !== a.idx) {
        parIdx = a.idx;
        let childs = getChildList(a, []);
        let childs2 = childs.filter(b => !b.childrenList?.length);
        let childs3 = childs2.map(item => {
          return item.idx;
        });
        if (childs3.every(b => selectKeysList.includes(b))) {
          a.isCheck = 1;
          defineCheckRowKeys.value.push(parIdx);
        }
      }
    });
  }
};

const convertSequence = arr => {
  let result = '';
  if (!arr || arr?.length === 0) return;
  let start = arr[0].idx;
  let end = arr[0].idx;
  for (let i = arr[0].typeNo; i <= arr[arr.length - 1].typeNo; i++) {
    let list = arr.filter(a => a.typeNo === i);
    if (list.length === 0) return;
    start = list[0].idx;
    if (list.length === 1) {
      if (result === '') {
        result += start;
      } else {
        result += ',' + start;
      }
    } else {
      end = list[list.length - 1].idx;
      if (result === '') {
        result += start + '~' + end;
      } else {
        result += ',' + start + '~' + end;
      }
    }
  }
  return result;
};
let disNoList = [];
let table2SelectZJ = ref([]);
let comTableLoading = ref(false);
const sureSelectCompute = () => {
  let type = 0;
  let list = [];
  let checkList = selectComData.value.filter(a => defineCheckRowKeys.value.includes(a.idx));
  let treeList = [...selectComData.value];
  if (checkList.find(a => a.idx === '0')) {
    //最外层父级选中
    let childList = checkList.filter(a => a.parentId === '0');
    childList.forEach(a => {
      list.push({
        typeNo: type,
        idx: a.idx,
        name: a.name,
      });
    });
  } else {
    treeList.map((i, idx) => {
      if (checkList.find(a => a.idx === i.idx)) {
        // debugger;
        if (i.childrenList?.length > 0) {
          let childAllList = getChildList(i, []);
          // debugger;
          treeList.splice(idx + 1, childAllList.length - 1);
          //删除选中目标的子级
        }
        let isLastCheck = checkList.find(a => a.idx === treeList[idx - 1].idx); //上一项选中且前面没有push进去当前项的父级
        let now = i.idx.split('.');
        let last = treeList[idx - 1].idx.split('.');
        let lastSameLength = last.slice(0, now.length);
        let isXL = now[now.length - 1] - lastSameLength[lastSameLength.length - 1] === 1;
        if (isLastCheck && now.length === last.length && isXL) {
          i.typeNo = treeList[idx - 1].typeNo;
        } else {
          type++;
          i.typeNo = type;
        }
        list.push({
          typeNo: i.typeNo,
          idx: i.idx,
          name: i.name,
          parentId: i.parentId,
        });
      }
    });
  }
  // debugger;
  let result = convertSequence(list);
  let tableNoRow = tableNo2.value.getCurrentRecord();
  let isFind = table2SelectZJ.value.find(
    a =>
      a.tab2DefaultRowSeq === tableNoRow.isDefaultRow.sequenceNbr &&
      a.feeCode === upTable.value.getCurrentRecord().feeCode &&
      a.id === upTable.value.getCurrentRecord().id
  );
  if (isFind) {
    isFind.classLevel2 = result;
    isFind.selectList = list;
  } else {
    table2SelectZJ.value.push({
      classLevel2: result,
      selectList: list,
      feeCode: upTable.value.getCurrentRecord().feeCode,
      id: upTable.value.getCurrentRecord().id,
      tab2DefaultRowSeq: tableNoRow.isDefault,
    });
  }
  setTable2Select();
  console.log(result, list, 'tableNoRow', tableNoRow, table2SelectZJ.value);
  // console.log('sureSelectCompute--确定', list, disNoList);
  selectCompute.value = false;
};
const saveClassLevel2Name = target => {
  //保存下表格默认的对应章节数据-为了标识基数分摊方式和基数选择
  if (!target.classLevel2NameOld)
    target.classLevel2NameOld = target.classLevel2Name;
};
const setTable2Select = (isSet = false) => {
  //设置下表格对应章节选择isSet--设置每条数据
  if (!isSet) {
    let tableNoRow = tableNo2.value.getCurrentRecord();
    let isFind = table2SelectZJ.value.find(
      a =>
        a.tab2DefaultRowSeq === tableNoRow.isDefault &&
        a.feeCode === upTable.value.getCurrentRecord().feeCode &&
        a.id === upTable.value.getCurrentRecord().id
    );
    if (isFind) {
      saveClassLevel2Name(tableNoRow);
      tableNoRow.classLevel2Name = isFind.classLevel2;
      tableNoRow.isDefaultRow.classLevel2 = isFind.classLevel2;
      tableNoRow.deList.find(c => c.sequenceNbr === tableNoRow.isDefault).classLevel2 =
        isFind.classLevel2;
      getDefaultRow(currentRow.value.classLevelList);
      //设置表格一的数据
      let target = currentRow.value.classLevelList.find(a => a.isDefault === tableNoRow.isDefault);
      target.isDefaultRow.classLevel2 = tableNoRow.isDefaultRow.classLevel2;
      target.classLevel2Name = tableNoRow.isDefaultRow.classLevel2;
    }
  } else {
    tableDataSecond.value.map(a => {
      let isFind = table2SelectZJ.value.find(
        b =>
          b.tab2DefaultRowSeq === a.isDefault &&
          b.feeCode === upTable.value.getCurrentRecord().feeCode &&
          b.id === upTable.value.getCurrentRecord().id
      );
      if (isFind) {
        saveClassLevel2Name(a);
        a.isDefaultRow.classLevel2 = isFind.classLevel2;
        a.classLevel2Name = isFind.classLevel2;
        a.deList.find(c => c.sequenceNbr === a.isDefault).classLevel2 = isFind.classLevel2;
      }
    });
  }
};
let selectComData = ref([]);
let selectComTable = ref();
let defineCheckRowKeys = ref([]); //初次打开木人选中行
let expandedRowKeys = ref([]);
const rowClassNameTree = (row, index, data) => {
  let ClassStr = 'normal-info';
  if (row.idx == data[0]?.idx) {
    ClassStr += ' first-row';
  }
  return ClassStr;
};
const customCell = ({ record: row, rowIndex, column }) => {
  let className = '';
  let style = {};
  let level = 0;
  for (let i = 1; i <= 7; i++) {
    if (row[`classifyLevel${i}`]) level++;
  }
  row.customLevel = level;
  if (['name'].includes(column.dataIndex) && row.childrenList?.length > 0) {
    if (row.noExpanded) {
      //收起状态的话需要变更
      className +=
        `Virtual-pdLeft-s${row.customLevel} ` + `Virtual-pdLeft-ag-noExpand-s${row.customLevel} `;
    } else {
      className +=
        `Virtual-pdLeft-s${row.customLevel} ` + `Virtual-pdLeft-ag-expand-s${row.customLevel} `;
    }
  }
  if (['name'].includes(column.dataIndex) && !row.childrenList?.length) {
    className += `Virtual-pdLeft-ag-noChild-s${row.customLevel} `;
  }
  // if (['name'].includes(column.dataIndex) && row.childrenList?.length > 0) {
  //   const line = row.maxLine || 1;
  //   className += ` cell-line-break-${line}`;
  // }
  return { style: style, class: className };
};
let selectedRowList = ref([]);
const onSelectChange = (selectedRowKeys, selectedRows) => {
  defineCheckRowKeys.value = selectedRowKeys;
  selectedRowList.value = selectedRows;
};
const setSelectIScheck = (record, selected) => {
  console.log(record, selected);
  if (record.childrenList?.length > 0) {
    let tarList = getChildList(record, []);
    tarList.map(a => (a.isCheck = selected ? 1 : 0));
  } else {
    record.isCheck = selected ? 1 : 0;
  }

  console.log(selectComData.value);
};
let tableOptions = reactive({
  columns: [
    {
      title: '专业/章节',
      dataIndex: 'name',
      align: 'left',
      width: '75%',
      ellipsis: true,
    },
  ],
  data: [],
});
const expandFun = (type, target, props) => {
  //展开收起设置
  target.noExpanded = props.expanded;
  let list = getChildList(target, []);
  let index = expandedRowKeys.value.findIndex(a => a === target.idx);
  if (type === 1) {
    //收起
    expandedRowKeys.value.splice(index, list.length);
  } else {
    let idxList = list.map(item => {
      return item.idx;
    });
    expandedRowKeys.value.splice(index, 0, ...idxList);
  }
};
const getChildList = (tar, tarList) => {
  //获取目标子列表数据
  tarList.push(tar);
  if (tar.childrenList && tar.childrenList.length > 0) {
    tar.childrenList.map(i => {
      getChildList(i, tarList);
    });
  }
  return tarList;
};
const openSelectCom = item => {
  // selectComData.value = [];
  defineCheckRowKeys.value = []; //清除默认选中行-warning
  selectCompute.value = true;
  comTableLoading.value = true;
  let tableNoRow = tableNo2.value.getCurrentRecord();
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    chapterStr: tableNoRow.isDefaultRow.classLevel2,
    fascicleStr: tableNoRow.classLevel1Name,
    azClassLevelType: currentRow.value.parentId,
  };
  console.log('openSelectCom---formData', formData);
  api
    .queryBaseDeChapter(formData)
    .then(res => {
      if (res.status === 200 && res.result) {
        let treeData = xeUtils.toTreeArray([res.result], {
          children: 'childrenList',
        });
        tableOptions.data = [res.result];
        selectComData.value = treeData;
        expandedRowKeys.value = treeData.map(item => {
          return item.idx;
        }); //展开所有行数据
        let isCheckList = treeData.filter(a => a.isCheck);
        let selectKeysList = isCheckList.map(item => {
          return item.idx;
        }); //设置选中行
        setTimeout(() => {
          defineCheckRowKeys.value = selectKeysList;
        }, 100);
        // console.log(
        //   '安装费用----基数定额列表查询',
        //   selectComData.value,
        //   res.result
        // );
      }
    })
    .finally(() => {
      comTableLoading.value = false;
    });
};
//设置应用范围
const treeData = shallowRef([]);
let rightInfo = shallowReactive({
  checkedKeys: [],
  expandedKeysRight: '',
});
const initTreeData = shallowRef([]);
//获取目标元素最外层父级
const getParentItem = (tar, treeList) => {
  //判断目标单位父级是否只有一个单位或者一个单项
  let parent = treeList.find(i => i.id === tar.parentId && i.levelType !== 1);
  let childAllList = treeList.filter(a => a.parentId === tar.parentId && a.id !== tar.id);
  if (
    (childAllList.length === 0 ||
      (childAllList.length > 0 && childAllList.every(b => disUnit.find(c => c.id == b.id)))) &&
    parent
  ) {
    disUnit.push(parent);
    getParentItem(parent, treeList);
  }
};
let disUnit = reactive([]);
const getList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(res => {
    const expandedKeys = [];
    if (res.result) {
      disUnit =
        res.result &&
        res.result.filter(
          e => e.levelType === 3 && store.deStandardReleaseYear / 1 !== e.deStandardReleaseYear / 1
        );
      disUnit.map(a => {
        getParentItem(a, res.result);
      });
    }
    let disList = disUnit.map(a => a.id);
    console.log(disUnit, disList);
    res.result.forEach(e => {
      if (e.levelType != 3) {
        expandedKeys.push(e.id);
      }
      //当前12定额不可选择22单位---反之亦然+只有单位设置专业可勾选
      if (
        disList.includes(e.id) ||
        (e.levelType === 3 && !e.constructMajorType) ||
        store.currentTreeInfo?.id === e.id
      ) {
        e.disabled = true;
      }
    });
    rightInfo.expandedKeysRight = expandedKeys;
    rightInfo.checkedKeys = [store.currentTreeInfo?.id];
    initTreeData.value = res.result.filter(a => a.levelType === 3); //单位
    treeData.value = xeUtils.toArrayTree(res.result, {
      parentKey: 'parentId',
      childrenKey: 'children',
    });
    console.log('安装右侧树-treeData.value', treeData.value);
  });
};
const selectSameMajor = e => {
  console.log('selectSameMajor', rightInfo, initTreeData.value);
  if (rightInfo.checkedKeys?.length === 0) {
    message.info('请选择单位工程');
    return;
  }
  let selectMajorList = [];
  let selectRows = initTreeData.value.filter(a => rightInfo.checkedKeys.includes(a.id));
  let list = [];
  let noSelectId = disUnit.map(a => a.id);
  initTreeData.value.map(a => {
    if (
      selectRows.find(
        d =>
          d.constructMajorType === a.constructMajorType &&
          a.levelType === 3 &&
          !noSelectId.includes(a.id)
      )
    )
      list.push(a.id);
  });
  rightInfo.checkedKeys = [...list];
};

const cellClassName = ({ $columnIndex, column, row }) => {
  if (!(['az', 'fwxs'].includes(row.id) && !['idx'].includes(column.field))) {
    return 'cell-Border';
  } else {
    return 'cell-BotoomBorder';
  }
};
const changeLineHeight = () => {
  //设置基数定额弹框项目特征展开收起状态
  tableDataThird.value.map(a => {
    if (a.kind === '03') a.lineHeight = !a.lineHeight;
  });
};
const cellClassNameTableNo3 = ({ $columnIndex, row, column }) => {
  // const selectName = selectedClassName({ $columnIndex, row, column });
  if (!column.field === 'projectAttr') return;
  if (row.lineHeight) {
    return 'row-more';
  } else if (!row.lineHeight && row.kind === '03') {
    return 'row-single';
  }
};
</script>
<style lang="scss" scoped>
@use './tableIcon.scss';
@use './projectAttr.scss';
.selectContent {
  display: inline-block;
  height: 35px;
  line-height: 35px;
  margin: -10px 0 5px 0;
  span {
    margin: 0 10px 0 0;
  }
  .btnAdd,
  .btnSub {
    width: 30px !important;
    display: inline-block;
    height: 30px !important;
    line-height: 30px;
    cursor: pointer;
    margin: 0 !important;
  }
  .fontsingle {
    margin: 0 0px 0 3px;
  }
}
.table-content {
  .upContent {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 270px;
    margin: 0;
    &-left {
      height: 100%;
      width: 83%;
      &-table {
        width: calc(100% - 10px);
        height: calc(100% - 35px);
      }
    }
  }
  // .tableNo1 {
  //   width: calc(100% - 10px);
  //   height: calc(100% - 35px);
  // }
  .tableNo2 {
    width: 100%;
    margin: 10px 0 0px;
  }
  .detailQD {
    display: inline-block;
    width: calc(100% - 40px);
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; // 默认不换行；
  }
  .detailTitle {
    margin-right: 15px;
  }
}

.asideTree {
  width: 17%;
  border: 1px solid #d9d9d9;
  height: calc(100% - 2px + 10px);
  margin: -10px 0 0 0;
  padding: 0 2px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.18);
  ::v-deep .ant-tree {
    font-size: 12px;
    overflow-y: auto;
    height: 100%;
    overflow-x: hidden;
    .ant-tree-node-content-wrapper {
      width: calc(100% - 5px);
      text-overflow: ellipsis;
      overflow-x: hidden;
      white-space: nowrap;
    }
  }
  &-title {
    padding: 10px 10px 0px 10px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d9d9d9;
    .ant-btn-primary {
      margin: 0px;
    }
  }
  &-content {
    height: calc(100% - 58px);
    // overflow-x: scroll;
    ::v-deep .ant-tree-checkbox-inner {
      border: 1px solid #1890ff;
    }
  }
  .check-labels {
    white-space: nowrap;
  }
  ::-webkit-scrollbar {
    //滚动条的宽度
    width: 4px; //纵向滚动条的宽度
    height: 6px; //横向滚动条的高度
    background-color: transparent;
  }
}
.tableNo3 {
  height: calc(100% - 35px);
  &-table {
    height: calc(100% - 50px);
  }
  .footer {
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 0;
    .radioList,
    .btnList {
      margin-top: 10px;
    }
  }
}
.tableNo4 {
  height: 370px;
}
.btnsOut {
  width: 10%;
  display: flex;
  margin: 10px auto 0;
  justify-content: space-around;
}
.btns {
  width: 10%;
  display: flex;
  margin: 10px auto 0;
  justify-content: space-around;
}
.line {
  width: 100%;
  height: 7px;
  background: rgba(221, 221, 221, 0.39);
  opacity: 0.52;
  margin: 20px 0;
}
.detail {
  width: 73%;
}
.detailBtn,
.detailBtn1 {
  position: absolute;
  right: 5px;
  bottom: 3px;
  width: 42px;
  font-size: 10px;
  height: 20px !important;
  line-height: 20px;
}
.detailBtn1 {
  right: 10px;
}
.ComputeContent {
  width: 100%;
  height: 70%;
  position: relative;
}
.selectRightBtn {
  position: absolute;
  bottom: -27px;
  left: 20px;
}
.selectBtn {
  display: flex;
  position: absolute;
  bottom: -27px;
  right: 20px;
}
::v-deep(.vxe-table) {
  .vxe-tree--line {
    /* 修改连接线的颜色 */
    border-left: 1px solid #87b2f2;
    border-bottom: 1px solid #87b2f2;
  }
}
::v-deep(.vxe-table .title-row-bgColor) {
  font-size: 12px;
  font-weight: 600;
  background-color: var(--vxe-table-row-current-background-color);
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2;
}
::v-deep(.vxe-table .row-qd) {
  background: rgba(166, 195, 250, 0.2);
}
::v-deep(.vxe-body--row.row--current) {
  background: #a6c3fa;
}
:deep(.ant-input-number-input) {
  padding: 0 0px 0 3px;
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px;
}
::v-deep .table-content .selectContent .ant-btn-primary {
  margin: -10px 10px 0 0 !important;
  height: 30px;
  font-size: 12px;
}

::v-deep .detail .ant-btn-primary {
  margin: 0 0 !important;
}
::v-deep .ant-input-group-addon {
  padding: 0 0 !important;
}

::v-deep(.vxe-select > .vxe-input) {
  //在原来基础上减去随机数1px,解决了小屏中vxe-select挡住表格边线问题
  $mini-hight: calc(var(--vxe-input-height-mini) - 1);
  height: $mini-hight;
  line-height: $mini-hight;
  .vxe-input--inner {
    border: none !important;
  }
}
::v-deep .ant-input-number-handler-wrap {
  display: none;
}
::v-deep .ant-input-number-group-addon {
  padding: 0px;
}
.advanced-content {
  .single {
    border: 1px solid #d9d9d9;
    font-size: 14px;
    padding: 15px 0 15px 15px;
    margin-bottom: 12px;
    position: relative;
    .title {
      position: absolute;
      font-size: 12px;
      color: #287cfa;
      top: -10px;
      background: white;
      .icon-font {
        margin-right: 6px;
      }
    }
    .vxe-checkbox {
      color: black;
    }
  }
  .single:nth-of-type(2) {
    margin-top: 20px;
  }
  .radioList {
    .ant-radio-wrapper {
      display: block;
      font-size: 12px;
    }
  }
}
.select-type {
  ::v-deep .ant-select-selector {
    .ant-select-selection-item {
      font-size: 13px;
    }
  }
  &-jtx {
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
    margin-top: 17px;
    &-ipt {
      display: inline-block;
      border: 1px solid #bfbfbf;
      margin: 0;
      width: calc(100% - 60px);
      height: 24px;
      padding-left: 10px;
      line-height: 20px;
      &-disSpan,
      &-span {
        // margin-right: 5px;
        display: inline-block;
        width: 20px;
        height: 100%;
        float: right;
        text-align: center;
      }
      &-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 248px;
        display: inline-block;
      }
      &-disSpan {
        background: #d9d9d9;
      }
      &-span {
        cursor: pointer;
      }
    }
  }
}
// ::v-deep(.surely-table) {
//因为无子级和有子级样式不一样，commonScss做区分改动
//   .surely-table-append-node {
//     display: inline-block !important;
//     margin-right: 5px;
//     @for $i from 1 to 9 {
//       .indent-level-#{$i} {
//         padding-left: if($i < 5, $i * 13px, 25px) !important;
//       }
//     }
//   }
// }
.tooltip {
  p {
    margin: 0;
  }
}
::v-deep(.redColor) {
  color: red !important;
  .vxe-input--inner {
    color: red !important;
  }
}
</style>
<style lang="scss">
.tableFirst {
  .vxe-table--render-default.is--tree-line .vxe-body--row .cell-Border {
    background-image: linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1)),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--render-default.is--tree-line .vxe-body--row .cell-BotoomBorder {
    border-bottom: 1px solid rgba(185, 185, 185, 1);
  }
}
</style>
