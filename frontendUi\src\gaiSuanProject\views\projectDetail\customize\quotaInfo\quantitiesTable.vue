<!--
 * @Descripttion: 工程量明细
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-10-21 09:01:41
-->
<template>
  <div class="contents-table">
    <div
      class="head"
      v-show="
        !['00','05','01','02','07','-1'].includes(props.currentInfo?.kind) &&
        props.currentInfo.isTempRemove !== 1 &&
        props.currentInfo.resQty !== 0 &&
        props.currentInfo.kind != '-1' &&
        !(
          currentInfoParent?.kind === '03' &&
          currentInfoParent?.originalQuantity == 0
        )
      "
    >
      <a-button
        type="text"
        @click="addQuantityData(currentInfo)"
        :disabled="loading"
        ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button
      >

      <a-button
        type="text"
        @click="moveQuantityData(currentInfo, 0)"
        :disabled="currentInfo?.sortNo === 1 || loading"
        ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button
      >
      <a-button
        type="text"
        @click="moveQuantityData(currentInfo, 1)"
        :disabled="currentInfo?.sortNo === props.tableData?.length || loading"
        ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button
      >

      <a-button
        type="text"
        @click="delQuantityData(currentInfo)"
        :disabled="loading"
        ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button
      >
    </div>
    <div class="content">
      <vxe-table
        :data="props.tableData"
        show-overflow
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :loading="loading"
        height="auto"
        class="table-scrollbar table-edit-common"
        show-footer
        footer-align="right"
        footer-row-class-name="footer-show"
        :menu-config="menuConfig"
        @menu-click="contextMenuClickEvent"
        @edit-closed="editClosedEvent"
        @current-change="currentChangeEvent"
        :footer-span-method="footerColspanMethod"
        :footer-method="footerMethod"
        ref="vexTable"
        keep-source
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
          enabled: isEditEnabled,
        }"
        :cell-class-name="selectedClassName"
        @cell-click="useCellClickEvent"
      >
        <vxe-column title="序号" width="60">
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          title="计算说明"
          field="mathIllustrate"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="
                props.currentInfo.isTempRemove !== 1 &&
                ((currentInfoParent?.kind === '03'&&currentInfoParent.quantity != 0)||currentInfoParent?.kind !== '03') &&
                !['00','05','01','02','07','-1'].includes(props.currentInfo?.kind) &&
                !(
                  currentInfoParent?.kind === '03' &&
                  currentInfoParent?.originalQuantity == 0
                )
              "
              v-model="row.mathIllustrate"
              type="text"
              placeholder="请输入计算说明"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          title="计算式"
          field="mathFormula"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="
                props.currentInfo.isTempRemove !== 1 &&
                ((currentInfoParent?.kind === '03'&&currentInfoParent.quantity != 0)||currentInfoParent?.kind !== '03') &&
                !['00','05','01','02','07','-1'].includes(props.currentInfo?.kind) &&
                !(
                  currentInfoParent?.kind === '03' &&
                  currentInfoParent?.originalQuantity == 0
                )
              "
              v-model="row.mathFormula"
              type="text"
              placeholder="请输入计算式"
            ></vxe-input>
            <!-- <icon-font
              type="icon-bianji"
              class="more-icon"
              @click.stop="comModel = true"
            ></icon-font> -->
          </template>
        </vxe-column>
        <vxe-column title="结果" field="mathResult"></vxe-column>
        <vxe-column
          title="变量引用"
          field="variables"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              v-if="
                props.currentInfo.isTempRemove !== 1 &&
                ((currentInfoParent?.kind === '03'&&currentInfoParent.quantity != 0)||currentInfoParent?.kind !== '03') &&
                !['00','05','01','02','07','-1'].includes(props.currentInfo?.kind) &&
                !(
                  currentInfoParent?.kind === '03' &&
                  currentInfoParent?.originalQuantity == 0
                )
              "
              v-model="row.variables"
              type="text"
              placeholder="请输入变量"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column title="累加标识" field="accumulateFlag">
          <template #default="{ row }">
            <vxe-checkbox
              v-if="
                props.currentInfo.isTempRemove !== 1 &&
                !['00','05','01','02','07','-1'].includes(props.currentInfo?.kind)
              "
              v-model="row.accumulateFlag"
              :checked-value="1"
              :unchecked-value="0"
              :disabled="props.currentInfo.isLocked||(currentInfoParent?.kind === '03'&&currentInfoParent.quantity == 0)"
              @change="checkboxChange(row)"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
  <info-modal
    v-model:infoVisible="infoVisible"
    :infoText="infoText"
    :isSureModal="isSureModal"
  ></info-modal>
  <common-modal
    className="dialog-comm"
    :title="'提示'"
    :width="500"
    :height="200"
    v-model:modelValue="visible"
    @close="visible = false"
    :mask="true"
    :lockView="true"
  >
    <tip
      v-if="visible"
      @close="cancel"
      @updateData="updateQuantityData(tipRow, 'accumulateFlag')"
      @add="addData"
    ></tip>
  </common-modal>
    <!-- 工程规模编辑 -->
    <common-modal
    className="dialog-comm noMask"
    title="工程量编辑"
    width="800"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModelCLose"
    :mask="false"
    style="position: releative"
  >
    <IntroductionQuantity
      :textValue="currentInfo.originalQuantity"
      ref="comArea"
    ></IntroductionQuantity>
    <span class="btns">
      <a-button @click="comModelCLose()">取消</a-button>
      <a-button type="primary" @click="comModelsureData()">确定</a-button>
    </span>
  </common-modal>
</template>

<script setup>
import api from '@/gaiSuanProject/api/projectDetail';
import { isNumericExpression, everyNumericHandler } from '@/utils/index';
import { message } from 'ant-design-vue';
import { reactive, ref, watch, nextTick, inject, computed } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode.js';
import { useCellClick } from '@gaiSuan/hooks/useCellClick';
import tip from './tip.vue';
import IntroductionQuantity from '../subItemProject/components/IntroductionQuantity.vue';

const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();
const props = defineProps(['tableData', 'type', 'currentInfo','componentName']);
const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();
const { subItemProjectData } = inject('subItemProjectData');
const originalData = ref([]);
let infoVisible = ref(false);
let visible = ref(false);
let infoText = ref('计算式输入非法，请重新输入标准四则运算表达式或数值');
let isSureModal = ref(false);
let vexTable = ref();
let currentInfo = ref();
let copyObj = ref(); // 复制数据
let varObjMap = new Map(); // 工程量明细 变量 对象map：key为变量值， value是明细每行数据对象
let pathArr = ref([]); // 变量引用路径临时缓存
let allResult = []; // 变量是否循环引用及每条循环引用路径 缓存
let loading = ref(false); // 数据加载loading
let isEditEnabled = ref(true); // 是否可编辑行
let comModel = ref(false);
let tipRow = ref({});
let currentInfoParent = ref();
watch(
  () => props.currentInfo,
  val => {
    console.info(3333333333,val);
    if (!val || !val.parentId) return null; // or some default value
    currentInfoParent.value = subItemProjectData.value.find(
      a => a.sequenceNbr === val.parentId
    );
  }
);
watch(
  () => props.tableData,
  () => {
    currentInfoParent.value = subItemProjectData.value.find(
      a => a.sequenceNbr === props.currentInfo.parentId
    );
    // 遍历生成 varObjMap
    varObjMap.clear();
    if (props.tableData && props.tableData.length > 0) {
      props.tableData.forEach((item, index) => {
        loading.value = false;
        item.sortNo = index + 1;
        if (index === 0) {
          if (!currentInfo.value) {
            currentInfo.value = item;
          }
        }
        if (item.variables && item.variables.length > 0) {
          varObjMap.set(item.variables.toUpperCase(), item);
        }
      });
      originalData.value = JSON.parse(JSON.stringify(props.tableData));
      nextTick(() => {
        setEditEnabled();
      });
    }
  },
  {
    immediate: true,
  }
);

watch(() => {
  props.currentInfo.isTempRemove,
    (n, o) => {
      console.log('nononoaa', n, o);
      if (props.currentInfo) {
        setEditEnabled();
      }
    },
    {
      deep: true,
      immediate: true,
    };
});
const comModelCLose = () => {
  comModel.value = false;
};
const comModelsureData = () => {
  comModel.value = false;
};
// 选中单条预算书数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
  setEditEnabled();
};
const checkboxChange = row => {
  // console.log(
  //   '累加标识',
  //   props.currentInfo.quantityExpression,
  //   props.currentInfo.quantityVariableName
  // );
  // console.log('累加标识-----------row', row);
  // if (
  //   !props.currentInfo.quantityExpression.includes(
  //     props.currentInfo.quantityVariableName
  //   )
  // ) {
  //   infoMode.show({
  //     iconType: 'icon-qiangtixing',
  //     infoText: '工程量表达式已有数据，是否替换？',
  //     autoClose: true,
  //     confirm: () => {
  //       updateQuantityData(row, 'variables');
  //       infoMode.hide();
  //     },
  //     close: () => {
  //       let $table = vexTable.value;
  //       $table.revertData(row);
  //     },
  //   });
  // } else {
  updateQuantityData(row, 'accumulateFlag');
  // }
};

const checkVariables = row => {
  const variables = row.variables || '';
  if (variables.trim() === '') return [true, '不能为空'];
  const reg = /[\-|\*|\+|\/]/g;
  if (reg.test(variables)) return [true, '不可输入+-*/运算符'];
  const regNum = /^[0-9\.]*$/;
  if (regNum.test(variables)) return [true, '不可输入纯数字'];
  const mapInfo = varObjMap.get(variables?.toUpperCase());
  if (mapInfo && mapInfo.sequenceNbr !== row.sequenceNbr) {
    return [true, '变量引用值已存在'];
  }
  return [false, ''];
};
// 修改工程量明细数据
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  console.log('111111111111', $table.isUpdateByRow(row, column.field));
  if (!$table.isUpdateByRow(row, column.field)) return;
  console.log('varObjMap', varObjMap);
  let varUpperCase = row.variables?.toUpperCase();
  if (column.field === 'variables') {
    const [isFail, msg] = checkVariables(row);
    if (isFail) {
      $table.revertData(row, 'variables');
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = msg;
      return;
    }
  }
  pathArr.value.push(varUpperCase);
  let result = selfReferCheckReal(
    row.variables?.toUpperCase(),
    varObjMap,
    pathArr.value
  );
  pathArr.value.pop();
  if (result) {
    $table.revertData(row, 'mathFormula');
    infoVisible.value = true;
    isSureModal.value = true;
    infoText.value = '计算式输入非法，请重新输入标准四则运算表达式或数值';
    return;
  }
  // 计算式不合法给提示
  row.mathFormula = replaceChineseBrackets(row.mathFormula);
  const [isSuccess, msg] = isNumericExpression(row.mathFormula, varObjMap);
  if (column.field === 'mathFormula' && isSuccess) {
    $table.revertData(row, 'mathFormula');
    infoVisible.value = true;
    isSureModal.value = true;
    infoText.value = msg;
    return;
  }
  if (column.field === 'mathFormula') {
  }
  console.log('nono', column.field, props.currentInfo);
  // 计算说明，计算式，计算引用修改了，都提示
  if (
    ['mathFormula', 'mathIllustrate', 'variables'].includes(column.field) &&
    props.currentInfo.originalQuantityExpression === 'QDL' &&
    props.currentInfo.quantity != 0 &&
    row.mathFormula
  ) {
    tipRow.value = row;
    visible.value = true;
    return;
  }
  // if (
  //   ['mathFormula', 'mathIllustrate', 'variables'].includes(column.field) &&
  //   props.currentInfo.originalQuantityExpression === 'GCLMXHJ' &&
  //   (row.mathFormula || row.mathIllustrate || row.variables)
  // ) {
  //   infoMode.show({
  //     iconType: 'icon-qiangtixing',
  //     infoText: '已从工程量明细中引用，修改工程量将清空明细区数据，是否继续？',
  //     confirm: () => {
  //       updateQuantityData(row, column.field);
  //       infoMode.hide();
  //     },
  //     close: () => {
  //       infoMode.hide();
  //       let $table = vexTable.value;
  //       $table.revertData(row);
  //     },
  //   });
  //   return;
  // }
  let obj = originalData.value.filter(
    x => x.sequenceNbr === row.sequenceNbr
  )[0];
  console.log('obj1111111', obj);
  if (JSON.stringify(obj) === JSON.stringify(row)) {
    return;
  }
  updateQuantityData(row, column);
};
function replaceChineseBrackets(str) {
  return str
    .replace(/[（（]/g, '(')
    .replace(/[））]/g, ')')
    .replace(/×/g, '*')
    .replace(/÷/g, '/');
}
const cancel = type => {
  if (type) {
    let $table = vexTable.value;
    $table.revertData(currentInfo.value);
  }
  visible.value = false;
};
// 修改工程量明细数据
const updateQuantityData = (row, column) => {
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(row)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    quotaListId: props.currentInfo?.sequenceNbr,
  };
  loading.value = true;
  console.log('修改工程量明细参数', apiData, column);
  api.updateQuantityData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('修改成功');
      tipRow.value = {};
      emits('updateData', column);
      if (infoVisible.value) infoVisible.value = false;
    }
  });
};
const addData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineId: props.currentInfo?.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(tipRow.value)),
  };
  let $table = vexTable.value;
  $table.revertData(currentInfo.value);
  api.append(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('修改成功');
      tipRow.value = {};
      emits('updateData', 'accumulateFlag');
      if (infoVisible.value) infoVisible.value = false;
    }
  });
};
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'add',
          name: '插入',
        },
        // {
        //   code: 'copy',
        //   name: '复制',
        //   visible: true,
        // },
        // {
        //   code: 'paste',
        //   name: '粘贴',
        //   visible: true,
        //   disabled: true,
        // },
        {
          code: 'moveUp',
          name: '上移',
          visible: true,
          disabled: false,
        },
        {
          code: 'moveDown',
          name: '下移',
          visible: true,
          disabled: false,
        },
        {
          code: 'delete',
          name: '删除',
          visible: true,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, column, columnIndex, row, rowIndex);
    if (!row) return;
    currentInfo.value = row;
    vexTable.value.setCurrentRow(currentInfo.value);
    options.forEach(list => {
      console.log('list', list, rowIndex);
      list.forEach(item => {
        if (rowIndex === 0 && item.code === 'moveUp') {
          item.disabled = true;
        } else if (
          rowIndex === props.tableData.length - 1 &&
          item.code === 'moveDown'
        ) {
          item.disabled = true;
        } else if (item.code === 'paste' && !copyObj.value) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
    });
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (menu.code === 'delete') {
    delQuantityData(row);
  } else if (menu.code === 'add') {
    addQuantityData(row);
  } else if (menu.code === 'copy') {
    copyObj.value = row;
  } else if (menu.code === 'paste') {
    pasteQuantityData(row);
  } else if (menu.code === 'moveUp') {
    moveQuantityData(row, 0);
  } else if (menu.code === 'moveDown') {
    moveQuantityData(row, 1);
  }
};

const footerColspanMethod = ({ $rowIndex, _columnIndex }) => {
  if ($rowIndex === 0) {
    if (_columnIndex === 0) {
      return {
        rowspan: 1,
        colspan: 6,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const footerMethod = ({ columns, data }) => {
  let sum = 0;
  let tempList = data.filter(x => x.accumulateFlag);
  console.log('数据集', tempList);
  if (!currentInfo.value) {
    currentInfo.value = props.tableData && props.tableData[0];
    vexTable.value.setCurrentRow(currentInfo.value);
  }
  sum = tempList.reduce(
    (accumulator, currentItem) =>
      accumulator + (currentItem['mathResult'] || 0),
    0
  );
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '计算结果    ' + Number(sum.toFixed(6));
      }
      return null;
    }),
  ];
  return footerData;
};

const openInfoModal = (msg, isVisible = true, sureModal = false) => {
  infoVisible.value = isVisible;
  isSureModal.value = sureModal;
  infoText.value = msg;
};

// 删除工程量明细数据
const delQuantityData = row => {
  const variables = row.variables;
  if (variables && isVariablesQuote(variables, props.tableData)) {
    openInfoModal('工程量表达式已被引用，不可删除当前数据', true, true);
    return;
  }

  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineId: props.currentInfo?.sequenceNbr,
    deleteId: row.sequenceNbr,
  };
  api.delQuantityData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      currentInfo.value = null;
      emits('updateData', 'quantitiesDel');
    }
  });
};

// 增加工程量明细
const addQuantityData = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineId: props.currentInfo?.sequenceNbr,
    selectId: vexTable.value?.getCurrentRecord()?.sequenceNbr,
  };
  api.addQuantityData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('插入成功');
      emits('updateData');
    }
  });
};

// 移动工程量明细数据
const moveQuantityData = (row, type) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineInfo: JSON.parse(JSON.stringify(row)),
    direction: type, // 0上 1下
  };
  console.log('移动参数', apiData);
  api.moveQuantityData(apiData).then(res => {
    console.log('移动结果', res);
    if (res.status === 200 && res.result) {
      currentInfo.value = row;
      message.success('移动成功');
      currentInfo.value.sortNo =
        type === 1
          ? currentInfo.value.sortNo + 1
          : currentInfo.value.sortNo - 1;
      emits('updateData');
    }
  });
};

// 粘贴数据
const pasteQuantityData = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineInfo: JSON.parse(JSON.stringify(copyObj.value)),
    pasteId: row.sequenceNbr,
  };
  console.log('粘贴参数', apiData);
  api.pasteQuantityData(apiData).then(res => {
    console.log('粘贴结果', res);
    if (res.status === 200 && res.result) {
      message.success('粘贴成功');
      copyObj.value = null;
      emits('updateData');
    }
  });
};

/**
 * 分解变量，默认公式中仅包含"+-*\/()"运算符
 */
const decomposeVars = formula => {
  let fs = formula.split(/-|\+|\*|\/|\(|\)/);
  let result = [];
  fs.forEach(f => {
    f = f.trim();
    if (f.length > 0) {
      result.push(f);
    }
  });
  return result;
};

/**
 * 循环引用校验
 * @param key 当前校验的变量
 * @param map  工程量明细 变量 对象map
 * @param pathArr 变量引用路径临时缓存
 * @returns {boolean} 是否循环引用
 */
const selfReferCheckReal = (key, map, pathArr) => {
  let result = false;
  let obj = map.get(key);
  if (!obj) return;
  let fs = decomposeVars(obj.mathFormula);
  console.log('fs', fs, obj.mathFormul);

  let i = 0;
  for (i = 0; i < fs.length; i++) {
    let varUpperCase = fs[i].toUpperCase();
    //判断变量名是否在map中存在，如果不存在 fs[i] 应该是一个数值
    if (map.has(varUpperCase)) {
      if (pathArr.indexOf(varUpperCase) > -1) {
        // 变量引用路径中已存在当前循环变量
        pathArr.push(varUpperCase);
        result = true;
        allResult.push('存在循环引用路径：' + pathArr.join(' => '));
        pathArr.pop();
      } else {
        pathArr.push(varUpperCase);
        // 如果调用后返回false，保留result之前的值
        result = selfReferCheckReal(varUpperCase, map, pathArr) || result;
        pathArr.pop();
      }
    }
  }

  return result;
};

/**
 * 变量是否被引用
 * @param {变量名} variables
 * @param {检测的数据} dataList
 */
const isVariablesQuote = (variables, dataList) => {
  for (let i = 0, len = dataList.length; i < len; i++) {
    const { mathFormula } = dataList[i];
    const mathArr = decomposeVars(mathFormula);
    if (mathArr.includes(variables)) return true;
  }
  return false;
};

// 是否编辑处理
const setEditEnabled = () => {
  if (props.currentInfo.isLocked) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};
</script>

<style lang="scss" scoped>
.contents-table {
  height: 100%;
  .content {
    height: calc(100% - 40px);
  }
}
:deep(.footer-show) {
  background: #f3f3f3;
  font-size: 14px;
  color: #000000;
  .vxe-cell--item {
    padding-right: 15px;
  }
}
.btns {
  position: absolute;
  width: 200px;
  bottom: 10px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
</style>
