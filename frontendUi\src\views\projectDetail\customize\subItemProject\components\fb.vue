<!--
 * @Descripttion: 整理子目 ==》 分部整理
-->
<script setup>
import { onMounted, ref, toRaw } from 'vue';
import { message } from 'ant-design-vue';
import api from '@/api/projectDetail.js';
import CommonModal from '@/components/global/commonModal/index.vue';
import { projectDetailStore } from '@/store/projectDetail.js';
const props = defineProps(['subitemIdentification']);
const emits = defineEmits(['update:subitemIdentification', 'updateData']);
const projectStore = projectDetailStore();
const loading = ref(false);
const checkboxValue = ref([]); //'1','2','3','4'
const checkboxChange = e => {
  console.log(e, checkboxValue.value);
};
const init = async () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  try {
    loading.value = true;
    console.log('获取单位分部整理数据参数', apiData);
    let res = await api.queryFbArrangeList(apiData);
    console.log('获取单位分部整理数据返回数据res', res);
    if (res.status === 200 && Array.isArray(res.result)) {
      checkboxValue.value = res.result;
    }
  } catch (e) {
    console.error('获取单位分部整理数据', e);
  } finally {
    loading.value = false;
  }
};
const sureHandle = async () => {
  if (checkboxValue.value.length > 0) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      typeList: toRaw(checkboxValue.value),
    };
    try {
      loading.value = true;
      console.log('分部整理参数', apiData);
      let res = await api.branchArrangeColl(apiData);
      console.log('分部整理返回数据res', res);
      if (res.status === 200) {
        if (res.result === true) {
          emits('updateData');
          cancel();
        } else {
          message.warn(res.result);
        }
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.error('分部整理', e);
    } finally {
      loading.value = false;
    }
  } else {
    message.info('请先选择分部整理条件');
  }
};
const popUpStatus = ref(false);
onMounted(() => {
  popUpStatus.value = true;
  init();
});
const cancel = () => {
  emits('update:subitemIdentification', '');
  popUpStatus.value = false;
};
</script>

<template>
  <div class="batch-delete">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="popUpStatus"
      title="分部整理"
      :mask="true"
      :loading="loading"
      :lockView="false"
      :lockScroll="false"
      width="600px"
      @cancel="cancel"
      @close="cancel"
    >
      <a-row>
        <a-col :span="24">
          <div style="width: 100%;height: 100%;border: 1px solid #B9B9B9;padding: 13px 10px;color: #2A2A2A ">
            <a-checkbox-group
              v-model:value="checkboxValue"
              @change="checkboxChange"
            >
              <a-row style="font-size: 14px;line-height: 30px;">
                <a-col :span="24">
                  <a-checkbox value="1">按专业整理分部</a-checkbox>
                </a-col>
                <a-col :span="24">
                  <a-checkbox value="2">按章整理分部</a-checkbox>
                </a-col>
                <a-col :span="24">
                  <a-checkbox value="3">按节整理分部</a-checkbox>
                </a-col>
                <a-col :span="24">
                  <a-checkbox value="4">删除自定义分部</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
            <a-row style="font-size: 14px;line-height: 24px;margin-top: 10px;">
              <a-col :span="24">
                说明信息:
              </a-col>
              <a-col :span="24">
                执行分部整理将对当前清单列表存在的清单按以上勾选项进行整理
              </a-col>
              <a-col :span="24">
                勾选”按专业整理分部”，表示分部整理时需要添加清单专业标题
              </a-col>
              <a-col :span="24">
                勾选“按章节整理分部”，表示分部整理时需要添加清单章标题
              </a-col>
              <a-col :span="24">
                勾选“按节整理分部”，表示分部墪理申需要添加节标题
              </a-col>
              <a-col :span="24">
                勾选“删除自定义分部”，表示执行分部整理时首先删除已有自定义分部标题和标准章节分部标题，然后再执行分部整理
              </a-col>
            </a-row>
          </div>
        </a-col>
        <a-col :span="24">
          <div class="btn-list">
            <a-button @click="cancel">取消</a-button>
            <a-button
              type="primary"
              @click="sureHandle"
            >确定</a-button>
          </div>
        </a-col>
      </a-row>
    </common-modal>
  </div>
</template>

<style scoped lang="scss">
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  width: 100%;

  button + button {
    margin-left: 10px;
  }
}
</style>