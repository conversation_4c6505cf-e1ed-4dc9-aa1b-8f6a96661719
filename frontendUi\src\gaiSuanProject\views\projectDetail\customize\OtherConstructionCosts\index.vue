<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <div class="table-content">
    <vxe-table
      align="center"
      v-if="tableData.length !== 0"
      ref="humanTable"
      :loading="loading"
      height="auto"
      :menu-config="menuConfig"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: 'sequenceNbr',
        useKey: true,
      }"
      :tree-config="{
        children: 'children',
        transform: true,
        expandAll: true,
        line: true,
        reserve: true,
        rowField: 'sequenceNbr',
        parentField: 'parentId',
        iconOpen: 'vxe-icon-square-minus',
        iconClose: 'vxe-icon-square-plus',
      }"
      :data="tableData"
      @edit-closed="editClosedEvent"
      keep-source
      @menu-click="contextMenuClickEvent"
      @cell-click="useCellClickEvent"
      class="table-edit-common"
      :cell-class-name="selectedClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :scroll-y="{ enabled: true, gt: 30 }"
      @current-change="currentChange"
      show-overflow
      @checkbox-change="selectChangeEvent"
    >
    <vxe-column
        tree-node
        field="sortIndex"
        min-width="120"
        title="序号"
      ></vxe-column>
      <vxe-column
        field="dispNo"
        width="120"
        title="编码"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <span v-if="row.levelType === 0">{{ row.dispNo }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
          ></vxe-input> </template
      ></vxe-column>
      <vxe-column field="code" width="120" title="费用代号" :edit-render="{}">
        <template #edit="{ row }">
          <span v-if="row.levelType === 0">{{ row.code }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.code"
            type="text"
            @keyup="validateAndFormatCode(row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="name" width="150" title="名称" :edit-render="{}">
        <template #edit="{ row }">
          <span v-if="row.levelType === 0">{{ row.name }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.name"
            type="text"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="unit" width="60" title="单位"></vxe-column>
      <vxe-column
        field="calculationMethod"
        width="150"
        title="计算方式"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <span>{{ getCalculation(row.calculationMethod) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-if="row.children.length < 1"
            v-model="row.calculationMethod"
            :clearable="false"
            transfer
          >
            <vxe-option
              v-for="item in methodOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{ getCalculation(row.calculationMethod) }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="price"
        width="120"
        title="单价/计算基数"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <!-- <span v-if="row.children.length>0&&row.levelType === 0 || row.calculationMethod === 3 || row.calculationMethod === 4">{{ row.price
            }}</span> -->
          <vxe-input
            v-if="row.children.length < 1 && row.calculationMethod === 1"
            :clearable="false"
            v-model.trim="row.price"
            type="number"
            @blur="row.price = pureNumberSHL(row.price, 2)"
          ></vxe-input>
          <!-- <span v-else>{{row.price}}</span> -->
          <!-- /[^\w\-\+\*\/]/g 
           @blur="
              row.price = row.price.replace(/[^+\-\*\/\d\w\u4e00-\u9fa5]/g, '')
            "
          -->
          <cell-textarea
            v-else-if="row.children.length < 1 && row.calculationMethod === 2"
            :clearable="false"
            v-model.trim="row.price"
            :textHeight="row.height"
          ></cell-textarea>
          <span v-else>{{ row.price }}</span>

          <icon-font
            v-if="
              row.children.length < 1 &&
              row.levelType !== 0 &&
              row.calculationMethod === 2
            "
            type="icon-bianji"
            class="more-icon"
            @click="editCalc(row)"
          ></icon-font>
        </template>
      </vxe-column>
      <vxe-column
        field="priceDescription"
        width="120"
        title="基数说明"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row.levelType !== 0"
            :clearable="false"
            v-model.trim="row.priceDescription"
            type="text"
          ></vxe-input>
          <span v-else>{{ row.priceDescription }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="quantity"
        width="120"
        title="数量/费率(%)"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <!-- <vxe-input v-if="row.children.length<1&& (row.calculationMethod === 1||row.calculationMethod === 2)" :clearable="false" v-model.trim="row.quantity"  type="number" 
            @blur="(row.quantity = pureNumberSHL(row.quantity, 2))" @keyup="row.quantity = pureNumber(row.quantity)"></vxe-input> -->
          <cell-textarea
            v-if="row.children.length<1&& (row.calculationMethod === 1||row.calculationMethod === 2)"
            :clearable="false"
            v-model.trim="row.quantity"
            :textHeight="row.height"
          ></cell-textarea>
          <span v-else>{{ row.quantity }}</span>
        </template>
      </vxe-column>
      <vxe-column field="amount" width="120" title="金额" :edit-render="{}">
        <template #edit="{ row }">
          <!-- <vxe-input
            v-if="
              row.levelType !== 0 &&
              row.calculationMethod === 4 &&
              row.children.length < 1
            "
            :clearable="false"
            v-model.trim="row.amount"
            type="number"
            @blur="row.amount = pureNumberSHL(row.amount, 2)"
            @keyup="row.amount = pureNumber(row.amount)"
          ></vxe-input> -->
          <cell-textarea
            v-if="row.levelType !== 0 && row.calculationMethod === 4 && row.children.length < 1"
            :clearable="false"
            v-model.trim="row.amount"
            :textHeight="row.height"
          ></cell-textarea>
          <span v-else>{{ row.amount }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="category"
        width="180"
        title="费用类别"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select
            v-if="row.children.length == 0"
            v-model="row.category"
            @change="categoryChange(row)"
          >
            <vxe-option
              v-for="(item, index) in treeData"
              :key="index"
              :value="item"
              :label="item"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{ row.category }}</span>
        </template>
      </vxe-column>
      <!-- <vxe-column field="ifCalculate" width="120" title="是否计算" :cell-render="{}">
        <template #default="{ row }">
         <vxe-checkbox
            v-model="row.ifCalculate"
            size="small"
            content=""
            @change="updateData(row, 'ifCalculate')"
          ></vxe-checkbox>
        </template> -->
      <!-- <template #default="{ row }">
          <a-checkbox v-if="row.levelType !== 0" v-model:checked="row.ifCalculate"></a-checkbox>
        </template>
        <template #edit="{ row }">
          <a-checkbox v-if="row.levelType !== 0" v-model:checked="row.ifCalculate"></a-checkbox>
        </template> -->
      <!-- </vxe-column> -->
      <vxe-column
        type="checkbox"
        width="120"
        title="是否计算"
        :edit-render="{}"
      >
        <template v-slot:header="{ column, $index }">
          <span>{{ column.title }}</span>
        </template>
        <!-- <template #default="{ row }"> -->
        <!-- <a-checkbox v-model:checked="row.ifCalculate"></a-checkbox> -->
        <!-- </template> -->
      </vxe-column>
      <vxe-column
        field="calculationProcess"
        width="120"
        title="计算过程"
      ></vxe-column>
      <vxe-column
        field="calculationBasis"
        width="120"
        title="计价依据"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <span v-if="row.levelType === 0">{{ row.calculationBasis }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.calculationBasis"
            type="text"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="remark" width="120" title="备注" :edit-render="{}">
        <template #edit="{ row }">
          <span v-if="row.levelType === 0">{{ row.remark }}</span>
          <vxe-input
            v-else
            :clearable="false"
            v-model.trim="row.remark"
            type="text"
          ></vxe-input>
        </template>
      </vxe-column>
      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="600"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
  >
    <content-down
      :isTextArea="true"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
  <common-modal
    v-model:modelValue="isPriceModel"
    className="dialog-comm"
    title="费用计算器"
    width="1200"
    height="700"
    @close="closePriceModel('isPriceModel')"
  >
    <orther-cost
      :isTextArea="true"
      :isPriceModel="isPriceModel"
      :textValue="textValue"
      ref="ortherFeeRef"
      :jsqsequenceNbr="jsqsequenceNbr"
      :jsqCategory="jsqCategory"
      @save="getHumanMachineData()"
    ></orther-cost>
    <div class="btns">
      <div class="btns-view">
        <a-button type="primary" @click="viewClick">查看</a-button>
      </div>
      <div class="btns-action">
        <a-button @click="closePriceModel('isPriceModel')">取消</a-button>
        <a-button type="primary" style="margin-left: 10px" @click="queryOther()"
          >应用</a-button
        >
      </div>
      <!-- <div>
        <a-button type="primary" @click="viewClick">应用</a-button>
      </div> -->
    </div>
  </common-modal>
  <!-- 计价文件查询 -->
  <common-modal
    v-model:modelValue="pricingDocumentQueryModal"
    className="dialog-comm"
    title="计价文件查询"
    width="1100"
    height="700"
    @close="closePriceModel('pricingDocumentQueryModal')"
  >
    <pricing-document-query
      ref="pricingDocumentQueryRef"
      :currentRecordValue="currentRecordValue"
    ></pricing-document-query>
  </common-modal>
  <common-modal
    v-model:modelValue="codeNullVisible"
    className="dialog-comm"
    title="提示"
    width="400px"
  >
    <div style="margin-bottom: 20px">置空费用代号将取消引用，是否确定？</div>
    <div class="footer-btn-list">
      <a-button @click="codeNullCancel">取消</a-button>
      <a-button type="primary" @click="codeNullQuery">确定</a-button>
    </div>
  </common-modal>
  <common-modal
    className="dialog-comm area-modal"
    width="500"
    @close="
      () => {
        deleteVisible = false;
      }
    "
    v-model:modelValue="deleteVisible"
    title="删除行"
  >
    <div class="tree-content-wrap">
      <div class="group-list">
        删除操作将导致费用代号取消引用，是否确定？
      </div>
      <div class="footer-btn-list">
        <a-button
          @click="
            () => {
              deleteVisible = false;
            }
          "
          >取消</a-button
        >
        <a-button
          type="primary"
          @click="
            () => {
              deleteVisible = false;
              delQuantityDataQuery();
            }
          "
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import {
  onMounted,
  ref,
  watch,
  getCurrentInstance,
  reactive,
  onActivated,
  toRaw,
  nextTick
} from 'vue';
import { message } from 'ant-design-vue';
import ContentDown from './ContentDown.vue';
import ortherCost from './ortherCost.vue';
import pricingDocumentQuery from './pricingDocumentQuery.vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@gaiSuan/api/csProject';
import { getUrl, pureNumber, pureNumberSHL,addLevelNumbers } from '@/utils/index';
import { insetBus } from '@gaiSuan/hooks/insetBus';

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const currentInfo = ref(null);
const emits = defineEmits([
  'refreshCurrentInfo',
  'tabClickBefore',
  'updateMenuList',
]);

import { useCellClick } from '@gaiSuan/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const isCurrent = ref(null);
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const jsqsequenceNbr = ref('');
const jsqCategory = ref('');
const codeNullVisible = ref(false);
const deleteVisible = ref(false);
const delRow = ref({});
const currentRecordValue = ref(null);
const humanTable = ref();
const treeData = ref([]);
const loading = ref(false);
const isPriceModel = ref(false);
const pricingDocumentQueryModal = ref(false);
const projectStore = projectDetailStore();
const tableData = ref([]);
let isPriceData = ref({});
let comModel = ref(false); //计算基数编写弹框
let textValue = ref('');
let oldValue = ref('');
const comArea = ref();
const ortherFeeRef = ref();
const methodOptions = ref([
  {
    label: '单价*数量',
    value: 1,
  },
  {
    label: '计算基数*费率',
    value: 2,
  },
  {
    label: '费用计算器',
    value: 3,
  },
  {
    label: '手动输入',
    value: 4,
  },
]);
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'insertRow',
          name: '新增行',
          visible: true,
          disabled: false,
          identification: 0,
        },
        {
          code: 'insertchildRow',
          name: '新增下级',
          visible: true,
          disabled: false,
          identification: 1,
        },
        {
          code: 'deleteRow',
          name: '删除行',
          visible: true,
          disabled: false,
          identification: 2,
        },
        {
          code: 'pricingDocumentQuery',
          name: '计价文件查询',
          visible: true,
          disabled: false,
          identification: 3,
        },
        {
          code: 'saveTemplate',
          name: '保存模版',
          visible: true,
          disabled: false,
          identification: 4,
        },
        {
          code: 'loadingTemplates',
          name: '载入模版',
          visible: true,
          disabled: false,
          identification: 5,
        },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    const $table = humanTable.value;
    if ($table) {
      $table.setCurrentRow(row);
      if (type === 'body') {
        for (const item of options[0]) {
          if (!row.permission.includes(item.identification)) {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
        }
        // options.forEach(list => {
        //   list.forEach(item => {
        //     item.disabled = false;
        //     // 如果为第一级则不可以新增行、删除操作
        //     if (row.levelType === 0 && (item.code === 'insertRow'||item.code === 'deleteRow')) {
        //         item.disabled = true;
        //     }
        //     // 如果为第三级则不可以新增下级操作
        //     if (row.levelType ===2 && item.code === 'insertchildRow') {
        //         item.disabled = true;
        //     }
        //   });
        // });
      }
    }
    return true;
  },
});
// 获取左侧树结构
const getTreeList = async () => {
  let apiData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
  };
  csProject.getOtherProjectCostCategoryEnum(apiData).then(async res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    res.result.unshift('无');
    treeData.value = res.result;
  });
};

const categoryChange = row => {
  if (row.category === '无') {
    row.category = '';
  }
};
const viewClick = () => {
  currentRecordValue.value = {
    name: ortherFeeRef.value.selTreeName,
  };
  pricingDocumentQueryModal.value = true;
};

const getDownList = (tree, result = []) => {
  // 遍历当前层级的所有节点
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    // 如果节点有子节点，则递归调用flattenTree
    if (node.children.length > 0) {
      getDownList(node.children, result);
    } else {
      // 将当前节点添加到结果数组中
      result.push(node);
    }
  }
  let nameArr = [];
  for (let item of result) {
    nameArr.push(item.name);
  }
  for (let i in nameArr) {
    if (
      nameArr[i] === '工程监理费（通用）' ||
      nameArr[i] === '工程监理费（河北）'
    ) {
      nameArr[i] = '工程监理';
    }
  }
  nameArr = [...new Set(nameArr)];
  // 返回结果数组
  return nameArr;
};
// 处理不能输入纯数组
const validateAndFormatCode = row => {
  const code = row.code.trim();
  const number = /^\d+$/;
  if (number.test(code)) {
    // 如果是纯数字
    row.code = '';
    return;
  }
  const codePattern = /^[a-zA-Z\u4e00-\u9fa5_][a-zA-Z0-9\u4e00-\u9fa5_]*$/;

  if (!codePattern.test(code)) {
    row.code = code.slice(0, -1);
  }
  if (row.code.match(/_/g) && row.code.match(/_/g).length > 3) {
    row.code = code.slice(0, -1);
  }
};
// 获取计算方式名称
const getCalculation = val => {
  let arr = methodOptions.value;
  let name = arr.filter(a => a.value == val);
  if (name.length !== 0) {
    return name[0].label;
  }
};
// 点击计算基数
const editCalc = row => {
  console.info('点击计算基数');
  comModel.value = true;
  textValue.value = row;
  oldValue.value = row.price;
};

const cancelData = () => {
  comArea.value.value = oldValue.value;
  textValue.value.price = oldValue.value;
  textValue.value.priceNum = oldValue.priceNum;
  comModel.value = false;
};
const sureData = async () => {
  //编辑计算基数弹框确认
  const value = comArea.value.value;
  const priceNum = comArea.value.priceNum;
  const newTableData = comArea.value.tableData;
  if (!value) {
    //不可以输入空
    message.warn(`输入不可为空`);
    return;
  }
  if (comArea.value.matchValue()) {
    return message.warn(`数据引用不合法，请重新编辑！`);
  }
  // console.log('calculationVerification(value, newTableData)',calculationVerification(value, newTableData))
  // if (calculationVerification(value, newTableData)) {
    textValue.value.price = value;
    textValue.value.priceNum = priceNum;
    updateData(textValue.value, 'price');
  // }
};
const calculationVerification = (value, newTableData) => {
  //计算校验
  let oldTableData = tableData.value;
  const zz = /[+-\/*\()]/;
  let valArr = value.split(zz);
  let treeArr = flattenTree(tableData.value);
  let codeArr = [];
  let isNoCheck = false;
  for (let i in newTableData) {
    codeArr.push(newTableData[i].code);
  }
  for (let n in treeArr) {
    codeArr.push(treeArr[n].code);
  }
  for (let s in valArr) {
    if (isNaN(parseInt(valArr[s])) && !codeArr.includes(valArr[s])) {
      isNoCheck = true;
      break;
    }
  }
  if (isNoCheck) {
    message.error('费用代码输入有误！');
  }
  return !isNoCheck;
};
const flattenTree = (tree, result = []) => {
  // 遍历当前层级的所有节点
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    // 将当前节点添加到结果数组中
    result.push(node);
    // 如果节点有子节点，则递归调用flattenTree
    if (node.children && node.children.length > 0) {
      flattenTree(node.children, result);
    }
  }
  // 返回结果数组
  return result;
};

// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log(menu)
  // 新增行
  if (menu.code === 'insertRow') {
    addQuantityData(row, 1);
    // 新增下级
  } else if (menu.code === 'insertchildRow') {
    addQuantityData(row, 2);
  } else if (menu.code === 'saveTemplate') {
    //保存模板
    executeOperation('saveTemplate');
  } else if (menu.code === 'pricingDocumentQuery') {
    //计价文件查询
    currentRecordValue.value=row
    openPricingDocumentQuery(true);
  } else if (menu.code === 'loadingTemplates') {
    //载入模版
    executeOperation('loadingTemplates');
  } else {
    // 删除行
    delQuantityData(row);
  }
};
const executeOperation = type => {
  let apiData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let url =
    type === 'saveTemplate'
      ? 'exportOtherProjectCost'
      : 'importOtherProjectCost';
  csProject[url](apiData)
    .then(res => {
      if (res.status !== 200) {
        return message.error(res.message);
      }
      message.success(
        type === 'saveTemplate' ? '保存模板成功！' : '载入模版成功！'
      );
      if (type !== 'saveTemplate') {
        emits('updateData');
        getHumanMachineData(true);
      }
    })
    .catch(err => {
      console.log(err, 'err');
    });
};

// 增加行
const addQuantityData = (row, type) => {
  const selectRecord = row || humanTable.value?.getCurrentRecord();
  if (!selectRecord) {
    return message.error('未选择行！');
  }
  let lineNumber = 0;
  tableData.value &&
    tableData.value.map((item, index) => {
      if (item.sequenceNbr == row.sequenceNbr) {
        if (type === 1) {
          lineNumber = index + 1;
        } else {
          // 新增下级增加到下级最后
          lineNumber = item.children.length + index + 1;
        }
      }
    });
  let apiData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
    otherProjectCostSummary: {
      parentId: type === 1 ? row.parentId : row.sequenceNbr,
    },
    lineNumber,
  };
  console.info('新增参数', apiData);
  csProject.addOtherProjectCost(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    message.success('插入成功');
    emits('updateData');
    getHumanMachineData();
  });
};
// 增加子级行
// const addChildQuantityData = row => {
//   const selectRecord = row || humanTable.value?.getCurrentRecord();
//   if(!selectRecord){
//     return message.error('未选择行！')
//   }
//   let apiData = {
//     projectId: projectStore.currentTreeGroupInfo?.constructId,
//     otherProjectCostId:selectRecord.sequenceNbr
//   };
//   csProject.addOtherProjectCost(apiData).then(res => {
//     console.info(res)
//     if(res.status !== 200){
//       return message.error(res.message)
//     }
//     message.success('插入成功');
//     emits('updateData');
//     getHumanMachineData()
//   });
// };
// 删除子级行
const delQuantityData = row => {
  console.log(row || humanTable.value?.getCurrentRecord())
  const selectRecord = row || humanTable.value?.getCurrentRecord();
  if (!selectRecord) {
    return message.error('未选择行！');
  }
  // false没被引用可以删除
  // if(!row.adopted){
    delRow.value=row
    deleteVisible.value=true;
  // }else{
  //   return message.error('被引用行不可删除！');
  // }
}
const delQuantityDataQuery = () => {
  deleteVisible.value=false;
  let apiData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
    otherProjectCostId: delRow.value.sequenceNbr,
    parentId: delRow.value.parentId,
  };
  csProject.delOtherProjectCost(apiData).then(res => {
    console.info(res);
    if (res.status !== 200) {
      return message.error(res.message);
    }
    message.success('删除成功');
    emits('updateData');
    getHumanMachineData(true);
  });
};
// 修改单价\计算基数
const changeJsfs = (row, field) => {
  // if(row.calculationMethod!==1&&row.calculationMethod!==2){
  //   updateData(row)
  //   return;
  // }
  // if(!row.price){
  //   row.price=0
  // }
  // if(!row.quantity){
  //   row.quantity=0
  // }
  // if(!row.priceNum){
  //   row.priceNum=0
  // }
  // // 如果是单价*数量
  // if(row.calculationMethod===1){
  //   if(isNaN(parseInt(row.price))){
  //     row.price=0
  //   }
  //   row.amount=parseInt(row.price)*parseInt(row.quantity)
  //   // 如果是计算基数*费率
  // }else{
  //   console.info(row.priceNum)
  //   console.info(row.quantity)
  //   row.amount=parseInt(row.priceNum)*parseInt(row.quantity)
  // }
  // 如果是单价*数量并且单价没有值则默认给0
  if (row.calculationMethod === 1 && !row.price) {
    row.price = 0;
  }
  updateData(row, field);
};
function isValidExpression(expression) {
  // 匹配四则运算表达式的正则表达式
  const regex = /^[\d\+\-\*\/\(\)\.]+$/;
  
  // 检查表达式是否匹配正则表达式
  if (!regex.test(expression)) {
    return false;
  }
  
  try {
    // 使用 eval() 函数计算表达式的值
    eval(expression);
    return true;
  } catch (e) {
    // 如果表达式有语法错误，eval() 函数会抛出异常
    return false;
  }
}
const editClosedEvent = async e => {
  const { $table, row, column } = e;
  const field = column.field;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'code' && row.code === '') {
    codeNullVisible.value = true;
    currentInfo.value = row;
    return;
  }
  if (field === 'price') {
    // if (!isValidExpression(row[field])) {
    //   message.warn(`表达式有误，请重新编辑！`);
    //   $table.revertData(row, field);
    //   return;
    // }
    let { zeroCode, newTableData } = await getZeroCode();
    const regex = new RegExp(`/(${zeroCode.join('|')})`);
    const isValid = regex.test(row.price);
    console.log('建设其他费计算基数编辑返回结果', zeroCode, isValid);
    if (isValid) {
      $table.revertData(row, 'price');
      return message.error('数据引用不合法，请重新编辑');
    }
    if(row.calculationMethod!=2){
      if(!calculationVerification(row.price, newTableData)){
        $table.revertData(row, 'price');
        return
      }
    }
    // if(row.calculationMethod==2){
    //   row.price=eval(row.price)
    // }
  }
  if(field === 'quantity'){
    if(row.calculationMethod==1||row.calculationMethod==2){
      row.quantity=eval(row.quantity)
    }
  }
  if(field === 'amount'){
    if(row.calculationMethod==4){
      row.amount=eval(row.amount)
    }
  }
  updateData(row, field);
};
const getZeroCode = async () => {
  return new Promise((resolve, reject) => {
    let zeroCode = ['0'];
    csProject
      .getOtherProjectCostCode({
        projectId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {
        if (res.status === 200) {
          res.result?.map((item, index) => (item.sortNo = index + 1));
          res.result.map(item => {
            if (item.price === 0) {
              zeroCode.push(item.code);
            }
          });
          resolve({ zeroCode, newTableData: res.result });
        }
      });
  });
};
// 清空费用代号取消
const codeNullCancel = () => {
  const $table = humanTable.value;
  codeNullVisible.value = false;
  $table.revertData(currentInfo.value, 'code');
};
// 清空费用代号确认
const codeNullQuery = () => {
  codeNullVisible.value = false;
  updateData(currentInfo.value, 'code');
};
// 修改列表行
const updateData = (row, field, list) => {
  if (row.calculationMethod === 1 && !row.price) {
    row.price = 0;
  }
  let otherProjectCost = {};
  if (row) {
    let fields= null
    if(row.fields instanceof Map){
      fields=Object.fromEntries(row.fields)
    }else{
      fields=row.fields
    }
    otherProjectCost = JSON.parse(JSON.stringify(row));
    otherProjectCost['fields']=JSON.parse(JSON.stringify(fields))
  } else {
    let fields= null
    if(textValue.value.fields instanceof Map){
      fields=Object.fromEntries(textValue.value.fields)
    }else{
      fields=textValue.value.fields
    }
    otherProjectCost = JSON.parse(JSON.stringify(textValue.value));
    otherProjectCost['fields']=JSON.parse(JSON.stringify(fields))
  }
  let apiData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
    otherProjectCostList: [],
    otherProjectCost,
  };
  if (list) {
    apiData.otherProjectCostList = JSON.parse(JSON.stringify(list));
  }
  if(apiData.otherProjectCost.calculationMethod!==4){
    delete apiData.otherProjectCost.amount
  }
  console.info('修改概算建设其他费参数', apiData);
  csProject.updateOtherProjectCost(apiData).then(res => {
    console.info(res);
    if (res.status !== 500) {
      message.success('修改成功');
      getHumanMachineData();
      comModel.value = false;
    } else if (res.status === 500) {
      message.error(res.message);
      const $table = humanTable.value;
      $table.revertData(row, field);
    }
  });
};
// 打开其他费用计算器
const showPriceHandle = () => {
  // const selectRecord = humanTable.value?.getCurrentRecord();
  // isPriceData = selectRecord;
  // if(!selectRecord){
  //   return message.error('未选择行！')
  // }
  const $table = humanTable.value;
  console.log($table.getCurrentRecord(), '$table.getCurrentRecord()');
  if ($table.getCurrentRecord()) {
    jsqsequenceNbr.value = $table.getCurrentRecord().sequenceNbr;
    jsqCategory.value = $table.getCurrentRecord().category;
  }
  isPriceModel.value = true;
};
// 关闭其他费用计算器/计价文件查询
const closePriceModel = val => {
  if (val === 'isPriceModel') {
    isPriceModel.value = false;
  } else {
    pricingDocumentQueryModal.value = false;
  }
};
// 其他费用计算器点击确认
const queryOther = isClose => {
  ortherFeeRef.value.formRef
    .validate()
    .then(res => {
      loading.value = true;
      const calculatorBasis = ortherFeeRef.value.selTreeAllName;
      const calculationProcess = ortherFeeRef.value.calculationProcess;
      const amount = ortherFeeRef.value.feeNum;
      const fields = ortherFeeRef.value.fields;
      let apiData = {
        calculatorBasis,
        projectId: projectStore.currentTreeGroupInfo?.constructId,
        amount,
        fields: toRaw(fields),
        calculationProcess,
      };
      console.info('费用计算器参数')
      console.info(apiData)
      csProject.getOtherProjectCostCalculatorData(apiData).then(res => {
        // 如果为500则列表没有此费用类别需要调用树结构保存数据
        if(res.status==500){
          csProject.updateOtherProjectCostCalculatorTree(apiData).then(res => {})
          ortherFeeRef.value.getOtherProjectCostCalculatorParams(true)
        }
        if (res.status !== 200) {
          loading.value = false;
          return message.error(res.message);
        }
        message.success(isClose ? '应用成功' : '计算成功');
        if (!isClose) {
          isPriceModel.value = false;
        }
        tableData.value = res.result;
        getHumanMachineData(false,ortherFeeRef.value.selTreeName);
      });
    })
    .catch(err => {
      console.log(err, 'err');
    });
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};

insetBus(
  bus,
  projectStore.componentId,
  'humanMachineSummary',
  async data => {}
);
watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.levelType],
  () => {
    if (
      projectStore.currentTreeInfo.type === 1 &&
      projectStore.tabSelectName === '建设其他费'
    ) {
      //侧边栏数据变化重新更新
      getHumanMachineData(true);
      getTreeList();
      // 概算未做
      // getLoadStatus();
    }
  }
);
onActivated(() => {
  insetBus(
    bus,
    projectStore.componentId,
    'otherConstructionCosts',
    async data => {
      if (data.name === 'insert') addQuantityData(null);
      if (data.name === 'orther-cost-calculator') showPriceHandle(null);
      if (data.name === 'delete') delQuantityData(null);
      // if (data.name === 'pricing-document-query') // 计价文件查询
      if (data.name === 'save-template') executeOperation('saveTemplate'); // 保存模板
      if (data.name === 'load-template') executeOperation('loadingTemplates'); // 载入模板
      if (data.name === 'pricing-document-query')
        openPricingDocumentQuery(); // 载入模板
    }
  );
  if (
    projectStore.currentTreeInfo.type === 1 &&
    projectStore.tabSelectName === '建设其他费'
  ) {
    getHumanMachineData();
    getTreeList();
  }
});
onMounted(() => {
  
});
const openPricingDocumentQuery = () => {
  pricingDocumentQueryModal.value = true;
};
const selectChangeEvent = ({ $table, row }) => {
  const records = $table.getCheckboxRecords();
  let arr = tableData.value;
  let selSeqArr = [];
  for (let item of records) {
    selSeqArr.push(item.sequenceNbr);
  }
  for (let item of arr) {
    if (selSeqArr.indexOf(item.sequenceNbr) != -1) {
      // item.scopeFlag=true;
      item.ifCalculate = true;
    } else {
      // item.scopeFlag=false;
      item.ifCalculate = false;
    }
  }
  updateData(row, 'ifCalculate', arr);
};



const getHumanMachineData = (init=false,selTreeName='') => {
  loading.value = true;
  let formData = {
    projectId: projectStore.currentTreeGroupInfo?.constructId,
  };
  csProject
    .getOtherProjectCostList(formData)
    .then(res => {
      console.info('获取建设其他费数据');
      console.info(res.result);
      if (res.status !== 200) {
        loading.value = false;
        return message.error(res.message);
      }
      handleTable(res.result)
      if(init){
        resetCurrentRow();
      }
      if(selTreeName!=''){
        setTreeCurrentRow(selTreeName)
      }
    })
    .catch(err => {
      loading.value = false;
    });
};
const handleTable = data => {
  let list = data ? data : [];
  tableData.value = list.map((i,k)=>{
    i.index = k
    return i
  })
  // 更新数据
  setMoveRowList(null,tableData.value,true)
  setTimeout(() => {
    for (const item of data) {
      if (item.ifCalculate) {
        humanTable.value.setCheckboxRow(item, true);
      } else {
        humanTable.value.setCheckboxRow(item, false);
      }
    }
    
  }, 5);
  nextTick(() => {
    humanTable.value?.setAllTreeExpand(true)
    if(currentInfo.value){
      humanTable.value.setCurrentRow(currentInfo.value)
      currentRecordValue.value = {
        name: currentInfo.value.name,
      };
      setMoveRowList(currentInfo.value,tableData.value)
    }else{
      isCurrent.value
          ? humanTable.value.setCurrentRow(data[isCurrent.value])
          : humanTable.value.setCurrentRow(data[0]);
          setMoveRowList(isCurrent.value?data[isCurrent.value]:data[0],data,false)
    }
  });
  loading.value = false;
  
};


const currentChange = ({row, $rowIndex}) => {
  currentRecordValue.value = {
    name: row.name,
  };
  currentInfo.value=row
  setMoveRowList(row,tableData.value)
}

const resetCurrentRow = () =>{
  nextTick(()=>{
    humanTable.value?.setCurrentRow(tableData.value[0]);
    setMoveRowList(tableData.value[0],tableData.value)
  })
}
const setTreeCurrentRow = (selTreeName) =>{
  let row=tableData.value.find(a=>a.category==selTreeName)
  nextTick(()=>{
    humanTable.value?.setCurrentRow(row);
    setMoveRowList(row,tableData.value)
  })
  setTimeout(()=>{
    humanTable.value?.scrollToRow(row);
  },200)
}


/**
 * 
 * @param {*} row 
 * @param {*} list 
 * @param {*} resetList  只更新列表
 */
const setMoveRowList = (row,list,resetList=false) => {
  if(resetList){
    projectStore.moveRow.tableData = list;
    return;
  }
  projectStore.moveRow = {
    tableData:toRaw(list),
    isTree:false,
    useRowList:[
      {
        ...row,
      }
    ],
  }
}
const getTableData=()=>{
  getHumanMachineData()
}
defineExpose({
  getTableData:getTableData
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}

.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
}

.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}

.table-content {
  height: calc(100%);
}

.table-content {
  // height: calc(65%);
  height: 100%;
  //user-select: none;

  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }

  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }

  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }

  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }

  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }

  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }

  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }

  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
}

.table-content ::v-deep(.vxe-table--render-default .vxe-tree--node-btn) {
  color: #87b2f2 !important;
}

.table-content :deep(.vxe-table) {
  .vxe-tree--line {
    /* 修改连接线的颜色 */
    border-left: 1px solid #87b2f2;
    border-bottom: 1px solid #87b2f2;
  }

  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }

  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}
.btns-view {
  flex: 1.3;
}
.btns-action {
  flex: 4;
  display: flex;
  justify-content: center;
}
.btns {
  margin: 10px;
  display: flex;
}
.btns2 {
  margin: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
