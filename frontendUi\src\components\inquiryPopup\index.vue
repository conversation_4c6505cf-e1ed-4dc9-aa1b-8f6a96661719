<!--
 * @Descripttion: 智能询价弹窗
 * @Author: sunchen
 * @Date: 2023-12-25 10:34:45
 * @LastEditors: wangru
 * @LastEditTime: 2025-07-10 09:58:32
-->
<template>
  <common-modal
    className="dialog-comm resizeClass inquiryPopup-dialog"
    width="1000"
    @close="cancel()"
    :mask="false"
    :lockView="false"
    show-zoom
    resize
    v-model:modelValue="dialogVisible"
    title="价格选择"
  >
    <!-- :lockView="false" -->

    <div
      class="inquiry-dialog-content"
      v-if="initData"
    >
      <div class="inquiry-dialog-content-tabs">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane
            key="informationPriceList"
            tab="信息价"
          ></a-tab-pane>
          <a-tab-pane
            key="marketPriceList"
            tab="市场价"
          ></a-tab-pane>
          <a-tab-pane
            key="recommendPriceList"
            tab="推荐价"
          ></a-tab-pane>
        </a-tabs>
        <div class="inquiry-dialog-content-table">
          <a-row
            type="flex"
            style="margin-bottom: 8px;"
          >
            <a-col
              :span="6"
              :order="1"
            >
              <span class="label">{{activeKey ==='recommendPriceList'?'推荐数据类型：':'地区：'}}</span>
              <a-cascader
                v-if="activeKey ==='informationPriceList' "
                size="small"
                :allowClear="false"
                v-model:value="regionList[activeKey].selectValue.cityVal"
                placeholder="请选择地区"
                :options="regionList[activeKey].cityList"
                style="max-width: 181px"
                @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
              />
              <a-select
                v-else
                ref="select"
                size="small"
                v-model:value="regionList[activeKey].selectValue.cityVal"
                style="width: 120px"
                @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
              >
                <a-select-option
                  v-for="item in regionList[activeKey].cityList"
                  :value="item"
                >{{ item }}</a-select-option>
              </a-select>
            </a-col>
            <a-col
              :span="6"
              :order="2"
            >
              <span
                class="label"
                style="margin-left:20px"
              >期数：</span>
              <a-select
                size="small"
                ref="select"
                v-model:value="regionList[activeKey].selectValue.dateVal"
                style="width: 120px"
                @change="handleChange('month')"
              >
                <a-select-option
                  :value="data"
                  v-for="data in regionList[activeKey].showDateList"
                >{{ data }}</a-select-option>
              </a-select>
            </a-col>
            <a-col
              :span="6"
              :order="3"
            >
              <a-input-search
                size="small"
                v-model:value="searchName"
                :maxlength="50"
                :placeholder="'请输入名称查询'"
                class="searchTab-input"
                :class="!searchName?'noAllow':''"
                @search="onSearch"
              />
            </a-col>
            <a-col
              :span="6"
              :order="4"
            ></a-col>
          </a-row>
          <vxe-table
            v-if="initData[activeKey] && initData[activeKey].length"
            align="center"
            show-footer
            height="300px"
            :row-class-name="rowClassName"
            @cell-dblclick="handleCellDblclick"
            :scroll-x="{ enabled: true, gt: 20 }"
            :scroll-y="{ enabled: true, gt: 100 }"
            :data="initData[activeKey]"
          >
            <vxe-column
              type="seq"
              width="70"
              title="序号"
            ></vxe-column>
            <vxe-column
              field="materialName"
              title="名称"
            ></vxe-column>
            <vxe-column
              field="specification"
              width="100"
              title="规格型号"
            ></vxe-column>
            <vxe-column
              field="unit"
              title="单位"
            ></vxe-column>
            <vxe-column
              field="marketPrice"
              :title="
                activeKey === 'informationPriceList'
                  ? '含税市场价'
                  : activeKey === 'marketPriceList'
                  ? '工程价'
                  : '推荐价'
              "
            ></vxe-column>
            <vxe-column
              field="notIncludingTaxMarketPrice"
              title="不含税市场价"
              v-if="activeKey === 'informationPriceList'"
            ></vxe-column>
            <vxe-column
              field="area"
              title="地区/产地"
            > </vxe-column>
            <vxe-column
              field="priceDate"
              title="期数/报价时间"
            ></vxe-column>
            <vxe-column
              field="taxRemoval"
              title="税率"
            ></vxe-column>
          </vxe-table>

          <div
            class="nodata"
            v-else
          >
            <img
              src="https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/jijiasoft/zanwushuju.png"
              class="bg-img"
              alt=""
            />
            <span>未查询到对应数据价格</span>
          </div>
        </div>
      </div>
      <div class="inquiry-dialog-footer">
        <div class="inquiry-footer">
          <icon-font
            class="icon"
            type="icon-tishi"
            style="margin-right: 5px"
          />
          您可进入
          <a
            href="https://www.yunsuanfang.com"
            class="small-tips"
            target="_blank"
          >https://www.yunsuanfang.com</a>
          查看最新的信息价、市场价数据或您可联系客服
          <span class="small-tips">400-005-8008</span> 进行人工查价
        </div>

        <div class="inquiry-footer">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />
          <span>双击数据行即可跳转</span>
        </div>
      </div>
    </div>
  </common-modal>
  <unitConvert
    v-if="unitConvertVisible"
    @closeDialog="closeUnitConvert"
    :priceInfo="postData"
    :isDiffDeType="true"
  />
</template>
<script setup>
import {
  ref,
  reactive,
  watch,
  shallowRef,
  toRaw,
  defineExpose,
  defineAsyncComponent,
  onMounted,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import loadPrice from '@/api/loadPrice.js';
import * as aes from '@/utils/aes/public.js';
import CommonModal from '@/components/global/commonModal/index.vue';
import feePro from '@/api/feePro.js';

const unitConvert = defineAsyncComponent(() => import('@/components/unitConvert.vue'));
const store = projectDetailStore();
const props = defineProps({
  info: {
    type: Object,
    default: null,
  },
});
const emits = defineEmits(['success']);
const route = useRoute();
const dialogVisible = ref(false);
const initData = shallowRef([]);
let activeKey = ref('informationPriceList');
let postData = ref();
let unitConvertVisible = ref(false);
let loading = ref(false);

const getTreeList = () => {
  initData.value = [];
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: store.currentTreeInfo?.parentId,
    unitId: store.currentTreeInfo.id,
    standardId: props.info.standardId,
    materialName: searchName.value,
    areaName:
      activeKey.value === 'informationPriceList'
        ? regionList.value[activeKey.value].selectValue.cityVal[
            regionList.value[activeKey.value].selectValue.cityVal.length - 1
          ]
        : regionList.value[activeKey.value].selectValue.cityVal,
    yearMonths: regionList.value[activeKey.value].selectValue.dateVal,
    specification: props.info.specification,
  };
  console.log('csProject.smartLoadPrice=》参数', postData);
  csProject.smartLoadPrice(postData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('getTreeList', res.result);
      initData.value = res.result[0];
    }
  });
};
let regionList = ref({
  informationPriceList: {
    title: '信息价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: [],
      dateVal: null,
    },
  },
  marketPriceList: {
    title: '市场价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
  recommendPriceList: {
    title: '推荐价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
});
let cityAllList = reactive([]);
const searchName = ref(null);
const getCityList = async () => {
  await loadPrice.getDimRegion({}).then(res => {
    if (res.status === 200) {
      let list = JSON.parse(aes.decrypt(res.result));
      console.log('getDimRegion', JSON.parse(aes.decrypt(res.result)));
      list.map(a => {
        a.label = a.name;
        a.value = a.name;
        a?.children?.map(b => {
          b.label = b.name;
          b.value = b.name;
        });
      });
      //王浩和产品确认过-过滤河北省（河北省无日期数据）批量载价也是
      cityAllList = list.filter(a => a.code !== '130000');
      console.log('cityAllList', cityAllList);
      // console.log('list', list);
    }
  });
};
const getMonthAndCity = async (apiData = {}) => {
  apiData.constructId = route.query?.constructSequenceNbr;
  apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
  apiData.unitId = store.currentTreeInfo?.id; //单位ID
  await loadPrice.queryLoadPriceAreaDate(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('queryLoadPriceAreaDate', res.result);
      for (let citys in res.result) {
        if (citys === '信息价') {
          regionList.value['informationPriceList'].cityList = cityAllList;
          regionList.value['informationPriceList'].selectValue.cityVal = [
            regionList.value['informationPriceList'].cityList[0].value,
            regionList.value['informationPriceList'].cityList[0].children[0].value,
          ];
        }
        setSelectList(citys, res.result);
      }
    }
  });
};
const setSelectList = (item, total) => {
  for (let i in regionList.value) {
    let temp = regionList.value[i];
    if (temp.title === item) {
      if (temp.title !== '信息价') {
        temp.cityList = Object.keys(total[item]);
      }
      temp.dateList = total[item];
      if (temp.title !== '信息价') {
        temp.selectValue.cityVal = temp.cityList[0];
        temp.showDateList = temp.dateList[temp.selectValue.cityVal];
      } else {
        temp.showDateList =
          temp.dateList[temp.selectValue.cityVal[temp.selectValue.cityVal.length - 1]] || [];
      }
      temp.selectValue.dateVal = (temp.showDateList && temp.showDateList[0]) || null;
    }
  }
  console.log('regionList.value', regionList.value);
};
const handleChange = async (type, target) => {
  if (type === 'city') {
    if (activeKey.value === 'informationPriceList') {
      regionList.value['informationPriceList'].showDateList =
        regionList.value['informationPriceList'].dateList[target[target.length - 1]];
      regionList.value['informationPriceList'].selectValue.dateVal =
        regionList.value['informationPriceList'].showDateList &&
        regionList.value['informationPriceList'].showDateList[0];
    } else {
      regionList.value[activeKey.value].showDateList =
        regionList.value[activeKey.value].dateList[target];
      regionList.value[activeKey.value].selectValue.dateVal =
        regionList.value[activeKey.value].showDateList[0];
    }
  }
  getTreeList();
  console.log('regionList.value', regionList.value);
};
const onSearch = async () => {
  if (!searchName.value) return;
  console.log('searchName', searchName.value);
  getTreeList();
};
// 回显能力添加
const getLoadPriceCache = async () => {
  try {
    const res = await loadPrice.getLoadPriceCache({
      constructId: store.currentTreeGroupInfo?.constructId,
    });
    if (res.status === 200 && res.result) {
      if ('type1' in res.result && res.result.type1.type == 1) {
        // regionList.value[0].selectValue.cityVal = ['',res.result.type1.areaName];
        regionList.value['informationPriceList'].selectValue.dateVal = res.result.type1.yearMonths;
      }
      if ('type2' in res.result && res.result.type2.type == 2) {
        regionList.value['marketPriceList'].selectValue.cityVal = res.result.type2.areaName;
        regionList.value['marketPriceList'].selectValue.dateVal = res.result.type2.yearMonths;
      }
      if ('type3' in res.result && res.result.type3.type == 3) {
        regionList.value['recommendPriceList'].selectValue.cityVal = res.result.type3.areaName;
        regionList.value['recommendPriceList'].selectValue.dateVal = res.result.type3.yearMonths;
      }
    }
    for (let index = 0; index < cityAllList.length; index++) {
      if (cityAllList[index]?.children.find(i => i.name === res.result.type1.areaName)) {
        regionList.value['informationPriceList'].selectValue.cityVal = [
          cityAllList[index].name,
          res.result.type1.areaName,
        ];
        break;
      }
    }
    console.log('loadPrice.getLoadPriceCache', res.result);
  } catch (e) {
    console.error('loadPrice.getLoadPriceCache错误', e);
  } finally {
    getTreeList();
  }
};
const closeUnitConvert = val => {
  unitConvertVisible.value = false;
  if (val) {
    postData.value = {
      ...val,
      loadPrice: {
        ...val.loadPrice,
        marketPrice: val.marketPriceAfter,
      },
    };
    beforeClick();
  }
};

const handleCellDblclick = ({ row }) => {
  console.log('水电费1', props.info);

  let { sequenceNbr, marketPrice, sourcePrice, unit, notIncludingTaxMarketPrice } = row;
  postData.value = {
    constructId: route.query?.constructSequenceNbr,
    singleId: store.currentTreeInfo?.parentId,
    unitId: store.currentTreeInfo.id,
    sequenceNbr: props.info.sequenceNbr,
    loadPrice: { ...row },
    beforeUnit: unit,
    nowUnit: props.info.unit,
    marketPrice:
      activeKey.value === 'informationPriceList' &&
      Number(store.deStandardReleaseYear) === 22 &&
      Number(store?.taxMade) === 1
        ? row.notIncludingTaxMarketPrice
        : row.marketPrice,
    marketPriceAfter: '',
    sourcePrice,
    loadingPriceFlag: 1,
    thisTimeLoadingPriceFlag: 1,
  };

  if (row.unit !== props.info.unit) {
    unitConvertVisible.value = true;
    return;
  }
  beforeClick();
};

const rowClassName = ({ row }) => {
  if (row.kind === '01' || row.kind === '02') {
    return 'row-sub';
  }
  return null;
};

const beforeClick = () => {
  loading.value = true;
  csProject
    .smartLoadPriceUse(toRaw(postData.value))
    .then(res => {
      console.log('🚀 ~ file: index.vue:183 ~ csProject.smartLoadPriceUse ~ res:', res);
      if (res.result) {
        cancel(true);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const cancel = (isOk = false) => {
  dialogVisible.value = false;
  emits('closeDialog', isOk);
};

const open = async () => {
  searchName.value = props.info.materialName;
  await getCityList();
  await getMonthAndCity();
  await getLoadPriceCache();
  dialogVisible.value = true;
};
watch(
  () => props.info,
  () => {
    searchName.value = props.info.materialName;
    getTreeList();
  }
);
onMounted(() => {
  open();
});
</script>

<style lang="scss">
.inquiryPopup-dialog {
  .row-sub {
    background-color: red;
  }
  .vxe-modal--content {
    padding-bottom: 15px !important;
    .ant-tabs-tab {
      padding: 0 20px 5px 20px;
    }
  }
  .inquiry-dialog-content {
    display: flex;
    flex-direction: column;
    min-height: 400px;
    height: 100%;
    .inquiry-dialog-content-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
  .inquiry-dialog-content-table {
    flex: 1;
    position: relative;
    .nodata {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      .bg-img {
        display: block;
        width: 180px;
        margin: 0 auto;
      }
      span {
        font-size: 14px;
        color: #606060;
        text-align: center;
      }
    }
  }
  .inquiry-dialog-footer {
    margin-top: 18px;
  }
  .inquiry-footer {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #2a2a2a;
    &:first-child {
      margin-top: 4px;
    }
    .small-tips {
      color: rgba(40, 124, 250, 1);
    }
  }
}
</style>
