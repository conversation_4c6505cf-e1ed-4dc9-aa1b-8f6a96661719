<template>
  <MyIndex ref="myIndex">
    <template #default>
      <operate @executeCommand="executeCommand" @showLoadMould="showLoadMould"/>
    </template>
  </MyIndex>
  <common-modal className="dialog-comm" :loading="loading" v-if="modelValue" v-model:modelValue="modelValue" title="更新招标书" :mask="true" :lockView="false"
                :lockScroll="false"  width="80%"  @close="cancel">
    <div slot="header">
      <Prompt :matchState="matchState"/>
    </div>
    <div slot="default">
      <keep-alive>
        <component :is="components.get(matchState)" :matchState="matchState" @buttonHandle="buttonHandle"/>
      </keep-alive>
    </div>
    <div slot="footer" class="dialog-footer">
        <div>
          <a-button size="small" type="primary" @click="batchDelByTypeOfColl">历史生成报告</a-button>
        </div>
        <div class="dialog-button">
          <a-button size="small" @click="stepHandle('previousStep')" :disabled= 'buttonState.ppsj'>上一步</a-button>
          <a-button size="small" @click="stepHandle('nextStep')" :disabled= 'buttonState.ppjg'>下一步</a-button>
          <a-button size="small" @click="stepHandle('complete')" :disabled= 'buttonState.ppwc'>完成</a-button>
          <a-button size="small" @click="cancel">取消</a-button>
        </div>
    </div>
  </common-modal>
</template>

<script setup>
import {defineAsyncComponent, markRaw, ref,toRefs} from 'vue';
import MyIndex from '@/views/projectDetail/customize/index.vue'
import operate from '@/views/projectDetail/electronicLabel/operate.vue';
import CommonModal from "@/components/global/commonModal/index.vue";
import Prompt from "@/views/projectDetail/electronicLabel/components/Prompt.vue";
import { projectDetailStore } from '@/store/projectDetail';
import { electronicLabel } from '@/views/projectDetail/electronicLabel/store/electronicLabelStore';
import {message} from "ant-design-vue";
import { useElectronicLabel } from '@/views/projectDetail/electronicLabel/hooks/useElectronicLabel.js';
const electronicLabelStore = electronicLabel();
import api from "@/api/electronicLabel.js";
const projectStore = projectDetailStore();
// ppsj:匹配数据；  ppjg：匹配结构； ppwc：匹配完成
const components = new Map([
  ['ppjg',defineAsyncComponent(() => import('./components/StructureMatching.vue'))],
  ['ppsj',defineAsyncComponent(() => import('./components/DataMatching.vue'))],
  ['ppwc',defineAsyncComponent(() => import('./components/Complete.vue'))] ]
);
const modelValue = ref(false);
let { buttonState,matchState,changeButtonHandle } = useElectronicLabel();

const stepHandle = async (type)=>{
  console.log(matchState.value);

  console.log(buttonState.value);
  switch (type) {
    case 'previousStep':
      matchState.value = matchState.value === 'ppwc' ? 'ppsj' : 'ppjg';
      break;
    case 'nextStep':
      if (matchState.value === 'ppjg') {
        let res = await nextStepHandle();
        if(res){
          matchState.value = matchState.value === 'ppsj' ? 'ppwc' : 'ppsj';
        }
      }else {
        matchState.value = matchState.value === 'ppsj' ? 'ppwc' : 'ppsj';
      }
      break;
    case 'complete':
      complete();
      break;
  }
  changeButtonHandle();
}
const nextStepHandle = async ()=>{
  if(matchState.value === 'ppjg'){
    const apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    try {
      loading.value = true;
      console.log(`api-nextStep参数`,apiData);
      const res = await api.nextStep(apiData);
      console.log(`api-nextStep返回值`,res);
      if(res.status === 200){
        return true;
      }else{
        message.error(res.message);
        return false;
      }
    }catch (e) {
      console.error('api.uploadUpdate=>catch',e);
      message.error(e.message);
    }finally {
      loading.value = false;
    }
  }else if(matchState.value === 'ppsj'){

  }
}
const myIndex = ref();
const complete = async ()=>{
  const apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  try {
    loading.value = true;
    console.log(`api-complete参数`,apiData);
    const res = await api.complete(apiData);
    console.log(`api-complete返回值`,res);
    if(res.status === 200){
      modelValue.value = false;
    }else{
      message.error(res.message);
    }
    }catch (e) {
      console.error('api.complete=>catch',e);
      message.error(e.message);
    }finally {
      restoreDefault();
      myIndex.value.getTreeList(true);
    }
}

// 更改 弹窗下方按钮的置灰显示规则
const buttonHandle = ({page,flag})=>{
  changeButtonHandle({page,flag})
}

const showLoadMould = async (e) => {
  if(!await myIndex.value.isItVerified()){
    modelValue.value = true;
  }
};

const loading = ref(false);
const cancel = async () => {
  const apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  try {
    loading.value = true;
    console.log(`api-close参数`,apiData);
    const res = await api.close(apiData);
    console.log(`api-close返回值`,res);
    if(res.status === 200){
    }else{
      message.error(res.message);
    }
  }catch (e) {
    // dataSource.value.tb = [];
    // dataSource.value.zb = [];
    console.error('api.uploadUpdate=>catch',e);
    message.error(e.message);
  }finally {
    restoreDefault();
  }
}
// 还原默认
const restoreDefault= ()=>{
  loading.value = false;
  modelValue.value = false;
  buttonState.value.ppjg = true;
  buttonState.value.ppsj = true;
  buttonState.value.ppwc = true;
  matchState.value = 'ppjg';
  electronicLabelStore.SET_CLEAR();
}



</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  .dialog-button button {
    margin-right: 10px;
  }
  .dialog-button button:last-child {
    margin-right: 0;
  }
}
::v-deep(footer)  {
  bottom: -51px !important;
}
::v-deep(.common) {
  //height: 60vh;
  display: flex;
  flex-direction: column;
}
</style>
