<!--
 * @Author: z<PERSON><PERSON>oliang
 * @Date: 2023-05-17 14:19:32
 * @LastEditors: wangru
 * @LastEditTime: 2025-07-28 15:50:56
-->

<template>
  <div class="content">
    <ul>
      <li
        v-for="item in newProData.menu"
        :key="item.id"
      >
        <div
          class="box"
          @click="open(item)"
        >
          <p :class="item.isActived ? 'pClicked' : 'pUnclicked'">
            <img
              :src="item.isActived ? item.clickUrl : item.unClickUrl"
              :alt="item.title"
            />{{ item.title }}
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { onMounted, reactive, defineEmits } from 'vue';
import csProject from '@/api/csProject';
import { getUrl } from '@/utils/index';
const emit = defineEmits(['getProType']);
const newProData = reactive({
  menu: [
    {
      clickUrl: getUrl('proImg/zhaobiao_1.png'),
      unClickUrl: getUrl('proImg/zhaobiao_2.png'),
      title: '招标项目',
      id: 'zhaobiao',
      isActived: true,
    },
    {
      clickUrl: getUrl('proImg/toubiao_1.png'),
      unClickUrl: getUrl('proImg/toubiao_2.png'),
      title: '投标项目',
      id: 'toubiao',
      isActived: false,
    },
    {
      clickUrl: getUrl('proImg/danwei_1.png'),
      unClickUrl: getUrl('proImg/danwei_2.png'),
      title: '单位工程',
      id: 'danwei',
      isActived: false,
    },
    {
      clickUrl: getUrl('proImg/gongliaoji_1.png'),
      unClickUrl: getUrl('proImg/gongliaoji_2.png'),
      title: '定额项目',
      id: 'gongliaoji',
      isActived: false,
    },
  ],
  newProType: 'zhaobiao',
});
const open = item => {
  newProData.newProType = item.id;
  newProData.menu.map(item => {
    if (item.id === newProData.newProType) {
      item.isActived = true;
    } else {
      item.isActived = false;
    }
  });
  emit('getProType', newProData.newProType);
  // console.log('新建项目预算类型传给右边input列表类型-----------',newProData.newProType )

  //掉接口获取数据并打开创建弹框
  // csProject.getRecentlyProgectList().then(function (response) {
  // 	console.log(response, '555555555555555555')
  // })
};
</script>
<style lang="scss" scoped>
.content {
  text-align: right;
  font-size: 16px;
  height: 100%;
  ul {
    height: 100%;
    text-align: center;
    list-style-type: none;
    padding: 0;
    flex-direction: column;
    display: flex;
    margin: 0;
    justify-content: space-evenly;
    .box {
      img {
        vertical-align: sub;
        margin-right: 5px;
      }
      p {
        width: 80%;
        margin: 0;
        font-size: 16px;
        height: 50px;
        line-height: 50px;
        border-radius: 0px 30px 30px 0px;
        padding-right: 20px;
        cursor: pointer;
      }
      .pClicked {
        color: #287cfa;
        background: #ffffff;
      }
      .pUnclicked {
        color: #ffffff;
        background: transparent;
      }
    }
  }
}
</style>
