/*
 * @Descripttion:
 * @Author:
 * @Date: 2025-01-15 15:14:21
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-14 18:03:02
 */

export const useVxeTableKeyDown = (callback = () => {}) => {
  const isInput = nodeName => {
    return nodeName.toUpperCase() === 'INPUT';
  };
  const isTextarea = nodeName => {
    return nodeName.toUpperCase() === 'TEXTAREA';
  };
  /**
   * 键盘事件处理函数
   *
   * @param {Object} param - 包含事件对象和表格实例的对象
   * @param {Object} param.$event - 浏览器事件对象
   * @param {Object} param.$table - 表格实例对象
   */
  const vxeTableKeydown = ({ $event, $table }) => {
    const nodeName = $event.target.nodeName;
    const eventKey = $event.key.toUpperCase();
    if (isInput(nodeName)) {
      if (eventKey === 'ENTER') {
        $event.target.blur(); // 避免一些input blur修改值vextable clearEdit监听不到值变化无法生效
        $table.clearEdit();
      }
    }
    if (isTextarea(nodeName)) {
      if (eventKey === 'ENTER' && $event.ctrlKey === true) {
        $event.target.value += '\n';
      } else if (eventKey === 'ENTER') {
        $table.clearEdit();
      }
    }
    if (callback) {
      callback(eventKey);
    }
  };
  return { vxeTableKeydown };
};
