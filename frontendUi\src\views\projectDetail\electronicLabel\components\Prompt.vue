<script setup>
import {ref} from 'vue';
const props =  defineProps(['matchState']);
const steps = ref({
  "ppjg":{
    id:1,
    title: '第一步：匹配标书结构',
    content: ['1、请选择最新的招标清单文件，软件会根据工程节点名称和层级自动匹配；','2、对匹配有问题的工程节点，可以通过鼠标拖拉工程节点或匹配按钮进行调整；']
  },
  'ppsj':{
    id:2,
    title: '第二步：匹配标书数据',
    content: ['1、软件已将最新招标工程和当前投标工程的数据做了详细匹配', '2、对匹配有问题的数据项，可以对每项数据进行调整', '3、点击完成后，将最新匹配数据更新到当前投标工程中']
  },
  'ppwc':{
    id:3,
    title: '第三步：完成',
    content: ['1、软件已将最新招标工程和当前投标工程的数据做了详细匹配', '2、对匹配有问题的数据项，可以对每项数据进行调整', '3、点击完成后，将最新匹配数据更新到当前投标工程中']
  }
});
</script>
<template>
  <div class="prompt">
    <div class="prompt-content ant-steps-small">
      <template v-for="(item,index) in steps">
        <div :title="item.title" class="prompt-node" :class="{'active': index === props.matchState }">
          <div class="ant-steps-item-icon">
            <span class="ant-steps-icon">{{item.id}}</span>
          </div>
          <div>{{item.title}}</div>
        </div>
      </template>
    </div>
    <template v-for="label in steps[props.matchState].content">
      <div>{{label}}</div>
    </template>
  </div>
</template>
<style scoped lang="scss">
  .prompt {
    font-family: Alibaba PuHuiTi;
    width: 100%;
    font-weight: 400;
    font-size: 12px;
    color: #2A2A2A;
    text-align: left;
    font-style: normal;
    text-transform: none;
    .prompt-content {
      margin-bottom: 15px;
      display: flex;
      .prompt-node {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        .ant-steps-item-icon{
          width: 15px;
          height: 15px;
          line-height: 13px;
          background-color: #A8A8A8;
          .ant-steps-icon {
            color: #fff;
          }
        }
      }
      .prompt-node.active {
        color: #287CFA;
        .ant-steps-item-icon{
          background-color: #287CFA;
          .ant-steps-icon {
            color: #fff;
          }
        }
      }
    }
  }
</style>