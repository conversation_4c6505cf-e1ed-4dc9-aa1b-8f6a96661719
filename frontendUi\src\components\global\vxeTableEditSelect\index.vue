<!--
 * @Descripttion: 输入框，如果有内容，展示下拉框
 * @Author: sunchen
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-25 10:02:18
-->
<template>
  <vxe-select
    v-if="
      [
        '工程类别',
        '工程用途',
        '工程类型',
        '建筑分类',
        '造价类别',
        '结构类型',
        '计算口径',
      ].includes(props.name) && projectStore.type == 'glj'
    "
    v-model="modeValues"
    :transfer="true"
    @change="saveCustomInput"
    :clearable="clearable"
  >
    <vxe-option
      v-for="(item, index) in selectList"
      :key="index"
      :value="item"
      :label="item"
    ></vxe-option>
  </vxe-select>
  <vxe-pulldown
    ref="pulldownRef"
    class="pulldown-wrap"
    :transfer="transfer"
    v-model="pulldown.visible"
    v-else
  >
    <template #default>
      <div class="pulldown-wraps">
        <vxe-input
          autofocus
          v-model="modeValues"
          ref="vxeInputRef"
          className="custom--inner"
          :maxlength="props.maxlength || 50"
          :clearable="false"
          :placeholder="`${
            props.list && props.list.length ? '请选择或输入' : '请输入内容'
          }`"
          @blur="blur"
        >
          <template #suffix>
            <i
              class="vxe-icon-caret-down icons"
              @click="focusEvent"
              v-if="props.list"
            ></i>
          </template>
        </vxe-input>
      </div>
    </template>
    <template #dropdown>
      <div class="input-wrap-dropdown">
        <div
          class="list-item"
          v-for="(i, k) in selectList"
          :key="i"
          @click="selectEvent(i)"
        >
          <span>{{ i }}</span>
        </div>
      </div>
    </template>
  </vxe-pulldown>
</template>
<script>
export default {
  name: 'vxeTableEditSelect',
};
</script>
<script setup>
import { nextTick, reactive, ref, watch, watchEffect } from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';

const projectStore = projectDetailStore();

const selectList = ref([]);
const props = defineProps([
  'list',
  'name',
  'filedValue',
  'transfer',
  'maxlength',
  'isNotLimit', //限制下拉列表中是否包含输入值  true -不限制  false限制
  'clearable',
]);
const clearable = props.clearable !== undefined ? props.clearable : false;

const emits = defineEmits(['update:filedValue', 'usePosition', 'showTable']);
const initList = ref([]);

watchEffect(() => {
  let data = xeUtils.clone(
    Array.isArray(props.list || '')
      ? props.list || []
      : (props.list || '').split(',')
  );
  initList.value = data;
  selectList.value = data;
});

let modeValues = ref(props.filedValue);
const vxeInputRef = ref(null);
watch(
  () => props.filedValue,
  () => {
    modeValues.value = props.filedValue;
  }
);

nextTick(() => {
  vxeInputRef.value?.focus();
});

const pulldown = reactive({
  visible: false,
  values: '',
});

const focusEvent = () => {
  pulldown.visible = true;
  emits('showTable', pulldown.visible);
};

const blur = () => {
  let value = xeUtils.trim(modeValues.value);
  const maxlength = props.maxlength || 50;
  if (value.length > maxlength) {
    value = value.slice(0, maxlength);
    message.warn(`名称过长，请输入${50}个字符范围内`);
  }
  if (
    props.list &&
    !props.isNotLimit &&
    !Array.isArray(props.list) &&
    props.list.length > 0 &&
    !props.list.includes(value)
  ) {
    modeValues.value = '';
    value = '';
  }
  emits('usePosition', 0);
  emits('update:filedValue', value);
};

const selectEvent = item => {
  emits('usePosition', 1);
  emits('update:filedValue', item);
  modeValues.value = item;
  pulldown.visible = false;
  selectList.value = initList.value;
};
const saveCustomInput = item => {
  selectEvent(item.value);
};
</script>

<style lang="scss" scoped>
.pulldown-wrap {
  width: 100%;
  ::v-deep(.vxe-input) {
    width: 98%;
  }
}
.input-wrap-dropdown {
  max-height: 40vh;
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
  .list-item {
    padding: 5px 4px;
    &:hover {
      background-color: azure;
      cursor: pointer;
    }
  }
}
</style>
