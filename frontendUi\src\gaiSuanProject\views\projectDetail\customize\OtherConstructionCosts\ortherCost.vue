<template>
  <!-- <div> -->
  <!-- <p class="title"><span class="text">费用代码</span></p> -->
  <div class="content">
    <div class="selectTree">
      <div class="tree">
        <a-directory-tree v-model:expandedKeys="expandedkeys" v-model:selectedKeys="selectedKeys" :tree-data="treeData"
          @select="changeSel" :defaultExpandAll="true"
          :fieldNames="{ children: 'children', title: 'name', key: 'sequenceNbr' }">
        </a-directory-tree>
      </div>
    </div>
    <div class="table-content">
      <div class="rightTopBox">
        <div class="rightTopText">{{ rightTitle }}<br/>{{ rightTitle2 }}</div>
        <vxe-grid v-bind="gridOptions" height="250"
          :show-header="selTreeName == '等额本息' || selTreeName === '等额本金' ? false : true"></vxe-grid>
        <div class="rightBottomText">{{ rightBtTitle }}</div>
      </div>
      <div class="rightBottom">
        <div class="titleBox">
          <icon-font class="titleLogo" type="icon-feiyongjisuan" />费用计算
        </div>
        <a-form ref="formRef" :rules="rules" :model="formData" :key="componentKey" :label-col="{ style: { width: '196px' } }" :wrapper-col="{ span: 15 }"
          autocomplete="off" @finish="handleOk">
          <a-row>
            <a-col :span="12" v-if="selTreeName === '水土保持补偿费'">
              <a-form-item label="项目" name="projectType">
                <a-select :title="formData.projectType" v-model:value="formData.projectType" size="small" @change="getFee">
                  <a-select-option title="一般性生产建设项目" value="一般性生产建设项目">一般性生产建设项目</a-select-option>
                  <a-select-option title="开采矿产资源" value="开采矿产资源">开采矿产资源</a-select-option>
                  <a-select-option title="取土、挖砂（河道采砂除外）、采石以及烧制砖、瓦、瓷、石灰" value="取土、挖砂（河道采砂除外）、采石以及烧制砖、瓦、瓷、石灰">取土、挖砂（河道采砂除外）、采石以及烧制砖、瓦、瓷、石灰</a-select-option>
                  <a-select-option title="排放废弃土、石、渣" value="排放废弃土、石、渣">排放废弃土、石、渣</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '水土保持补偿费'&&formData.projectType==='开采矿产资源'">
              <a-form-item label="阶段" name="subProjectType">
                <a-select :title="formData.subProjectType" v-model:value="formData.subProjectType" size="small" @change="getFee">
                  <a-select-option title="建设期间" value="建设期间">建设期间</a-select-option>
                  <a-select-option title="开采期间石油、天然气" value="开采期间石油、天然气">开采期间石油、天然气</a-select-option>
                  <a-select-option title="开采期间石油、天然气以外的矿产（露天矿）" value="开采期间石油、天然气以外的矿产（露天矿）">开采期间石油、天然气以外的矿产（露天矿）</a-select-option>
                  <a-select-option title="开采期间石油、天然气以外的矿产（地下矿）" value="开采期间石油、天然气以外的矿产（地下矿）">开采期间石油、天然气以外的矿产（地下矿）</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <!-- &&selTreeName !== '水土保持补偿费' -->
            <a-col :span="12" v-if="selTreeName !== '地质灾害危险性评估费'&&selTreeName !== '水土保持补偿费'">
              <a-form-item :label="moneyName+'（万元）'" name="money">
                 <a-select
                  allowClear show-search  
                  v-model:value="formData.money"
                  size="small"
                  :filter-option="filterOption"
                  option-label-prop="valueCopy"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="changeMoneyHandle"
                  @search="handleSearch($event,'money')"
                  :options="moneyList"
                >
                
                <!-- @blur="handleMoneyBlur" -->
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- v-if="moneyList.length>0"
                <a-input-number  style="width:100%;" v-else v-model:value="formData.money" :min="0" addon-after="万元" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '水土保持补偿费'">
              <a-form-item :label="formData.projectType==='一般性生产建设项目'||formData.projectType==='开采矿产资源'?'征用土地面积':formData.projectType==='取土、挖砂（河道采砂除外）、采石以及烧制砖、瓦、瓷、石灰'?'取土、挖沙、采石量':'土、石、渣量'" name="landArea">
                <a-input-number  style="width:100%;" v-model:value="formData.landArea" :min="0" addon-after="㎡" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '结算审核咨询费' || selTreeName === '施工阶段造价咨询费'">
              <a-form-item label="审定额（万元）" name="sde">
                <a-input-number  style="width:100%;" v-model:value="formData.sde" :min="1" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '结算审核咨询费' || selTreeName === '施工阶段造价咨询费'">
              <a-form-item label="核增核减超过比（%）" name="hzhjcgb">
                <a-input-number  style="width:100%;" v-model:value="formData.hzhjcgb" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '施工阶段造价咨询费'">
              <a-form-item label="收费费率（2.5‰~5‰）" name="sfRate">
                <a-input-number  style="width:100%;" v-model:value="formData.sfRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '结算审核咨询费'">
              <a-form-item label="收费费率（4‰~8‰）" name="sfRate">
                <a-input-number  style="width:100%;" v-model:value="formData.sfRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程设计费' || selTreeName === '总体设计费' || selTreeName === '施工图预算编制费' || selTreeName === '竣工图编制费' || selTreeName === '工程监理费（通用）' || selTreeName === '工程监理费（河北）' || selTreeName === '预算编制咨询费' || selTreeName === '工程量清单编制咨询费' || selTreeName === '招标控制价编制咨询费' || selTreeName === '施工阶段造价咨询费' || selTreeName === '结算审核咨询费'">
              <a-form-item label="专业调整系数" name="zyRate">
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.zyRate" :min="0" size="small" @blur="getFee" /> -->
                <!-- <a-select v-model:value="formData.zyRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['专业调整系数']" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.zyRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'zyRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['专业调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '编制项目建议书' || selTreeName === '编制可行性研究报告' || selTreeName === '评估项目建议书' || selTreeName === '编制环境影响报告书（含大纲）' || selTreeName === '编制环境影响报告表' || selTreeName === '评估环境影响报告书（含大纲）' || selTreeName === '评估环境影响报告表' || selTreeName === '编制社会稳定风险评估报告' || selTreeName === '评价社会稳定风险评估报告' || selTreeName === '评估可行性研究报告'">
              <a-form-item label="行业调整系数" name="hytzRate">
                <!-- <a-select v-model:value="formData.hytzRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['行业调整系数']" @change="getFee">
                </a-select> -->

                  <a-select
                  v-model:value="formData.hytzRate"
                  size="small"
                  allowClear 
                  show-search
                  :filter-option="filterOption"
                  option-label-prop="valueCopy"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  :options="coefficientListCopy['行业调整系数']"
                  @search="handleSearch($event,'hytzRate')"
                  @blur="handleBlur"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.hytzRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程设计费' || selTreeName === '总体设计费' || selTreeName === '施工图预算编制费' || selTreeName === '竣工图编制费' || selTreeName === '编制项目建议书' || selTreeName === '编制可行性研究报告' || selTreeName === '评估项目建议书' || selTreeName === '评估可行性研究报告'">
              <a-form-item label="工程复杂调整系数（0.8~1.2）" name="gcfzRate">
                <!-- <a-select v-model:value="formData.gcfzRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['工程复杂调整系数']" @change="getFee">
                </a-select> -->

                <!-- <a-select
                  v-model:value="formData.gcfzRate"
                  size="small"
                  allowClear show-search 
                  :filter-option="filterOption"
                  option-label-prop="valueCopy"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'gcfzRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['工程复杂调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select> -->
                <a-input-number  style="width:100%;" v-model:value="formData.gcfzRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程设计费' || selTreeName === '总体设计费' || selTreeName === '施工图预算编制费' || selTreeName === '竣工图编制费'">
              <a-form-item label="附加调整系数" name="fjRate">
                <!-- <a-select v-model:value="formData.fjRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['附加调整系数']" @change="getFee">
                </a-select> -->

                <a-select
                  v-model:value="formData.fjRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'fjRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['附加调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.fjRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程设计费' || selTreeName === '总体设计费' || selTreeName === '施工图预算编制费' || selTreeName === '竣工图编制费'">
              <a-form-item label="其他调整系数" name="qtRate">
                <!-- <a-select v-model:value="formData.qtRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['其他调整系数']" @change="getFee">
                </a-select> -->

                 <a-select
                  v-model:value="formData.qtRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'qtRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['其他调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.qtRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '总体设计费'">
              <a-form-item label="总体设计费费率（%）" name="ztsjfRate">
                <a-input-number  style="width:100%;" v-model:value="formData.ztsjfRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '施工图预算编制费'">
              <a-form-item label="施工图预算编制费费率（%）" name="sgtysRate">
                <a-input-number  style="width:100%;" v-model:value="formData.sgtysRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '竣工图编制费'">
              <a-form-item label="竣工图编制费费率（%）" name="jgtbzfRate">
                <a-input-number  style="width:100%;" v-model:value="formData.jgtbzfRate" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程监理费（通用）' || selTreeName === '工程监理费（河北）'">
              <a-form-item label="复杂调整系数" name="fzRate">
                <!-- <a-select v-model:value="formData.fzRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['复杂调整系数']" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.fzRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'fzRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['复杂调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.fzRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '工程监理费（通用）'">
              <a-form-item label="高程调整系数" name="gcRate">
                <!-- <a-select v-model:value="formData.gcRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['高程调整系数']" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.gcRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'gcRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['高程调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.gcRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '编制环境影响报告书（含大纲）' || selTreeName === '编制环境影响报告表' || selTreeName === '评估环境影响报告书（含大纲）' || selTreeName === '评估环境影响报告表'">
              <a-form-item label="环境敏感调整系数" name="hjmgRate">
                <!-- <a-select v-model:value="formData.hjmgRate" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['环境敏感调整系数']" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.hjmgRate"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'hjmgRate')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['环境敏感调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.hjmgRate" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="工程类别" name="projectType">
                <a-select :title="formData.projectType" v-model:value="formData.projectType" size="small" @change="worksCategory">
                  <a-select-option title="线性工程" value="线性工程">线性工程</a-select-option>
                  <a-select-option title="矿山工程" value="矿山工程">矿山工程</a-select-option>
                  <a-select-option title="水利水电工程" value="水利水电工程">水利水电工程</a-select-option>
                  <a-select-option title="工业与民用建筑工程" value="工业与民用建筑工程">工业与民用建筑工程</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="评估等级" name="evaluationLevel">
                <a-select v-model:value="formData.evaluationLevel" size="small" @change="getFee(1)">
                  <a-select-option value="一级">一级</a-select-option>
                  <a-select-option value="二级">二级</a-select-option>
                  <a-select-option value="三级">三级</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="复杂程度" name="complexitydz">
                <a-select v-model:value="formData.complexitydz" size="small" @change="getFee">
                  <a-select-option value="复杂" v-if="formData.evaluationLevel!=='三级'">复杂</a-select-option>
                  <a-select-option value="中等">中等</a-select-option>
                  <a-select-option value="简单" v-if="formData.evaluationLevel!=='二级'">简单</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '岩土工程设计费'">
              <a-form-item label="复杂程度" name="complexity">
                <a-select v-model:value="formData.complexity" size="small" @change="getFee">
                  <a-select-option value="Ⅰ级">Ⅰ级</a-select-option>
                  <a-select-option value="Ⅱ级">Ⅱ级</a-select-option>
                  <a-select-option value="Ⅲ级">Ⅲ级</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '编制社会稳定风险评估报告' || selTreeName === '评价社会稳定风险评估报告'">
              <a-form-item label="敏感程度调整系数" name="mgcdtzxh">
                <!-- <a-select v-model:value="formData.mgcdtzxh" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['社会稳定风险敏感程度调整系数']" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.mgcdtzxh"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'mgcdtzxh')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['社会稳定风险敏感程度调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.mgcdtzxh" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '编制社会稳定风险评估报告' || selTreeName === '评价社会稳定风险评估报告'">
              <a-form-item label="区域范围调整系数" :min="0" name="qyfwtzxx">
                <!-- <a-select v-model:value="formData.qyfwtzxx" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['区域范围调整系数']" @change="getFee">
                </a-select> -->
                 <a-select
                  v-model:value="formData.qyfwtzxx"
                  size="small"
                  option-label-prop="valueCopy"
                  allowClear show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="getFee"
                  @search="handleSearch($event,'qyfwtzxx')"
                  @blur="handleBlur"
                  :options="coefficientListCopy['区域范围调整系数']"
                >
                  <template #option="{name,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{name}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input v-model:value="formData.qyfwtzxx" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="地区调整系数（0.8~1.2）" name="dqtzxs">
                <a-input-number  style="width:100%;" v-model:value="formData.dqtzxs" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="评估长度/面积" name="landOrArea" addon-after="km/km2">
                <a-input-number  style="width:100%;" v-model:value="formData.landOrArea" addon-after="km/km2" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="工程类别调整系数K1" name="projectTypeK1">
                <!-- <a-select v-model:value="formData.projectTypeK1" size="small" :options="projectTypeK1options" @change="getFee">
                </a-select> -->
                <a-select
                  v-model:value="formData.projectTypeK1"
                  size="small"
                  allowClear 
                  show-search
                  :filter-option="filterOption"
                  option-label-prop="valueCopy"
                  :dropdownMatchSelectWidth="false"
                  @change="changePtK1"
                  :options="projectTypeK1options"
                  @search="handleSearch($event,'projectTypeK1')"
                >
                  <!-- @blur="handleBlur" -->
                  <template #option="{label,valueCopy }">
                    <div class="antSelectDropdown">
                      <span>{{label}}</span>
                      <div style="width:20px"></div>
                      <span>{{valueCopy}}</span>
                    </div>
                  </template>
                </a-select>
                <!-- <a-input-number  style="width:100%;" v-model:value="formData.projectTypeK1" :min="0" size="small" @blur="getFee" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '等额本息' || selTreeName === '等额本金'">
              <a-form-item label="贷款利率（%）" name="dkll">
                <a-input-number  style="width:100%;" v-model:value="formData.dkll" :min="0" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName === '等额本息' || selTreeName === '等额本金'">
              <a-form-item label="还款期数（月）" name="hkqh">
                <a-input-number  style="width:100%;" v-model:value="formData.hkqh" :min="0" addon-after="月" size="small" @blur="getFee" />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="selTreeName !== '等额本息' && selTreeName !== '等额本金'">
              <a-form-item label="浮动值（%）" name="floatRate">
                <a-input-number  style="width:100%;" v-model:value="formData.floatRate" :min="0" @blur="getFee" size="small"/>
              </a-form-item>
            </a-col>

            <a-col :span="12" v-if="selTreeName === '预算编制咨询费' || selTreeName === '工程量清单编制咨询费' || selTreeName === '招标控制价编制咨询费' || selTreeName === '施工阶段造价咨询费' || selTreeName === '结算审核咨询费'">
              <a-form-item label="钢筋工程量精细计量方式" name="jlMethod">
                <a-select v-model:value="formData.jlMethod" size="small" :fieldNames="{label: 'name'}" :options="coefficientList['钢筋工程量精细计量方式']" @change="jlMethodChange">
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="formData.jlMethod!==''&&(selTreeName === '预算编制咨询费' || selTreeName === '工程量清单编制咨询费' || selTreeName === '招标控制价编制咨询费' || selTreeName === '施工阶段造价咨询费' || selTreeName === '结算审核咨询费')">
              <a-form-item label="钢筋重量（吨）" name="weight">
                 <!-- <a-input-number  style="width:100%;" v-model:value="formData.weight" :min="0" size="small" @blur="getFee" /> -->
                 <a-select
                  allowClear show-search  
                  v-model:value="formData.weight"
                  size="small"
                  :filter-option="filterOption"
                  option-label-prop="scCount"
                  :fieldNames="{label: 'name'}"
                  :dropdownMatchSelectWidth="false"
                  @change="changeWeightHandle"
                  @search="handleWeightSearch($event,'weight')"
                  :options="weightList"
                  @blur="getFee"
                >
                  <template #option="{name,scCount,unitCN }">
                    <div class="antSelectDropdown">
                      <span>三材钢筋</span>
                      <span>{{name}}</span>
                      <span>{{scCount}}{{unitCN}}</span>
                    </div>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            
            <!-- 下一个迭代做 -->
            <!-- <a-col :span="12" v-if="selTreeName === '地质灾害危险性评估费'">
              <a-form-item label="工程规模调整系数K2" name="projectTypeK2">
                <a-input v-model:value="formData.projectTypeK2" size="small" @blur="getFee" />
              </a-form-item>
            </a-col> -->
          </a-row>
          <a-form-item :label="calculateName">
            <a-input v-model:value="feeNum" disabled size="small" addon-after="万元"/>
          </a-form-item>
          <a-form-item label="计算过程">
            <a-textarea v-model:value="jsgcData" :rows="2" disabled size="small" />
          </a-form-item>
        </a-form>
      </div>
    </div>
     <common-modal
        v-model:modelValue="codeNullVisible"
        className="dialog-comm"
        title="提示"
        width="400px"
      >
        <div style="margin-bottom: 20px;">该费用已修改，是否应用？</div>
        <div class="footer-btn-list">
          <a-button @click="codeNullCancel"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="codeNullQuery"
            >确定</a-button
          >
        </div>
      </common-modal>
  </div>
  <!-- </div> -->
</template>

<script setup>
import { ref, reactive, onMounted,nextTick,toRaw,watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@gaiSuan/api/csProject';
import { tablePage } from './tablePage';
import { message } from 'ant-design-vue';
import { getUrl, pureNumber, pureNumberSHL } from '@/utils/index';

const props = defineProps({
  jsqsequenceNbr: String,
  jsqCategory: String,
  isPriceModel:String,
});

const emits = defineEmits(['save']);
const store = projectDetailStore();
const expandedkeys = ref([]);
const selectedKeys = ref([]);
const treeData = ref([]);
const codeNullVisible=ref(false)
const initialFormData=ref('')
const rightTitle = ref('');
const rightTitle2 = ref('');
const rightBtTitle = ref('');
const formRef = ref();
const selTreeName = ref('');
const selTreeAllName = ref('');
const calculateName = ref('');
const moneyName = ref('');
const moneyList = ref([])
const weightList = ref([])
const promptApplicationData = ref({});
const feeNum = ref('');
const fields=ref(null)
const calculationProcess=ref('')
const jsgcData = ref('');
const coefficientList= ref({})
const coefficientListCopy=ref({})
const leftChangeSel=ref({})
const componentKey=ref(0)
const currentFormData=ref({})
const formData = reactive({
  floatRate:100,
  ztsjfRate:5,
  zyRate:1,
  gcfzRate:1,
  fjRate:1,
  qtRate:1,
  sgtysRate:10,
  jgtbzfRate:8,
  fzRate:1,
  gcRate:1,
  hytzRate:1,
  hjmgRate:1,
  gcfzRate:1,
  mgcdtzxh:1,
  qyfwtzxx:1,
  projectType:'线性工程',
  evaluationLevel:'一级',
  complexitydz:'复杂',
  complexity:'Ⅰ级',
  dqtzxs:1,
  projectTypeK1:1,
  dkll:4.75,
  hkqh:'',
  jlMethod:'',
  weight:'',
  money:'',
  landOrArea:''
});
const projectTypeK1options = ref([
  {label:'铁路（地铁）、高等级公路',value:'铁路（地铁）、高等级公路-1',valueCopy:1},
  {label:'一般公路、输水渠道',value:'一般公路、输水渠道-0.8',valueCopy:0.8},
  {label:'输油、输水、输气管线、光缆、输电线路',value:'输油、输水、输气管线、光缆、输电线路-0.6',valueCopy:0.6},
  ])

const gridOptions = reactive({
  border: true,
  height: 287,
  align: null,
  columnConfig: {
    resizable: true
  },
  columns: tablePage.projectBuildAdminFee,
  headerCellClassName:'',
  mergeCells: [],
  data: []
})
const rules = {
  projectType: [{ required: true, trigger: 'blur' }],
  subProjectType: [{ required: true, trigger: 'blur' }],
  money: [{ required: true, trigger: 'blur' }],
  landArea: [{ required: true, trigger: 'blur' }],
  sde: [{ required: true, trigger: 'blur' },
  { validator: (rule, value, callback) => {
     if (value === '0') {
       callback(new Error('审定额（万元）不可为0！'))
     } else {
       callback()
     }
   }, trigger: 'blur' }
  ],
  hzhjcgb: [{ required: true, trigger: 'blur' }],
  sfRate: [{ required: true, trigger: 'blur' }],
  sfRate: [{ required: true, trigger: 'blur' }],
  zyRate: [{ required: true, trigger: 'blur' }],
  hytzRate: [{ required: true, trigger: 'blur' }],
  gcfzRate: [{ required: true, trigger: 'blur' }],
  weight:[{ required: false, trigger: 'blur' }],
  fjRate: [{ required: true, trigger: 'blur' }],
  qtRate: [{ required: true, trigger: 'blur' }],
  ztsjfRate: [{ required: true, trigger: 'blur' }],
  sgtysRate: [{ required: true, trigger: 'blur' }],
  jgtbzfRate: [{ required: true, trigger: 'blur' }],
  fzRate: [{ required: true, trigger: 'blur' }],
  gcRate: [{ required: true, trigger: 'blur' }],
  hjmgRate: [{ required: true, trigger: 'blur' }],
  evaluationLevel: [{ required: true, trigger: 'blur' }],
  complexitydz: [{ required: true, trigger: 'blur' }],
  complexity: [{ required: true, trigger: 'blur' }],
  mgcdtzxh: [{ required: true, trigger: 'blur' }],
  qyfwtzxx: [{ required: true, trigger: 'blur' }],
  dqtzxs: [{ required: true, trigger: 'blur' }],
  landOrArea: [{ required: true, trigger: 'blur' }],
  projectTypeK1: [{ required: true, trigger: 'blur' }],
  dkll: [{ required: true, trigger: 'blur' }],
  hkqh: [{ required: true, trigger: 'blur' }],
  floatRate: [{ required: true, trigger: 'blur' }],
};
watch(
  () => [props.isPriceModel],
  () => {
    getOtherProjectCostCalculatorParams()
    getWeightList();
  }
);
onMounted(() => {
  currentFormData.value={...formData}
  // getTreeList();
  getOtherProjectCostCalculatorParams()
  getWeightList();
});
// 获取左侧树结构
const getTreeList = (isError) => {
  let apiData = {
    projectId: store.currentTreeGroupInfo?.constructId,
  };
  csProject.getOtherProjectCostCalculatorTree(apiData).then(async (res) => {
    if(res.status !== 200){
      return message.error(res.message)
    }
    let data=res.result
    if(!props.jsqsequenceNbr || isError){
      treeData.value=data
      expandedkeys.value=[data[0].sequenceNbr]
      selectedKeys.value=[data[0].sequenceNbr]
    }
    selTreeName.value=data[0].name
    selTreeAllName.value=data[0].name
    getTableList(data[0].name)
    getMoneyList(data[0])


  });
};
// 处理树结构
const handleTreeList = (data,ids) => {
    if(data.ifCheck){  // 如果是选中的数据 则加到数组中
      ids.push({type:1,...data});
      return true
    }else{ // 
      let unexistCheck = true
      if(data.children){ //
        data.children.forEach(itm=>{
          let temp = handleTreeList(itm,ids)
          if(temp){
            unexistCheck = false
          }
        })
      }
      if(unexistCheck){
        return false
      }else{
        ids.push({type:0,...data})
        return true
      }

    }
}
// const isEqual = (obj1, obj2) => {
//   // 简单的对象比较函数，可以根据需要替换为更复杂的比较逻辑
//   return JSON.stringify(obj1) === JSON.stringify(obj2);
// }
// 获取钢筋重量下拉菜单
const getWeightList=()=>{
  let apiData={
    constructId:store.currentTreeGroupInfo?.constructId,
  }
  csProject.getScCountGJ(apiData).then(res => {
    if(res.status !== 200){
      return message.error(res.message)
    }
    let datas=res.result
    datas.forEach(data => {
      data['value']=data.scCount
    })
    weightList.value=datas
  })
}
// 弹窗取消
const codeNullCancel=()=>{
  codeNullVisible.value=false
  jsgcData.value = ''
  initialFormData.value = ''
  changeSel(leftChangeSel.value.val,leftChangeSel.value.row,leftChangeSel.value.nameList,leftChangeSel.value.isEcho)
}
// 弹窗确认
const codeNullQuery=()=>{
  let apiData={
      calculatorBasis:promptApplicationData.value.selTreeAllName,
      projectId: store.currentTreeGroupInfo?.constructId,
      amount:promptApplicationData.value.feeNum,
      fields:toRaw(promptApplicationData.value.fields),
      calculationProcess:promptApplicationData.value.calculationProcess,
    }
    csProject.getOtherProjectCostCalculatorData(apiData).then(res => {
      // 如果为500则列表没有此费用类别需要调用树结构保存数据
      console.info(444444444,res.status)
      if(res.status==500){
        csProject.updateOtherProjectCostCalculatorTree(apiData).then(res => {
          console.info(444444444,res)
          getOtherProjectCostCalculatorParams(true)
        })
        
      }
      if(res.status !== 200){
        codeNullVisible.value=false
        jsgcData.value = ''
        initialFormData.value = ''
        changeSel(leftChangeSel.value.val,leftChangeSel.value.row,leftChangeSel.value.nameList,leftChangeSel.value.isEcho)
        return message.error(res.message)
      }
      message.success('应用成功');
      codeNullVisible.value=false
      treeData.value=[]
      initialFormData.value=JSON.parse(JSON.stringify(jsgcData.value))
      // changeSel(leftChangeSel.value.val,leftChangeSel.value.row,leftChangeSel.value.nameList,leftChangeSel.value.isEcho)
      getOtherProjectCostCalculatorParams(true)
      emits('save')
    })
}

// 点击左侧树结构
/**
 * 
 * @param val 
 * @param row 
 * @param nameList 
 * @param isEcho 
 * @param isSet  是否赋值，解决初始化，空的费用，然后带出来了
 */
const changeSel = (val,row,nameList,isEcho,isSet = true) => {
  // 如果点击的不是最底层则不做任何操作
  if(row.node&&row.node.children.length!==0){
    return;
  }
  // 判断值是否修改，如果有修改则提示是否应用
  if(jsgcData.value!==initialFormData.value){
    codeNullVisible.value=true
    leftChangeSel.value={val,row,nameList,isEcho}
    return
  }
  gridOptions.mergeCells = []
  gridOptions.headerCellClassName='showTableRow'
  selTreeName.value=row.node?.name
  let node=[]
  if(!isEcho){
    node=getFatherId(row.node)
    getCoefficient(row)
    getMoneyList(row)
  }else{
    node=nameList
    let name={node:{parent:{node:{name:nameList[nameList.length-1]}}}}
    getCoefficient(name)
    getMoneyList(row)
  }
  // initialFormData.value=JSON.parse(JSON.stringify(jsgcData.value))
  node.reverse()
  if(node.length>0){
    let calculatorBasis=node.join('-')
    selTreeAllName.value=calculatorBasis
    getTableList(calculatorBasis)
  }
  setTimeout(()=>{

    // 如果原本就有数据则将数据填入到输入框内
    if(row.node.fields && isSet){
      let fieldList=null
      if(row.node.fields instanceof Map){
        fieldList=Object.fromEntries(row.node.fields)
      }else{
        fieldList=row.node.fields
      }
      fields.value=fieldList
      Object.keys(fieldList).forEach((key) => {
        if(key==='amount'){
          feeNum.value=fieldList[key]
        }
        if(key==='calculationProcess'){
          jsgcData.value=fieldList[key]
          calculationProcess.value=fieldList[key]
          initialFormData.value=JSON.parse(JSON.stringify(jsgcData.value))
        }
        if(key==='jlMethod'){
          jlMethodChange(fieldList[key])
        }
        formData[key] = fieldList[key];
      });
    }else{
      feeNum.value=''
      jsgcData.value=''
      initialFormData.value=''
      calculationProcess.value=''
      formData.floatRate=100
      formData.ztsjfRate=5
      formData.zyRate=1
      formData.gcfzRate=1
      formData.fjRate=1
      formData.qtRate=1
      formData.sgtysRate=10
      formData.jgtbzfRate=8
      formData.fzRate=1
      formData.gcRate=1
      formData.hytzRate=1
      formData.hjmgRate=1
      formData.gcfzRate=1
      formData.mgcdtzxh=1
      formData.qyfwtzxx=1
      formData.projectType='线性工程'
      formData.evaluationLevel='一级'
      formData.complexitydz='复杂'
      formData.complexity='Ⅰ级'
      formData.dqtzxs=1
      formData.projectTypeK1=1
      formData.dkll=4.75
      formData.hkqh=''
      formData.jlMethod=''
      formData.weight=''
      formData.money=''
      formData.landOrArea=''
    }
    promptApplicationData.value= {
      calculationProcess: JSON.parse(JSON.stringify(calculationProcess.value)),
      selTreeAllName:JSON.parse(JSON.stringify(selTreeAllName.value)),
      feeNum:JSON.parse(JSON.stringify(feeNum.value)),
      fields:fields.value,
    }
  },500)
}

const getOtherProjectCostCalculatorParams= (type=false)  =>{
  let apiData={
    projectId:store.currentTreeGroupInfo?.constructId,
    sequenceNbr:props.jsqsequenceNbr
  }
  csProject.getOtherProjectCostCalculatorParams(apiData).then(res=>{
    let ids=[]
    let nameList=[]
    let datas=res.result
    treeData.value=datas
    for (const item of datas) {
      handleTreeList(item,ids)
    }
    console.info(4444444444,leftChangeSel.value.val)
    // 如果选中行没有费用类别则选择树结构第一行，否则寻找选中行的费用类别进行回显
    // if(ids.length==0||props.jsqCategory==''||props.jsqCategory==undefined){
    if(leftChangeSel.value.val==undefined&&(ids.length==0||props.jsqCategory==''||props.jsqCategory==undefined)){
      expandedkeys.value=[datas[0].sequenceNbr]
      selectedKeys.value=[datas[0].sequenceNbr]
      nameList=[datas[0].name]
    }else{
      for (const item of ids) {
        nameList.push(item.name)
        if(item.type===0){
          expandedkeys.value.push(item.sequenceNbr)
        }
        if(item.type===1){
          selectedKeys.value.push(item.sequenceNbr)
        }
      } 
    }
    
    // 如果不是第一次则传左侧点击树的结果
    if(type){
      let nameBackUp=leftChangeSel.value.row.node.nameBackUp.split('-')
      nameBackUp.reverse()
      changeSel(leftChangeSel.value.val,leftChangeSel.value.row,nameBackUp,true)
      return
    }
    // 执行点击选择树操作
    if(ids.length>0&&props.jsqCategory!=''){
      let row={
        node:ids[0]
      }
      changeSel(leftChangeSel.value.val,row,nameList,true)
    }else{
      changeSel(selectedKeys.value,{node:datas[0]},nameList,true,false)
    }
    getprojectTypeK1(formData.projectType)
    
  })
}
const jlMethodChange = (val) => {
  if(!val){
    rules['weight'][0].required=false
  }else{
    rules['weight'][0].required=true
  }
  componentKey.value++
  setTimeout(()=>{
    getFee(val)
  },100)
}
// 递归获取父节点
const getFatherId=(row)=> {
  let arr=[row.name]
  let parent = row.parent;
  // 如果没有父级则直接返回当前点击节点id
  if(parent==undefined){
    return arr
  }
  while (parent) {
    if (parent.parent==undefined) {
      arr.push(parent.node.name)
      return arr
    } else {
      arr.push(parent.node.name)
      parent = parent.parent;
    }
  }
}
const findNodeWithSequence=(data, sequenceNbr, parentNodes = [])=> {
      for (const node of data) {
        const newNode = { ...node, parentNodes };
        const newParentNodes = [...parentNodes, newNode];

        if (node.sequenceNbr === sequenceNbr) {
          return newNode;
        }

        if (node.children) {
          const result = findNodeWithSequence(
            node.children,
            sequenceNbr,
            newParentNodes
          );
          if (result) {
            return result;
          }
        }
      }
      return null;
    }
const getMoneyList=(row)=>{
  let calculatorBasis='项目建设管理费'
  if(row.node!==undefined&&row.node.name!=="编制社会稳定风险评估报告"&&row.node.name!=="评价社会稳定风险评估报告"&&row.node.name!=="水土保持补偿费"&&row.node.name!=="预算编制咨询费"&&row.node.name!=="工程量清单编制咨询费"&&row.node.name!=="招标控制价编制咨询费"&&row.node.name!=="施工阶段造价咨询费"&&row.node.name!=="结算审核咨询费"&&row.node.name!=="岩土工程设计费"){
    calculatorBasis=row.node.nameBackUp
  }
  // selTreeAllName.value=calculatorBasis
  let apiData={
    constructId:store.currentTreeGroupInfo?.constructId,
    calculatorBasis,
  }
  csProject.getMoney(apiData).then(res=>{
    if(res.status !== 200){
      moneyList.value=[]
      return message.error(res.message)
    }
    if(res.result){
      for (const item of res.result) {
        let value=item.money
        item.valueCopy=value
        item.value=item.name+'-'+item.money
      }
      moneyList.value=res.result
    }else{
      moneyList.value=[]
    }
  })
}
// 查询系数列表
const getCoefficient=(row)=>{
  if(!row.node.parent){
    return
  }
  // node.parent.name
  let api=''

  if(row.node.parent.node.name==='工程设计费'){
    api='getGcsj'
  }
  if(row.node.parent.node.name==='工程监理费'){
    api='getGcjl'
  }
  if(row.node.parent.node.name==='环境影响咨询服务费'){
    api='getHjyx'
  }
  if(row.node.parent.node.name==='建设项目前期工作咨询费'){
    api='getJsxm'
  }
  if(row.node.parent.node.name==='社会稳定风险评估费'){
    api='getShfx'
  }
  if(row.node.parent.node.name==='造价咨询费'){
    api='getZjzx'
  }
  if(!api){
    return
  }
  csProject[api]().then(res=>{
    coefficientList.value=res.result
    let list = JSON.parse(JSON.stringify(coefficientList.value))
     for (const node in list) {
       for (const item of list[node]) {
         if(item.options){
           for (const item1 of item.options) {
             let value=item1.value
             item1.valueCopy=value
             item1.value=item.name+item1.name+'-'+item1.value
           }
         }else{
           let value=item.value
           item.valueCopy=value
           item.value=item.name+'-'+item.value
         }
       }
     }
     coefficientListCopy.value=list
  }).catch(err=>{})
}
// 获取右侧表格
const getTableList = (calculatorBasis) => {
  let apiData = {
    calculatorBasis,
  };
  csProject.getOtherProjectCostCalculatorBasis(apiData).then(res => {
    if(res.status !== 200){
      return message.error(res.message)
    }
    if(res.result){
      gridOptions.data=res.result.data
      rightTitle.value=res.result.basis
      rightTitle2.value=res.result.header
      rightBtTitle.value=res.result.footer
    }
    handleRightBox(calculatorBasis)
  });
};
const getprojectTypeK1= (type)=>{
  switch (type) {
    case '矿山工程':
      projectTypeK1options.value=[
         {label:'地下采矿工程',value:'地下采矿工程-1',valueCopy:1},
         {label:'露天转地下采矿工程',value:'露天转地下采矿工程-0.8',valueCopy:0.8},
         {label:'露天采矿工程',value:'露天采矿工程-0.6',valueCopy:0.6},
      ]
      break;
    case '水利水电工程':
      projectTypeK1options.value=[
         {label:'地下电厂水利水电工程',value:'地下电厂水利水电工程-1',valueCopy:1},
         {label:'地表电厂水利水电工程',value:'地表电厂水利水电工程-0.9',valueCopy:0.9},
         {label:'水利工程',value:'水利工程-0.8',valueCopy:0.8},
      ]
      
      break;
    case '工业与民用建筑工程':
      projectTypeK1options.value=[
         {label:'工业民用厂房、住宅工程',value:'工业民用厂房、住宅工程-1',valueCopy:1},
         {label:'其他场地及单跨场馆工程',value:'其他场地及单跨场馆工程-0.8',valueCopy:0.8},
      ]
      
      break;
  
    default:
      projectTypeK1options.value=[
         {label:'铁路（地铁）、高等级公路',value:'铁路（地铁）、高等级公路-1',valueCopy:1},
         {label:'一般公路、输水渠道',value:'一般公路、输水渠道-0.8',valueCopy:0.8},
         {label:'输油、输水、输气管线、光缆、输电线路',value:'输油、输水、输气管线、光缆、输电线路-0.6',valueCopy:0.6},
      ]
      break;
  }
}
const worksCategory= (type)=>{
  getFee(type)
  getprojectTypeK1(type)
}
// 获取右侧费用计算器
const getFee = (type) => {
  if(type===1&&formData.evaluationLevel==='三级'){
    formData.complexity='中等'
  }
  if(type===1&&formData.evaluationLevel==='二级'){
    formData.complexity='复杂'
  }
  formRef.value.validate().then(() => {
    let fieldList=['zyRate','hytzRate','gcfzRate','fjRate','qtRate','fzRate','gcRate','hjmgRate','mgcdtzxh','qyfwtzxx','jlMethod','money']
    for (const item of fieldList) {
      if(formData[item] && typeof formData[item] === 'string' && formData[item].includes('-')){
        formData[item]=formData[item].split('-')[1]
      }
    }
    let apiData = {
      ...formData,
      calculatorBasis:selTreeAllName.value
    };
    if(apiData.calculatorBasis=='地质灾害危险性评估费'){
      apiData.complexity=apiData.complexitydz
    }
    // if(formData.subProjectType!==''){
    //   apiData.projectTypeK1=''
    // }
    console.info(222222222,apiData)
    csProject.getOtherProjectCostCalculatorFormula(apiData).then(res => {
      console.info(222222222,res)
      if(res.status !== 200){
        return message.error(res.message)
      }
      let data=res.result.calculationProcess
      let arr=data.split('=')
      feeNum.value=arr[1]
      jsgcData.value=data
      fields.value=res.result.fields
      calculationProcess.value=res.result.calculationProcess
    });
  }).catch(error => {
    feeNum.value=''
    jsgcData.value=''
    fields.value=null
    calculationProcess.value=''
  });
  setTimeout(() => {  
    promptApplicationData.value= {
      calculationProcess: JSON.parse(JSON.stringify(calculationProcess.value)),
      selTreeAllName:JSON.parse(JSON.stringify(selTreeAllName.value)),
      feeNum:JSON.parse(JSON.stringify(feeNum.value)),
      fields:fields.value,
    }
  },400)
};

// 处理右侧表格及下方表单
const handleRightBox = (calculatorBasis) => {
  switch (calculatorBasis) {
    case "项目建设管理费":
      gridOptions.columns=tablePage.projectBuildAdminFee
      calculateName.value='项目建设管理费'
      moneyName.value='计算金额'
      break;
    case "招标代理费-招标代理费（通用）-工程招标代理费":
    case "招标代理费-招标代理费（河北）-工程招标代理费（河北）":
      gridOptions.columns=tablePage.engineeringBidding
      moneyName.value='投资估算额'
      calculateName.value='工程招标费'
      break;
    case "招标代理费-招标代理费（通用）-货物招标代理费":
    case "招标代理费-招标代理费（河北）-货物招标代理费（河北）":
      gridOptions.columns=tablePage.goodsBidding
      moneyName.value='投资估算额'
      calculateName.value='货物招标费'
      break;
    case "招标代理费-招标代理费（通用）-服务招标代理费":
    case "招标代理费-招标代理费（河北）-服务招标代理费（河北）":
      gridOptions.columns=tablePage.serviceBidding
      moneyName.value='投资估算额'
      calculateName.value='服务招标费'
      break;
    case "工程设计费-工程设计费":
    case "工程设计费-总体设计费":
    case "工程设计费-施工图预算编制费":
    case "工程设计费-竣工图编制费":
    case "工程监理费-工程监理费（通用）":
      gridOptions.columns=tablePage.engineeringDesignFee
      moneyName.value='计费额'
      calculateName.value='工程设计费'
      if(calculatorBasis=='工程设计费-总体设计费'){
        calculateName.value='总体设计费'
      }else if(calculatorBasis=='工程设计费-施工图预算编制费'){
        calculateName.value='施工图预算编制费'
      }else if(calculatorBasis=='工程设计费-竣工图编制费'){
        calculateName.value='竣工图编制费'
      }else if(calculatorBasis=='工程监理费-工程监理费（通用）'){
        calculateName.value='建设工程监理费'
      }
      break;
    case "工程设计费-岩土工程设计费":
      gridOptions.columns=tablePage.geotechnicalEngineeringdesignFee
      moneyName.value='岩土工程概算额'
      calculateName.value='岩土工程设计费'
      formData.complexity='Ⅰ级'
      break;
    case "工程监理费-工程监理费（河北）":
      gridOptions.columns=tablePage.engineeringSupervisionFee
      moneyName.value='计费额'
      calculateName.value='工程监理费'
      break;
    case "建设项目前期工作咨询费-编制项目建议书":
    case "建设项目前期工作咨询费-编制可行性研究报告":
    case "建设项目前期工作咨询费-评估项目建议书":
    case "建设项目前期工作咨询费-评估可行性研究报告":
      gridOptions.columns=tablePage.prepareProjectProposal
      moneyName.value='估算投资额'
      calculateName.value='编制项目建议书费'
      if(calculatorBasis=='建设项目前期工作咨询费-编制可行性研究报告'){
        calculateName.value='编制可行性研究报告咨询费'
      }else if(calculatorBasis=='建设项目前期工作咨询费-评估项目建议书'){
        calculateName.value='评估项目建议书费'
      }else if(calculatorBasis=='建设项目前期工作咨询费-评估可行性研究报告'){
        calculateName.value='评估可行性研究报告咨询费'
      }
      break;
    case "环境影响咨询服务费-编制环境影响报告书（含大纲）":
    case "环境影响咨询服务费-编制环境影响报告表":
    case "环境影响咨询服务费-评估环境影响报告书（含大纲）":
    case "环境影响咨询服务费-评估环境影响报告表":
      gridOptions.columns=tablePage.prepareAnEnvironmentalImpactReport
      gridOptions.mergeCells =tablePage.prepareAnEnvironmentalImpactReportMergeCells
      moneyName.value='估算投资额'
      calculateName.value='编制环境报告书费'
      if(calculatorBasis=='环境影响咨询服务费-编制环境影响报告表'){
        calculateName.value='编制环境影响报告表费'
      }else if(calculatorBasis=='环境影响咨询服务费-评估环境影响报告书（含大纲）'){
        calculateName.value='评估环境影响报告书费'
      }else if(calculatorBasis=='环境影响咨询服务费-评估环境影响报告表'){
        calculateName.value='评估环境影响报告表费'
      }
      break;
    case "社会稳定风险评估费-编制社会稳定风险评估报告":
      gridOptions.columns=tablePage.prepareSocialStabilityRiskAssessmentReport
      calculateName.value='编制社会稳定风险评估报告费'
      moneyName.value='计算额'
      break;
    case "社会稳定风险评估费-评价社会稳定风险评估报告":
      gridOptions.columns=tablePage.evaluationSocialStabilityRiskAssessmentReport
      calculateName.value='评价社会稳定风险评估报告费'
      moneyName.value='计算额'
      break;
    case "地质灾害危险性评估费":
      gridOptions.columns=tablePage.geologicalHazardRiskAssessmentFee
      gridOptions.mergeCells =tablePage.geologicalHazardRiskAssessmentFeeMergeCells
      gridOptions.headerCellClassName=(row)=>{
        if(row.column.field=='gstze'||row.column.field=='range1'){
          return 'hideTableRow'
        }else{
          return 'showTableRow'
        }
      }
      formData.complexitydz='复杂'
      calculateName.value='地质灾害评估费'
      break;
    case "造价咨询费-预算编制咨询费":
    case "造价咨询费-工程量清单编制咨询费":
    case "造价咨询费-招标控制价编制咨询费":
    case "造价咨询费-施工阶段造价咨询费":
    case "造价咨询费-结算审核咨询费":
      gridOptions.columns=tablePage.budgetPreparation
      gridOptions.mergeCells =tablePage.budgetPreparationMergeCells
      gridOptions.headerCellClassName=(row)=>{
        if(row.column.field=='rowspan2'){
          return 'selCall'
        }
      }
      setTimeout(()=>{
        let headCall = document.querySelector('.selCall');
        headCall.setAttribute('rowspan', '2');
        gridOptions.headerCellClassName=(row)=>{
          if(row.column.field=='c2'||row.column.field=='c3'){
            return 'hideTableRow'
          }else{
            return 'showTableRow'
          }
        }
      },1)
      moneyName.value='建安工程造价'
      calculateName.value='预算编制咨询费'
      if(calculatorBasis=='造价咨询费-工程量清单编制咨询费'){
        calculateName.value='工程量清单编制咨询费'
      }else if(calculatorBasis=='造价咨询费-招标控制价编制咨询费'){
        calculateName.value='招标控制价编制咨询费'
      }else if(calculatorBasis=='造价咨询费-施工阶段造价咨询费'){
        calculateName.value='施工阶段造价咨询费'
      }else if(calculatorBasis=='造价咨询费-结算审核咨询费'){
        calculateName.value='结算审核咨询费'
      }
      break;
    case "水土保持费-水土保持方案编制费":
    case "水土保持费-水土保持监理费":
    case "水土保持费-技术评估报告编制费":
    case "水土保持费-技术咨询服务费":
      gridOptions.columns=tablePage.preparationCostSoilWaterConservationPlan
      moneyName.value='计算额'
      calculateName.value='水土保持方案编制费'
      if(calculatorBasis=='水土保持费-水土保持监理费'){
        calculateName.value='水土保持监理费'
      }else if(calculatorBasis=='水土保持费-技术评估报告编制费'){
        calculateName.value='技术评估报告编制费'
      }else if(calculatorBasis=='水土保持费-技术咨询服务费'){
        calculateName.value='技术咨询服务费'
      }
      break;
    case "水土保持补偿费":
      gridOptions.columns=tablePage.compensationSoilWaterConservation
      gridOptions.mergeCells =tablePage.compensationSoilWaterConservationMergeCells
      calculateName.value='水土保持补偿费'
      formData.projectType='一般性生产建设项目'
      formData.subProjectType='建设期间'
      break;
    case "建设期贷款利息-等额本息":
    case "建设期贷款利息-等额本金":
      gridOptions.columns=tablePage.equalPrincipalInterest
      moneyName.value='贷款金额'
      calculateName.value='建设期贷款利息总和'
      break;
  }
}

// 切换工程调整类别K1
const changePtK1=(label,item) =>{
  if(item){
    formData['projectTypeK1']=item.valueCopy
    formData['subProjectType']=item.label
  }
  getFee()
}
const handleSearch=(value,field) =>{
  if(value){
    formData[field]=value
    formData['subProjectType']=''
    formData['source']=''
  }else{
    let regex = /^\d+(\.\d+)?$/
    if(!regex.test(formData[field])){
      formData[field]=''
    }
  }
  getFee()
}
const filterOption = () => {
  return true
}
const handleBlur=()=>{
  formData['source']=''
  getFee()
}
const handleMoneyBlur=()=>{
  let moneyStr=String(JSON.parse(JSON.stringify(formData.money)))
  let str=moneyStr
  let isIncludes=false
  for(let item of moneyList.value){
    if(str.includes(item.name)){
      isIncludes=true
    }
  }
  if(isIncludes){
    formData['source']=str
    let result=handleMoney(str)
    formData['money']=result
  }else{
    formData['source']=''
    formData['money']=eval(moneyStr)
  }
  getFee()
}
// 拆分四则运算并计算值
const handleMoney=(str)=>{
  if(str==''){
    return ''
  }
  let result = [];  
  let current = '';  
  let inIdentifier = false; // 用于标记是否正在读取一个标识符（如变量名或带空格的字符串）  
  for (let char of str) {  
    // 如果当前不在标识符中，并且遇到的是操作符或括号，则结束当前标识符（如果有的话）并添加它  
    if (!inIdentifier && (char === '+' || char === '-' || char === '*' || char === '/' || char === '(' || char === ')')) {  
      if (current) {  
          result.push(current);  
          current = '';  
      }  
      result.push(char);  
      continue;  
    }  
    // 如果遇到的是数字或字母（或下划线，假设标识符可能包含下划线），则开始或继续读取标识符  
    if (!isNaN(char * 1) || (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || char === '_') {  
        inIdentifier = true;  
    }  
    // 累加当前字符到标识符中  
    current += char;  
    // 如果离开了一个标识符（通过遇到非字母数字字符），则将其添加到结果中  
    if (inIdentifier && !(char >= '0' && char <= '9' || (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || char === '_' || char === ' ')) {  
        result.push(current);  
        current = '';  
        inIdentifier = false;  
        // 注意：这里没有立即添加非字母数字字符，因为它会在下一次循环中被处理  
    }  
  }
  // 确保最后一个标识符也被添加到结果中  
  if (current) {  
      result.push(current);  
  }
  for(let i in result) {
    for(let obj of moneyList.value){
      if(result[i]==obj.name){
        result[i]=obj.money+''
      }
    }
  }
  return eval(result.join(''))

}
// 切换money字段
const changeMoneyHandle=(type)=>{
  if(type){
    formData['source']=type.split('-')[0]
    formData['money']=type.split('-')[1]
  }
  getFee()
}
// 切换钢筋字段
const changeWeightHandle=(type)=>{
  formData['weightSource']='三材钢筋数量'
  getFee()
}
const handleWeightSearch=(value,field) =>{
  if(value){
    formData[field]=value
    formData['weightSource']=''
  }
}


defineExpose({
  selTreeAllName,
  feeNum,
  fields,
  calculationProcess,
  formRef,
  selTreeName,
  getOtherProjectCostCalculatorParams
});
</script>
<style lang="scss" scoped>
.title {
  width: 100%;
  background-color: #e7e7e7;
  height: 35px;
  text-align: left;
  margin-top: 15px;

  .text {
    display: inline-block;
    width: 128px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: #f8fbff;
    border-top: 2px solid #4786ff;
  }
}

.content {
  display: flex;
  width: 100%;
  height: 92%;
  // height: calc(100% - 35px - 15px - 14px);
  justify-content: space-around;

  .selectTree {
    flex: 1.3;
    height: 100%;
    margin: 0 10px;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    .tree {
      height: 100%;
      border: 1px solid #dcdfe6;
      overflow: scroll;
    }
  }

  .table-content {
    height: 100%;
    flex: 4;
    overflow: hidden;
    margin-left: 30;
  }
}

.content :deep(.ant-tree) {
  padding-top: 20px;
  height: 100%;
}

:deep(.rightTopBox) {
  border: 1px solid #dcdfe6;
  // height: 388px;
  height: 353px;

  .showTableRow {
    display: show;
  }

  .hideTableRow {
    display: none;
  }
}

.rightTopText {
  text-align: center;
  font-weight: bold;
  font-size: 14px;
  color: black;
  border: 1px solid #dcdfe6;
  padding: 10px;
}

.rightBottomText {
  border: 1px solid #dcdfe6;
  color: black;
  padding: 5px;
  height:37px;
  overflow-x: hidden;
  overflow-y: scroll;
}

:deep(.rightBottom) {
  border: 1px solid #dcdfe6;
  padding: 5px;
  margin-top: 10px;
  height: calc(100% - 363px);
  overflow: scroll;

  .ant-form-item {
    margin-bottom: 5px;
  }

  .ant-form-item-label > label {
    // display: flex;
    // justify-content: flex-end;
    // line-height: 16px;
    height: 40px;
    // align-items: center;

    label {
      white-space: normal;
      text-align: right;
      padding-right: 10px;
    }
  }
  .ant-row{
    justify-content: start;
    align-items: flex-start;
  }

  .ant-input {
    background-color: #fff;
    color: black;
  }
}

.titleBox {
  color: #287CFA;
  display: flex;
  align-items: center;
  justify-content: start;
  margin-bottom: 10px;

  img {
    margin-right: 5px;
  }
}

.titleLogo {
  width: 12px;
}
.antSelectDropdown{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
