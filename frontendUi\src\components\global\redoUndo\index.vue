<!--
 * @Descripttion: 
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-08 14:48:15
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-23 12:24:49
-->
<template>
  <div class="redo-undo">
    <a-dropdown>
      <div
        class="control"
        :style="{opacity:redoStore?.doList?.undo?.length > 0?'1':'0.5'}"
      >
        <icon-font
          type="icon-chexiao"
          class="icon"
          @click="redo.revoke()"
        ></icon-font>
        <!-- <CaretDownOutlined /> -->
      </div>
      <template #overlay>
        <a-menu v-if="redoStore?.doList?.undo?.length">
          <a-menu-item
            v-for="item in redoStore?.doList?.undo"
            @click="redo.revoke(item.sequenceNbr)"
            :key="item"
          >
            <span>{{ item.name }}</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-dropdown>
      <div
        class="control"
        :style="{opacity:redoStore?.doList?.redo?.length > 0?'1':'0.5'}"
      >
        <icon-font
          type="icon-qianjin"
          class="icon"
          @click="redo.recovery()"
        ></icon-font>
        <!-- <CaretDownOutlined /> -->
      </div>
      <template #overlay>
        <a-menu v-if="redoStore?.doList?.redo?.length">
          <a-menu-item
            v-for="item in redoStore?.doList?.redo"
            @click="redo.recovery(item.sequenceNbr)"
            :key="item"
          >
            <span>{{ item.name }}</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>
<script setup>
import {
  RedoOutlined,
  UndoOutlined,
  CaretDownOutlined,
} from '@ant-design/icons-vue';
import { proRedo } from '@/store/proRedo';
import redo from '@/hooks/redo';

const redoStore = proRedo();
console.log('redoStore');
</script>
<style lang="scss" scoped>
.redo-undo {
  display: flex;
  .control {
    width: 30px;
    color: white;
    display: inline-block;
    padding: 0 4px;
    .anticon-caret-down {
      font-size: 10px;
    }
  }
}
</style>