<!--
 * @@Descripttion: 计算预制混凝土泵送增加费
 * @Author: wangru
 * @Date: 2023-08-04 15:56:53
 * @LastEditors: wangru
 * @LastEditTime: 2025-02-08 16:44:43
-->
<template>
  <div>
    <div
      class="content"
      v-if="currentInfo"
    >
      <div class="way-select">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-anzhuangfeiyongshuchufangshi"
          ></icon-font>泵送方式
        </div>
        <a-radio-group
          v-model:value="currentInfo.pumpingType"
          class="radioList"
          @change="radioChange"
        >
          <a-radio :value="2">泵车</a-radio>
          <a-radio :value="1">输送泵</a-radio>
        </a-radio-group>
      </div>
      <div class="type-list">
        <span class="title">输送泵地上檐高类别</span>
        <a-select
          v-model:value="currentInfo.eavesHeight"
          style="width: 280px; margin-right: 10px"
          :options="currentInfo.eavesHeightList"
          :field-names="{ label: 'deName', value: 'sequenceNbr' }"
          :disabled="currentInfo.pumpingType === 2"
          placeholder="请选择地上最高檐口高度/层数"
        ></a-select>
      </div>
      <div class="way-select">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-anzhuangfeiyongshuchufangshi"
          ></icon-font>计算规则
        </div>
        <div class="checkboxList">
          <vxe-checkbox
            v-model="currentInfo.hntCalculationFlag"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="2"
          >预拌混凝土参与计算</vxe-checkbox>
          <vxe-checkbox
            v-model="currentInfo.xjhntCalculationFlag"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="2"
          >现浇混凝土参与计算</vxe-checkbox>
          <vxe-checkbox
            v-if="currentInfo.pumpingType === 1"
            v-model="currentInfo.dsCalculationFlag"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="2"
          >地上参与泵送费计算</vxe-checkbox>
          <vxe-checkbox
            v-if="currentInfo.pumpingType === 1"
            v-model="currentInfo.dxCalculationFlag"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="2"
          >地下参与泵送费计算</vxe-checkbox>
        </div>
      </div>
    </div>
    <div class="tableNo3">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, height: 30 }"
        :data="currentInfo.baseDeList"
        height="300"
        width="450"
        ref="tableNo3"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          showIcon: false,
          expandAll: true,
        }"
        :row-class-name="rowClassName"
        show-overflow
        keep-source
      >
        <vxe-column
          field="bdCode"
          width="30%"
          title="编码"
          tree-node
        >
        </vxe-column>
        <vxe-column
          field="type"
          width="10%"
          title="类别"
        > </vxe-column>
        <vxe-column
          field="name"
          title="名称"
        > </vxe-column>
        <vxe-column
          field="isCheck"
          width="20%"
          title="地上/地下"
          v-if="currentInfo.pumpingType === 1"
        >
          <template #default="{ row }">
            <vxe-select
              v-if="row.kind === '04'"
              v-model="row.upFloor"
              transfer
              @change="selectChange(row, $event)"
            >
              <vxe-option
                v-for="item in floorList"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <p
      class="btns"
      style="width: 200px !important"
    >
      <a-button
        type="primary"
        @click="emits('close')"
        ghost
      >取消</a-button>
      <a-button
        type="primary"
        @click="calculationPumpingAddFee()"
        :loading="submitLoading"
      >确定</a-button>
    </p>
  </div>
</template>
<script setup>
import {
  onMounted,
  reactive,
  ref,
  watch,
  toRaw,
  watchEffect,
  nextTick,
} from 'vue';
import { pureNumber } from '@/utils/index';

import api from '../../../../api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode.js';
import { message } from 'ant-design-vue';
import { onClickOutside } from '@vueuse/core';
const store = projectDetailStore();
const target = ref(null); //点击输入框之外的地方可进行查询
const emits = defineEmits(['updateData', 'close']);
let deBookValue = ref(); //默认安装工程下拉框选中值
let chapterValue = ref(); //默认章节下拉框下拉框选中值
let currentRow = ref(); //上表格选中行
let infoVisible = ref(false); // 提示信息框是否显示
let infoText = ref(''); // 提示信息框的展示文本
let iconType = ref(''); // 提示信息框的图标
let isSureModal = ref(false); // 提示信息框是否为确认提示框
let isEdit = ref(true); //是否可以编辑分摊三种费率
let editInfo = ref(false);
let submitLoading = ref(false); //点击确定按钮loading
const upTable = ref();
let mergeCells = ref(null);
let originalHeight = ref(null); // 接口返回檐高类别
let eavesHeightList = ref([]); // 檐高类别列表
let currentInfo = ref({
  pumpingType: 1, // 泵送方式：1 泵车  2 输送泵
  eavesHeight: null, // 输送泵地上檐高类别选择的选项
  eavesHeightList: [], // 输送泵地上檐高类别所有选项
  hntCalculationFlag: 1, // 预拌混凝土是否计算泵送增加费 1：预拌混凝土计算泵送增加费  2：预拌混凝土不计算泵送增加费
  baseDeList: [],
}); // 当前页面信息
const floorList = ref([
  {
    label: '地上',
    value: 1,
  },
  {
    label: '地下',
    value: 2,
  },
]);
onMounted(() => {
  getPumpingAdditionalFeeViewData();
});

const radioChange = () => {
  if (currentInfo.value.pumpingType === 2) {
    currentInfo.value.eavesHeightList = eavesHeightList.value.filter(
      x => x.bsType === 2
    );
    currentInfo.value.eavesHeight =
      currentInfo.value.eavesHeightList[0].sequenceNbr;
  } else {
    currentInfo.value.eavesHeightList = eavesHeightList.value.filter(
      x => x.bsType === 1
    );
    currentInfo.value.eavesHeight =
      originalHeight.value || currentInfo.value.eavesHeightList[0].sequenceNbr;
  }
};

const getPumpingAdditionalFeeViewData = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  api.getPumpingAdditionalFeeViewData(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      currentInfo.value = res.result;
      if (res.result.pumpingType === 1) {
        originalHeight.value = res.result.eavesHeight;
      }
      eavesHeightList.value = JSON.parse(
        JSON.stringify(res.result.eavesHeightList)
      );
      radioChange();
    }
  });
};
const calculationPumpingAddFee = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    ...currentInfo.value,
    baseDeList: JSON.parse(JSON.stringify(currentInfo.value.baseDeList)),
    eavesHeightList: JSON.parse(
      JSON.stringify(currentInfo.value.eavesHeightList)
    ),
  };
  console.log('apiData', apiData);
  api.calculationPumpingAddFee(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200) {
      message.success('计取成功');
      emits('updateData');
    }
  });
};

const rowClassName = ({ row }) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }

  return ClassStr;
};
</script>
<style lang="scss" scoped>
.content {
  .way-select {
    border: 1px solid #d9d9d9;
    font-size: 14px;
    padding: 10px 0 15px 15px;
    margin-bottom: 12px;
    .title {
      font-size: 12px;
      color: #287cfa;
      margin-bottom: 8px;
      .icon-font {
        margin-right: 6px;
      }
    }
    .item {
      span {
        display: block;
        color: #2a2a2a;
      }
      span:nth-of-type(1) {
        font-weight: bold;
      }
    }
  }
  .type-list {
    font-size: 14px;
    margin-bottom: 12px;
    .title {
      margin-right: 7px;
    }
  }
  .table-content {
    .tableNo1 {
      width: 70%;
    }
    .tableNo2 {
      width: 98%;
      margin: 10px 0;
    }
    .detailTitle {
      margin-right: 15px;
    }
  }
}

.btns {
  width: 10%;
  display: flex;
  margin: 10px auto 0;
  justify-content: space-around;
}
.line {
  width: 100%;
  height: 7px;
  background: rgba(221, 221, 221, 0.39);
  opacity: 0.52;
  margin: 20px 0;
}
.detail {
  width: 73%;
}
:deep(.ant-input-number-input) {
  padding: 0 0px 0 3px;
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}
::v-deep .detail .ant-btn-primary {
  margin: 0 0 !important;
}
::v-deep .ant-input-group-addon {
  padding: 0 0 !important;
}
::v-deep .vxe-select > .vxe-input .vxe-input--inner {
  border: none !important;
}
::v-deep .ant-input-number-handler-wrap {
  display: none;
}
::v-deep .ant-input-number-group-addon {
  padding: 0px;
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2 !important;
}
::v-deep(.vxe-table .row-qd) {
  background: #dce6fa;
}
::v-deep(.checkboxList .vxe-checkbox--label) {
  line-height: 13px;
  font-size: 12px;
}
</style>
