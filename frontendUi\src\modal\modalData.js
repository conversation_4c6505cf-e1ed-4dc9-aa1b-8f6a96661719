/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-20 09:34:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-28 15:00:48
 */
const modalData = {
  ys: [
    {
      name: '清单定额指引',
      type: 'inventory-and-quota-index',
      path: () => import('@/modal/ys/inventoryAndQuotaIndex/index.vue'),
      style: {
        width: 1040,
        height: 500,
        minWidth: 900,
        minHeight: 350,
      },
    },
  ],
  gs: {
    common: [],
  },
  glj: {
    common: [],
  },
  yssh: {
    common: [],
  },
  js: {
    common: [],
  },
};

export default modalData;
