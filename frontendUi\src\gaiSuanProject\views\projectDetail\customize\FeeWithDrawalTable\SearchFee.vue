<!--
 * @Descripttion: 费率列表， 费用汇总列表页面也用了，双击选取值
 * @LastEditors: sunchen
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-15 15:09:55
-->
<template>
  <div class="content">
    <div class="tableAndfigs">
      <div class="searchBox">
        <span>定额库选取：</span>
        <div v-if="EntryType=='SummaryExpense'">
          <vxe-select v-model="deStandardId" style="width:230px;"  placeholder="请选择"  @change="selChange2">
            <vxe-option v-for="item in rationList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
          </vxe-select>
        </div>
        <a-select v-else v-model:value="deStandardId" :options="rationList" @change="selChange" style="width:230px;" placeholder="请选择" :size="colStyle.colSize"></a-select>
      </div>
      <div class="conBox">
        <div class="conLeft">
          <a-tree
            v-if="libraryIndex==='0'"
            :tree-data="feeJzArr"
            @select="treeClick"
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
          />
          <a-tree
          v-else
            :tree-data="feeAzArr"
            @select="treeClick"
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
          />
        </div>
        <div class="conRight">
          <vxe-table
            align="center"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            :data="tableData"
            ref="rightTable"
            max-height="90%"
            keep-source
            class="table-edit-common"
            :cell-class-name="selectedClassName"
            @cell-dblclick="useRow"
          >
            <vxe-column width="60" title="序号">
              <template #default="{ row, $rowIndex }">
                {{ $rowIndex + 1 }}
              </template>
            </vxe-column>
            <vxe-column field="name" min-width="100" title="名称"></vxe-column>
            <vxe-column field="freeRate" min-width="180" title="费率值(%)"></vxe-column>
            <vxe-column field="description" min-width="180" title="备注"></vxe-column>
            <template #empty>
              <span
                style="
                  color: #898989;
                  font-size: 14px;
                  display: block;
                  margin: 25px 0;
                "
              >
                <icon-font
                  style="font-size: 22px"
                  type="icon-zanwushuju"
                ></icon-font>
                暂无数据
              </span>
            </template>
          </vxe-table>
        </div>
      </div>
    </div>
    <!-- <div style="text-align: center;margin-top: 10px;">
      <a-button style="margin-right: 15px;" @click="cancel">关闭</a-button>
    </div> -->
  </div>
</template>
<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue';
import csProject from '@gaiSuan/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import { ipcApiRoute } from '@gaiSuan/api/main';
import { feeJzArr,feeAzArr } from './feeJson';
import { useCellClick } from '@gaiSuan/hooks/useCellClick';


// SummaryExpense 费用汇总
const props = defineProps({
  EntryType: {
    type: String,
    default: '',
  },
});

const { selectedClassName } =
  useCellClick();
const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const emits = defineEmits(['close']);
const $ipc = globalProperties.$ipc;
const store = projectDetailStore();
const deStandardId = ref('')
const libraryCode = ref('')
const libraryIndex = ref('0')
const rationList = ref([])
const tableData = ref([])
const selTree = ref(['manageFeeRate'])
const expandedKeys = ref(['manageFeeRate'])
const selectedKeys = ref(['manageFeeRate'])
const colStyle = reactive({
  colSize: null,
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
});
onMounted(() => {
  queryRationList()
});
// 获取表格信息
const getTableList=()=>{
  let apiData={
    type:store.currentTreeInfo.type,
    constructId:store.currentTreeGroupInfo?.constructId,
    unitId:store.currentTreeInfo.id,
    libraryCode:deStandardId.value,
    freeRateType:selectedKeys.value[0]
  }
  console.info(apiData)
  csProject.getFreeRateInfo(apiData).then(res => {
    console.info(res)
    if(res.status!==200){
      message.error(res.message);
      return false;
    }
    tableData.value=res.result
  });
}
//获取定额标准下拉列表
const queryRationList = () => {
  const postData = {
    provenceCode: 130000,
    cityCode: 130100,
  };
  $ipc
    .invoke(ipcApiRoute.getDeLibraryByDirection, postData)
    .then(function (result) {
      if (result.status === 200) {
        let rationArr=[]
        let arr=result.result
        for(let i in arr) {
          rationArr.push({
            value: result.result[i].libraryCode,
            label: result.result[i].libraryName,
            index:i
          });
        }
        nextTick(() => {
          rationList.value = rationArr;
          console.log("🚀 ~ nextTick ~  rationList.value :",  rationList.value )
          deStandardId.value = rationArr[0].value;
          getTableList()
        });
      }
    });
};
// 切换定额库
const selChange = (val,e) => {
  libraryIndex.value=e.index
  selectedKeys.value=['manageFeeRate']
  getTableList()
}

const selChange2 = (e) => {
  libraryIndex.value= rationList.value.find(i=> i.value == e.value)?.index
  selectedKeys.value=['manageFeeRate']
  getTableList()
}

// 点击左侧树
const treeClick = (val,e) => {
  if(val.length===0){
    selectedKeys.value=selTree.value
    return false;
  }
  selTree.value=val
  getTableList()
}
const cancel=()=>{
  emits('close', false);
}


const useRow = ({row}) => {
  emits('onUse',row?.freeRate)
}

</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;

  .tableAndfigs {
    height: calc(100% - 32px);
  }
}

.searchBox {
  width: 100%;
  display: flex;
  align-items: center;
}

.conBox{
  width:100%;
  display:flex;
  justify-content: space-between;
  margin-top:15px;
  height:500px;
  .conLeft{
    width:200px;
    border:1px solid #B9B9B9;
  }
  .conRight{
    width:calc(100% - 210px);
    border:1px solid #B9B9B9;
  }
}

.select-wraps{
  width: 364px;
  background: rgba(255,255,255,0.39);
  border: 1px solid rgba(0,0,0,0.149);
}
</style>
