const {Service} = require('../../../core');
const Log = require('../../../core/log');
const {BaseFeeFileRelation} = require("../../model/BaseFeeFileRelation");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {NumberUtil} = require("../../utils/NumberUtil");
const UnitConversion = require("../../enum/UnitConversion");
const {SqlUtils} = require("../../utils/SqlUtils");
const _ = require("lodash");
const ConstantUtil = require('../../enum/ConstantUtil');
const InsertStrategy = require('../../main_editor/insert/insertStrategy');
const RemoveStrategy = require('../../main_editor/remove/removeStrategy');
const ReplaceStrategy = require('../../main_editor/replace/replaceStrategy');
const RcjDeleteStrategy = require("../../rcj_handle/remove/removeRcjStrategy");
const CalculationTool = require("../../unit_price_composition/compute/CalculationTool");
const {ResponseData} = require("../../utils/ResponseData");
const OptionMenuHandler = require("../../main_editor/optionMenuHandler");
const {UPCContext} = require("../../unit_price_composition/core/UPCContext");
const {Snowflake} = require("../../utils/Snowflake");
const EE = require("../../../core/ee");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const BranchProjectOptionMenuConstant = require("../../enum/BranchProjectOptionMenuConstant");
const BranchProjectDisplayConstant = require("../../enum/BranchProjectDisplayConstant");
const {UnitRcjCacheUtil} = require("../../rcj_handle/cache/UnitRcjCacheUtil");

class ItemBillProjectOptionService extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseBranchProjectOptionService = this.service.baseBranchProjectOptionService;
        this._unitPriceServices = this.service.unitPriceService;

    }

    baseQdDeProcess = this.service.baseQdDeProcess;
    listFeatureProcess = this.service.listFeatureProcess;
    baseFeeFileService = this.service.baseFeeFileService;
    unitPriceServices = this.service.unitPriceService;
    itemBillProjectProcess = this.service.itemBillProjectProcess;
    rcjProcess = this.service.rcjProcess;
    quantitiesService = this.service.quantitiesService;

    stepItemCostService = this.service.stepItemCostService;

    disPlayType = {
        "0": " ",
        "01": "部",
        "02": "部",
        "03": "清",
        "04": "定"
    }

    lockQd(constructId, singleId, unitId, qdId) {
        let allDatas = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.lockLine(allDatas, qdId);
        return true;
    }

    unLockQd(constructId, singleId, unitId, qdId) {
        let allDatas = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        this._baseBranchProjectOptionService.unLockLine(allDatas, qdId);
        return true;
    }

    async insertLine(constructId, singleId, unitWorkId, pointLine, newLine, rootLineId) {
        if(pointLine.kind==BranchProjectLevelConstant.qd && newLine.kind==BranchProjectLevelConstant.fb){
            let fb = PricingFileFindUtils.getDeById(constructId, singleId, unitWorkId,pointLine.parentId);
            if(fb.kind==BranchProjectLevelConstant.zfb){
                newLine.kind=BranchProjectLevelConstant.zfb;
            }
        }
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId: unitWorkId, pageType: "fbfx"});
        let line = await insertStrategy.execute({pointLine: pointLine, newLine: newLine, option: "save"});
        //获取parentNode
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        OptionMenuHandler.fillUpDown(fbFx.getNodeById(line.parentId));
        return {
            "data": {...line, parent: null, children: null, prev: null, next: null},
            "index": line.index,
        };
    }

    /**
     * 新增行
     * @return {data, index: number}
     */
    insertLineOld(constructId, singleId, unitWorkId, pointLine, newLine, rootLineId) {
        let fbfxRes = this._baseBranchProjectOptionService.insertLine(
            pointLine, newLine, null,
            PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId), rootLineId);
        PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).itemBillProjects = fbfxRes.allDatas;
        // 分部 子分部 重新计算单价构成
        let newData = fbfxRes.newData;
        if (newData.kind === BranchProjectLevelConstant.fb || newData.kind === BranchProjectLevelConstant.zfb) {
            this.unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, newLine.sequenceNbr, true, fbfxRes.allDatas);
        }
        // 新数据挂工程量明细 如果新增的行里有的话直接使用
        if (!_.isEmpty(newLine.quantities)) {
            this.quantitiesService.updateDatas(newLine.quantities, newData);
        } else {
            this.quantitiesService.initDatas(newData);
        }

        // 处理清单编码
        if (pointLine.kind === BranchProjectLevelConstant.qd) {
            let qdCode = pointLine.fxCode;
            if (qdCode && qdCode.length === 9) {
                if (qdCode && qdCode !== "") {
                    qdCode = this._baseBranchProjectOptionService.getQdCode(constructId, singleId, unitWorkId, qdCode);
                    pointLine.fxCode = qdCode;
                }
            }
        }
        let newData1 = fbfxRes.newData;
        //如果是临时删除数据  主要是处理粘贴来的数据
        if (!newData1.tempDeleteFlag) {
            //处理临时锁定数据  新增的是定额需要处理
            this.addDeTempDel(constructId, singleId, unitWorkId, fbfxRes.newData);
        }


        return {
            "data": fbfxRes.newData,
            "index": fbfxRes.displayIndex - 1,
        };
    }

    addDeTempDel(constructId, singleId, unitWorkId, de) {
        //处理临时锁定数据  新增的是定额需要处理
        if (de.kind == "04") {
            let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
            //获取父级清单
            let itemBillProject = allData.filter(qd => qd.sequenceNbr == de.parentId)[0];
            if (itemBillProject.tempDeleteFlag) {
                de.tempDeleteFlag = true;
                //处理定额数据
                this.setDeOrQdTempDelStatust(constructId, singleId, unitWorkId, allData, de, true, unit);
            }
        }
    }


    /**
     * 从列表中进行更新
     */
    async updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo) {
        return await this._baseBranchProjectOptionService.updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo, "fbfx");
    }

    updateQdFeature(constructId, singleId, unitId, pointLine, updateStr) {
        pointLine = this._findLine(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes(), pointLine.sequenceNbr);
        this.service.baseBranchProjectOptionService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }

    async replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, unit, kind, type1, libraryCode) {
        let replaceStrategy = new ReplaceStrategy({constructId, singleId, unitId: unitWorkId, pageType: "fbfx"})
        let newNode = await replaceStrategy.execute({selectId, replaceId, unit, kind, libraryCode});
        let row = _.cloneDeep(newNode)
        delete row.parent;
        delete row.children;
        delete row.prev;
        delete row.next;
        return row;
    }

    /**
     * 索引界面点击 替换
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param selectId 索引
     * @param replaceId 选中行
     * @param type      type 接口位置（1编辑区 2明细区）
     * @param conversionCoefficientk 人材机转换系数 只有替换人材机时候才有
     * @param kind      替换类型  03清单 04定额
     * @return {Promise<void>}
     */
    async replaceFromIndexPageOld(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, unit, kind, type1, libraryCode) {
        let allData;
        let deLine;
        let is2022 = libraryCode.startsWith(ConstantUtil.YEAR_2022);
        // 替换 清单 定额
        if (type === 1) { // 操作区
            let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
            let pointLine = this.getLine(allData, replaceId);
            if (!pointLine.rcjFlag || pointLine.rcjFlag != 1) {
                // 操作区 清单 定额 替换
                let res = await this._baseBranchProjectOptionService.replaceFronIndexPage(allData, constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, {}, null, type1, is2022);
                //如果替换的是清单
                if (kind === BranchProjectLevelConstant.qd) {
                    res.tempDeleteFlag = false;
                    let unit1 = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
                    //重新处理清单下挂定额数据
                    this.setQdTempDelStatust(constructId, singleId, unitWorkId, allData, res, false, unit1);
                }
                return res;
            } else {
                // 替换操作区人材机
                let res = await this._baseBranchProjectOptionService.upDateRcjLine(PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId), replaceId,
                    constructId, singleId, unitWorkId, selectId, is2022);
                return res;
            }
        } else { // 替换明细区人材机
            if (this._isrcj(constructId, singleId, unitWorkId, replaceId)) {
                let {deLine, rcj} = await this.rcjProcess.upDateRcj(PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId), constructId, singleId, unitWorkId, selectId, replaceId, conversionCoefficient);
                rcj.initResQty = rcj.resQty;
                rcj.taxRateInit = rcj.taxRate;
                // 根据人材机变化，修改状态 换 定
                let baseRcjs = this.rcjProcess.getBaseRcjInfoByDeId(deLine.standardId);
                let rcjs = this.rcjProcess.queryRcjDataByDeId(deLine.sequenceNbr, constructId, singleId, unitWorkId);
                if (!deLine.appendType) {
                    deLine.appendType = [];
                }
                deLine.appendType = deLine.appendType.filter(a => a !== "换");
                if (!this.rcjProcess.isSameRcj(baseRcjs, rcjs)) {
                    deLine.appendType.push("换");
                }

                return rcj.sequenceNbr;
            } else {
                let phbs = PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitWorkId);
                let currentPhb = phbs.filter(f => f.sequenceNbr === replaceId)[0];
                let baseRcjSql;
                if (is2022) {
                    baseRcjSql = "select * from base_rcj_2022 where sequence_nbr = ?";
                } else {

                    baseRcjSql = "select * from base_rcj where sequence_nbr = ?";
                }
                let sqlRes = this.app.betterSqlite3DataSource.prepare(baseRcjSql).get(selectId);
                let convertRes = SqlUtils.convertToModel([sqlRes])[0];

                for (let attr in convertRes) {
                    if (attr === "sequenceNbr") continue;
                    currentPhb[attr] = convertRes[attr];
                }

                return currentPhb.sequenceNbr;
            }
        }
    }

    _isrcj(constructId, singleId, unitWorkId, selectId) {
        let rcjs = PricingFileFindUtils.getRcjList(constructId, singleId, unitWorkId);
        let find = rcjs.filter(f => f.sequenceNbr === selectId);
        return find && find.length > 0;
    }

    getLine(allData, selectId) {
        return allData.getNodeById(selectId);
        /*for (let i = 0; i < allData.length; ++i) {
            if (selectId === allData[i].sequenceNbr) {
                return allData[i];
            }
        }*/
    }

    /**
     * 从索引界面替换-完整版，包含替换后触发的事件
     * @param args 详见itemBillProjectController.replaceFromIndexPage
     */
    async replaceFromIndexPageFullEdition(args) {
        let {constructId, singleId, unitId, unitWorkId, selectId, replaceId, type, conversionCoefficient, unit, kind, libraryCode} = args;
        if (ObjectUtils.isEmpty(unitId)) {
            unitId = unitWorkId;
        }
        //true 标记前端 传来修改
        let res = await this.replaceFromIndexPage(constructId, singleId, unitId, selectId, replaceId, type, conversionCoefficient, unit, kind, false, libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return res;
    }

    /**
     * 批量删
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param sequenceNbrs
     */
    async batchDelete(constructId, singleId, unitWorkId, sequenceNbrs) {
        let self = this;
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        let lines = allData.filter((item) => _.includes(sequenceNbrs, item.sequenceNbr));
        let removeStrategy = new RemoveStrategy({constructId, singleId, unitId: unitWorkId, pageType: "fbfx"})
        let pset = new Set();
        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            if (allData.hasNodeById(line.sequenceNbr)) {
                let parent = await removeStrategy.execute({
                    pointLine: line,
                    isBlock: line.kind !== BranchProjectLevelConstant.de
                });
                pset.add(parent.sequenceNbr);
            }
        }
        //删除所有的子定额
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        for(const id of sequenceNbrs){
            let itemBillProjects = fbFx.filter(de=>de.parentDeId==id);
            if(ObjectUtils.isNotEmpty(itemBillProjects)){
                for (let i = 0; i < itemBillProjects.length; i++) {
                    let cline = itemBillProjects[i];
                    if (allData.hasNodeById(cline.sequenceNbr)) {
                        let parent = await removeStrategy.execute({
                            pointLine: cline,
                            isBlock: cline.kind !== BranchProjectLevelConstant.de
                        });
                        pset.add(parent.sequenceNbr);
                    }
                }
            }
        }
        let arr = [...pset];
        let calculationTool = new CalculationTool({constructId, singleId, unitId: unitWorkId, allData: allData});
        arr.forEach(id => {
            if (allData.hasNodeById(id)) {
                calculationTool.calculationChian({sequenceNbr: id})
            }
        })
        return true;
    }

    async fillDataFromIndexPage(constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rootLineId, rcjFlag, bzhs, type, libraryCode, isInvokeCountCostCodePrice) {
        let newLine = {"kind": createType};
        let insertStrategy = new InsertStrategy({constructId, singleId, unitId: unitWorkId, pageType: "fbfx"});
        let line = await insertStrategy.execute({
            pointLine,
            indexId,
            newLine,
            libraryCode,
            rcjFlag,
            unit,
            option: "insert"
        });
        return {
            "data": {...line, parent: null, children: null, prev: null, next: null},
            "index": line.index,
        };
    }

    /**
     * 从索引界面添加数据
     * @param isInvokeCountCostCodePrice  是否调用费用汇总计算的方法
     */
    async fillDataFromIndexPageOld(constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rootLineId, rcjFlag, bzhs, type, libraryCode, isInvokeCountCostCodePrice) {
        let res;
        // 1. 如果是新增情况，先新增数据
        if (pointLine.kind !== createType || pointLine.bdCode /*|| (rcjFlag && pointLine.rcjFlag !== rcjFlag)*/) {
            let fbfxRes = this._baseBranchProjectOptionService.insertLine(
                pointLine, {"kind": createType}, null,
                PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId),
                rootLineId, bzhs);
            this.quantitiesService.initDatas(fbfxRes.newData);
            PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).itemBillProjects = fbfxRes.allDatas;
            pointLine = fbfxRes.newData;
            pointLine.rcjFlag = rcjFlag;
            res = {
                "data": fbfxRes.newData,
                "index": fbfxRes.displayIndex
            }
        } else {
            // 如果是前端传入数据，将pointLine 转变为内存数据
            let webIndex = pointLine.index;
            pointLine = this._findLine(PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId), pointLine.sequenceNbr);
            pointLine.index = webIndex;
            pointLine.rcjFlag = rcjFlag;
        }
        // 2. 修改数据
        try {
            // 报错影响后续执行 先catch
            await this._baseBranchProjectOptionService.updateFromIndexPage(PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId),
                constructId, singleId, unitWorkId, pointLine,
                {
                    "indexId": indexId,
                    "is2022": libraryCode.startsWith(ConstantUtil.YEAR_2022),
                    "unit": unit
                }, null, null, type, isInvokeCountCostCodePrice);
        } catch (e) {
            console.error(e);
        }
        if (!res) { // 如果是修改 返回前端当前行
            res = {
                "data": pointLine,
                "index": pointLine.index
            }
        }

        this.service.conversionDeService.initDef(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
        //处理定额临时状态数据
        this.addDeTempDel(constructId, singleId, unitWorkId, res.data);
        return res
    }

    /**
     * 从索引界面添加数据-完整版，包含插入数据后触发的事件
     * @param params 详见itemBillProjectController.fillDataFromIndexPage的args
     */
    async fillDataFromIndexPageFullEdition(params) {
        // NOTE: 以下代码从itemBillProjectController.fillDataFromIndexPage 复制过来。触发事件应放在service。

        let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag, libraryCode, isInvokeCountCostCodePrice} = params;

        // 原从索引界面添加数据结构
        let res = await this.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag, null, true, libraryCode);

        // 添加数据后触发的事件
        this.service.management.trigger("controllerItemChange");
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return res;
    }

    /**
     * 复制
     * @param sequenceNbrs
     */
    copyLine(sequenceNbrs) {
        this._baseBranchProjectOptionService.copyLine(sequenceNbrs, "fbfx");
        PricingFileFindUtils.setProjectBeforeOption();
        return true;
    }

    cut(sequenceNbrs) {
        this._baseBranchProjectOptionService.copyLine(sequenceNbrs, "fbfx");
        PricingFileFindUtils.setProjectBeforeOption(false);
        return true;
    }


    /**
     * 粘贴
     *
     * 1.遍历复制的结构，pointLine为前端传入选中行，然后根据复制的结构，一条条增加结构
     * 2.利用for in 语句 将复制的数据的属性填充进入每一行（编号行，seqNum行不动）
     * 3.将
     *      人材机，    项目下 rcjDetailList 拿到，接着，过滤当前复制的de的对应人材机 然后改 rcjDetailList 的deId
     *      人材机的配合比， 项目下 rcjDetailList 同上 ，但是对应的不是定额 是人材机
     *      换算信息数据, 拿到原来项目的  项目下 这个属性 conversionInfoList  做法同人材机 改 deId  conversionInfo.deId = 新行的.sequenceNbr;
     *      材机换算规则数据  项目下 rcjRules , 做法同上 deId  但是内存存的是个 unit.rcjRules = {}; // Map: {ruleId : [...rule]} 这里注意一下
     *      清单的特征及内容  项目下拿 listFeatureList 改 qdId,
     *      工程量明细    pointLine.quantities; 清单下 自带这个属性，理论上insertLine时候直接过来的 如果没有 看一下 清单行的 quantities
     *      以上内容 修改关联的deid然后存入新项目
     * 4.从新计算单价构成
     */
    /**
     * 粘贴
     * @param pointLine 前端鼠标点击的行
     * @param menuType
     */
    async pasteLine(constructId, singleId, unitId, pointLine) {

        let copeUnitProject = PricingFileFindUtils.getProjectBuffer();
        let data = await this.addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine);
        //如果是剪切的情况下需要把之前的 删除
        if(!PricingFileFindUtils.getProjectBeforeOption()){
            let {
                itemBillProjects,
            } = copeUnitProject;
           let ids =  itemBillProjects.map(item=>item.sequenceNbr)
            await this.batchDelete(copeUnitProject.constructId, copeUnitProject.spId, copeUnitProject.sequenceNbr,ids);
        }
        return data;
    }


    /**
     * 复制主材设备
     * @param sequenceNbrs
     */
    copyLinezcsb(sequenceNbrs) {
        this._baseBranchProjectOptionService.copyLineZcsb(sequenceNbrs);
        return true;
    }


    /**
     * 粘贴主材设备
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {Promise<boolean>}
     */
    async pasteLineZcsb(constructId, singleId, unitId, pointLine, type) {

        let constructProjectRcjs = PricingFileFindUtils.getProjectBuffer();
        if (!Array.isArray(constructProjectRcjs)) {
            return "粘贴的参数含有定额获取其他数据";
        }
        return await this.addDataByDe(constructProjectRcjs, constructId, singleId, unitId, pointLine, type);

    }

    /**
     * 给主材设备添加到所选定额下
     * @param constructProjectRcjs
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {Promise<void>}
     */
    async addDataByDe(constructProjectRcjs, constructId, singleId, unitId, pointLine, type) {
        let newConstructProjectRcjs = ConvertUtil.deepCopy(constructProjectRcjs);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        for (const rcj of newConstructProjectRcjs) {
            let nextId = Snowflake.nextId();
            rcj.sequenceNbr = nextId;
            rcj.deId = pointLine.sequenceNbr;
            //判断当前单位是否有
            await this.service.rcjProcess.rcjUseUnitRcj(constructId, singleId, unitId, rcj);
            UnitRcjCacheUtil.add(unit,rcj,rcj.deId);
        }
        //获取所有人材机

        unit.constructProjectRcjs.push(...newConstructProjectRcjs);
        let allData;

        if (type === "csxm") {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        } else {
            allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        }

        //计算定额的单价构成
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.sequenceNbr, true, allData);
        //处理定额换标识
        let de;
        de = allData.getAllNodes().find(d => d.sequenceNbr == pointLine.sequenceNbr);
        if (de.appendType != null && !de.appendType.includes("换")) {
            de.appendType.push("换");
        }
        //计算费用
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        //重新计算费用汇总
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
        return true;
    }

    _getMaxQdCodeNum(baseCode, constructId, singleId, unitId) {
        //这里的 itemBillProjects 和measureProjectTables都是树形结构
        let {
            itemBillProjects,
            measureProjectTables
        } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let codeList = [];
        let matchListA = itemBillProjects.getAllNodes().filter(item => item.bdCode && item.bdCode.startsWith(baseCode));
        let max = null;
        //条件获取到相对应的清单集合
        //根据清单编码排序
        if (matchListA && matchListA.length > 0) {
            max = _.maxBy(matchListA, function (o) {
                return o.bdCode;
            }).bdCode;
        }
        let matchListB = measureProjectTables.getAllNodes().filter(item => item.fxCode && item.fxCode.startsWith(baseCode));
        if (matchListB && matchListB.length > 0) {
            let b = _.maxBy(matchListB, function (o) {
                return o.fxCode;
            }).fxCode;
            if (max && max < b) max = b;
            if (!max) max = b;
        }
        if (!max) return 0;
        return max.length > 9 ? Number.parseInt(max.substring(9)) : 0;
    }



    /**
     * 抽取公共方法（粘贴、复用组价公用）
     * @param copeUnitProject
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {Promise<boolean>}
     */
    async addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine, resultMap = {}) {
        //判断当前有复制过如果没有则直接返回
        if (_.isEmpty(copeUnitProject)) return false;
        //获取分部分项数据
        let {
            feeFiles,
            itemBillProjects,
            listFeatureList,
            constructProjectRcjs,
            rcjDetailList,
            rcjRules,
            sequenceNbr
        } = copeUnitProject;
        let sourceUnitId=sequenceNbr;
        if (_.isEmpty(itemBillProjects)) return false;
        let {feeFiles: oldFeefles, mainDeLibrary} = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //取费文件计算使用
        let diffFeeFiles = [];
        _.forEach(feeFiles, (item) => {
            item.defaultFeeFlag = null;
            let isOk = false;
            for (let i = 0; i < oldFeefles.length; i++) {
                let oItem = oldFeefles[i];
                if (item.feeFileCode == oItem.feeFileCode) {
                    isOk = true;
                }
            }
            if (!isOk) {
                diffFeeFiles.push({...item});
            }
        });
        if (!_.isEmpty(diffFeeFiles)) {
            PricingFileFindUtils.getUnit(constructId, singleId, unitId).feeFiles = oldFeefles.concat(diffFeeFiles);
        }
        //分组
        let groupItemBillProjects = _.groupBy(itemBillProjects, (item) => item.kind);
        //清单列表
        let qdlist = groupItemBillProjects[BranchProjectLevelConstant.qd];
        //定额列表
        let delist = groupItemBillProjects[BranchProjectLevelConstant.de];
        if (ObjectUtils.isEmpty(qdlist) && delist.length > 0) {
            // 此处处理临时bug  如果粘贴的只有定额  那么所选目标只能是清单和定额
            if (pointLine.kind != BranchProjectLevelConstant.qd&&pointLine.kind != BranchProjectLevelConstant.de) {
                throw new Error('定额只能粘贴到清单级别');
            }
        }
        let qdKeyMap = new Map();
        if (!_.isEmpty(qdlist)) {
            //qdlist = _.reverse(qdlist);
            for (let i = 0; i < qdlist.length; i++) {
            // for (let i = qdlist.length - 1; i >= 0; i--) {
                let item = qdlist[i];
                let fxCode = item.fxCode;
                if (fxCode&&item.fxCode.length > 9) {
                    let baseCode = item.fxCode.substring(0, 9);
                    let maxCodeNum = this._getMaxQdCodeNum(item.fxCode.substring(0, 9), constructId, singleId, unitId);
                    let newCode = maxCodeNum + 1;
                    fxCode = baseCode + _.padStart(newCode, 3, '0');
                }
                let newLine = {...item, sequenceNbr: "", fxCode, bdCode: fxCode};
                if (_.isEmpty(delist)) {
                    //清空金额数据
                    newLine = {
                        ...item,
                        fxCode,
                        bdCode: fxCode,
                        sequenceNbr: "",
                        zjfPrice: 0,
                        zjfTotal: 0,
                        total: 0,
                        price: 0,
                        rfee: 0,
                        totalRfee: 0,
                        cfee: 0,
                        totalCfee: 0,
                        jfee: 0,
                        totalJfee: 0,
                        managerFee: 0,
                        totalManagerFee: 0,
                        profitFee: 0,
                        totalProfitFee: 0,
                        zcfee: 0,
                        totalZcfee: 0
                    };
                }

                let key = item.sequenceNbr + "";
                //添加清单
                let {data} = await this.insertLine(constructId, singleId, unitId, pointLine, newLine);
                qdKeyMap.set(key, data);
                try {
                    resultMap[data.sequenceNbr] = data;
                } catch (e) {
                }
                //更新清单特征集合
                let itemRcjDetailList = _.filter(_.cloneDeep(listFeatureList), (i) => i.qdId == key);
                for (let i = 0; i < itemRcjDetailList.length; i++) {
                    itemRcjDetailList[i].qdId = data.sequenceNbr;
                }
                let listFeatureListold = PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList;
                if (_.isEmpty(listFeatureListold)) {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = itemRcjDetailList;
                } else {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = listFeatureListold.concat(itemRcjDetailList);
                }
            }
        }
        if (!_.isEmpty(delist)) {
            //拿到人材机明细
            let before = null;//处理顺序
            let beforeItem = null;
            let beforeQdItem = null;
            //delist = _.reverse(delist);
            let fromFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + copeUnitProject.sequenceNbr);

            let fromIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, copeUnitProject.sequenceNbr);
            //单价构成下拉列表
            let tagterFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + unitId);
            //单价构成模板
            let tagterIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, unitId);
            for (let i = 0; i < delist.length; i++) {
                let item = delist[i];
                let newLine = {...item, sequenceNbr: ""};
                let key = item.sequenceNbr + "";
                let qdItem = qdKeyMap.get(item.parentId);

                // let appendType = _.filter(item.appendType, (str) => "借" != str);
                // if (item.libraryCode && item.libraryCode != mainDeLibrary) {
                //     appendType.push("借");
                // }
                // newLine.appendType = appendType;
                //处理如果是费用定额的话，需要将计算基数置为0
                if (!ObjectUtils.isEmpty(newLine.isCostDe) && newLine.isCostDe !== 0) {
                    //赋值计算基数
                    newLine.formula = 0;
                    newLine.caculatePrice = 1;
                    newLine.baseNum = {def: 0};
                    newLine.isAutoCost = false;
                }
                //如果清单不存在
                let res = {};
                //处理复制粘贴时候 HSGCL 处理工程量表达式是数字0的情况 数字是没有includes 这个方法的
                item.quantityExpression = item.quantityExpression + "";
                if (item.quantityExpression.includes('QDL') && !_.isEmpty(beforeItem) && beforeItem.sequenceNbr == item.createDeId) {
                    newLine.createDeId = before.sequenceNbr;
                }
                
                //清单不存在的情况下 当前行就是传进来的行
                if (_.isEmpty(qdItem)) {
                    
                    res = await this.insertLine(constructId, singleId, unitId, (before != null ? before : pointLine), newLine);
                } else {
                    res = await this.insertLine(constructId, singleId, unitId, (beforeQdItem == qdItem ? (before != null ? before : qdItem) : qdItem), newLine);
                } 
                
                try {
                    resultMap[res.data.sequenceNbr] = res.data;
                } catch (e) {
                }
                before = res.data;
                beforeItem = item;
                beforeQdItem = qdItem;
                if (!res.data.bdCode) {
                    continue;
                }
                //处理单价构成模板
                if (res.data.qfCode && res.data.qfCode.includes("_")) {

                    let t = _.cloneDeep(fromFeeFileMap.get(res.data.qfCode));
                    //如果目标单位里不存在 则添加
                    if (!tagterFeeFileMap.has(res.data.qfCode)) {
                        UPCContext.qfCodeMap.set(constructId + "," + unitId + res.data.qfCode, res.data.costFileCode);
                        tagterFeeFileMap.set(res.data.qfCode, t);
                        let t1 = _.cloneDeep(fromIncrementTemplateListMap.get(res.data.qfCode));
                        tagterIncrementTemplateListMap.set(res.data.qfCode, t1);
                    }
                }
                let isSzgcWxyhCopy = await this.service.unitProjectService.deLibraryIsSzgcWxyh(copeUnitProject.constructId,copeUnitProject,item);
                //目标判断
                    let isSzgcWxyh = await this.service.unitProjectService.deLibraryIsSzgcWxyh(constructId,PricingFileFindUtils.getUnit(constructId, singleId, unitId),res.data);
                 
                //处理人材机
                if (constructProjectRcjs && !_.isEmpty(constructProjectRcjs)) {
                    // 复制的是不是中修定额
                
                    //获取人材机
                    let itemConstructProjectRcjs = _.filter(constructProjectRcjs, (i) => i.deId == key) || [];
                    //获取人材机明细
                    let itemRcjDetailList = rcjDetailList || [];
                    let resultRcj = await this.service.rcjProcess.pasteRcj(_.cloneDeep(itemConstructProjectRcjs), _.cloneDeep(itemRcjDetailList), constructId, singleId, unitId, res.data.sequenceNbr,sourceUnitId);
        
                if (isSzgcWxyhCopy!==isSzgcWxyh) {
                    // isSzgcWxyhCopy == true isSzgcWxyh == false  /0.9
                    //isSzgcWxyhCopy == false isSzgcWxyh == true  *0.9
                    if( res.data.isSupplement != 1){
                        let rcjList = this.service.rcjProcess.queryRcjDataByDeId(res.data.sequenceNbr,constructId, singleId, unitId);
                        // 获取人材机列表
                            if(isSzgcWxyhCopy){
                                // 除以0.9
                               await this.service.rcjProcess.insertDeZXRcjListBetter(rcjList);
                            }else{
                                // 乘以0.9
                                await this.service.rcjProcess.insertDeZXRcjList(rcjList);
                            }

                    }
                 }
                    //如果复制的存在标准换算信息
                    if (!_.isEmpty(rcjRules)) {
                        let copeRcjRules = {};
                        _.forEach(itemConstructProjectRcjs, (item) => {
                            let copeRules = rcjRules[item.sequenceNbr];
                            if (!_.isEmpty(copeRules) && !_.isEmpty(resultRcj)) {
                                copeRcjRules[resultRcj.map.get(item.sequenceNbr).sequenceNbr] = _.cloneDeep(copeRules);
                            }
                        })
                        let nrcjRules = PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules;
                        if (_.isEmpty(nrcjRules)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = copeRcjRules;
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = {...nrcjRules, ...copeRcjRules};
                        }
                    }
                }
                //处理换算记录
                if (!_.isEmpty(copeUnitProject.conversionInfoList)) {
                    //换算信息数据

                    let itemConversionInfoList = _.filter(_.cloneDeep(copeUnitProject.conversionInfoList), (i) => i.deId == key);
                    for (let i = 0; i < itemConversionInfoList.length; i++) {
                        itemConversionInfoList[i].deId = res.data.sequenceNbr;
                    }
                    let conversionInfoList = PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList;
                    if (_.isEmpty(conversionInfoList)) {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = itemConversionInfoList;
                    } else {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = conversionInfoList.concat(itemConversionInfoList);
                    }
                   

                }
                if (!_.isEmpty(copeUnitProject.defaultConcersions)) {
                    if (copeUnitProject.defaultConcersions[key]) {
                        let item = copeUnitProject.defaultConcersions[key];
                        let defaultConcersions = PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions;
                        if (_.isEmpty(defaultConcersions)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions = {[res.data.sequenceNbr]: item};
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions[res.data.sequenceNbr] = item;
                        }
                        
                        //this.service.conversionInfoService.addInfoForSzsswxyhzxzx22(PricingFileFindUtils.getUnit(constructId, singleId, unitId), res.data);
                    }
                }
                if (isSzgcWxyhCopy!==isSzgcWxyh) {
                    let itemBillProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId).itemBillProjects;
                    let de = itemBillProjects.getNodeById(res.data.sequenceNbr);
                     if(isSzgcWxyhCopy){
                         this.service.conversionInfoService.deleteInfoForSzsswxyhzxzx22(PricingFileFindUtils.getUnit(constructId, singleId, unitId), de);
                     }else{
                        if (!copeUnitProject.defaultConcersions[key]) {
                            this.service.conversionInfoService.addInfoForSzsswxyhzxzx22(PricingFileFindUtils.getUnit(constructId, singleId, unitId), de);
                        }
                         
                     }
                 }
                delete globalThis.isSzgcWxyhCopy;
            }
        }
        //处理复制粘贴后的 QDL关联
        let newitemBillProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId).itemBillProjects;
        let alldata = newitemBillProjects.getAllNodes()
        if (!_.isEmpty(newitemBillProjects)) {
            //处理清单 QDL
            for (let i = 0; i < alldata.length; i++) {
                let item = alldata[i];
                if (item.kind === BranchProjectLevelConstant.qd) {
                    await this._baseBranchProjectOptionService.updateQdToDeQDL(constructId, singleId, unitId, newitemBillProjects, item.sequenceNbr);
                    this.service.unitPriceService.caculateQDUnitPrice(constructId, singleId, unitId, item.sequenceNbr,
                        true, newitemBillProjects);
                }
            }
        }
        await this.service.autoCostMathService.autoCostMath({
            constructId,
            singleId,
            unitId
        });
        return true;
    }

    //帮伟全复制过来
    getFbfxAllData(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize, isAllFlag) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        return this._baseBranchProjectOptionService.pageSearchForAllData(pageNum, pageSize, sequenceNbr,
            PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId),
            this.disPlayType, isAllFlag,unit.screenCondition);
    }

    async removeLine(constructId, singleId, unitWorkId, pointLine, isBlock) {
        // TODO  2024.12.31 产品要求删除定额时 如果清单是临时删除状态将不删除清单
        //  http://172.16.6.4/zentao/www/bug-view-22856.html#app=execution  禅道bug链接
        // if (pointLine.kind == BranchProjectLevelConstant.de) {
        //     pointLine = this.clearQdData(pointLine, constructId, singleId, unitWorkId);
        //     if (ObjectUtils.isEmpty(pointLine)) {
        //         return;
        //     }
        // }
        let removeStrategy = new RemoveStrategy({constructId, singleId, unitId: unitWorkId, pageType: "fbfx"})
        await removeStrategy.execute({pointLine, isBlock});
        await removeStrategy.after();
        //获取parentNode
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        OptionMenuHandler.fillUpDown(fbFx.getNodeById(pointLine.parentId));
        //状态数据处理
        this._baseBranchProjectOptionService.setItemDtaStatus(fbFx.getAllNodes());
        //删除后处理颜色数据
        await this.updateUnitColorList(constructId, singleId, unitWorkId, "fbfx");
        //如果删除的是定额 将定额的子定额数据进行批量删除
        if (pointLine.kind == BranchProjectLevelConstant.de) {
            let itemBillProjects = fbFx.filter(de=>de.parentDeId==pointLine.sequenceNbr);
            if(ObjectUtils.isNotEmpty(itemBillProjects)){
                let sequenceNbrs = itemBillProjects.map(de=>de.sequenceNbr);
                await this.batchDelete(constructId, singleId, unitWorkId,sequenceNbrs);
            }
        }
        return true;

    }

    clearQdData(pointLineDe, constructId, singleId, unitWorkId) {
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        //获取定额对应清单
        let pointLine = allData.getNodeById(pointLineDe.parentId);
        if (ObjectUtils.isEmpty(pointLine)) {
            return pointLine;
        }
        if (pointLine.tempDeleteFlag) {
            let children = pointLine.children;
            if (children.length == 1) {
                return pointLine;
            }
        }
        return pointLineDe;

    }

    /**
     * 删除行
     */
    removeLineOld(constructId, singleId, unitWorkId, pointLine, isBlock) {
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
        let removedRes
        let args = {};
        args["constructId"] = constructId;
        args["singleId"] = singleId;
        args["unitId"] = unitWorkId;
        args["type"] = "fbfx";
        if (!isBlock) {
            removedRes = this._baseBranchProjectOptionService.removeLine(pointLine, allData, args);
            // 处理被删除的数据行的相关联数据
            this.itemBillProjectProcess.delLineRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
            this.unitPriceServices.deleteFee(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
        } else {
            removedRes = this._baseBranchProjectOptionService.removeLineBlock(pointLine, allData, influenceLines => {
                // 处理被删除的数据行的相关联数据
                this.itemBillProjectProcess.delBlockRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
                for (let i = 0; i < influenceLines.length; ++i) {
                    this.unitPriceServices.deleteFee(constructId, singleId, unitWorkId, influenceLines[i].sequenceNbr);
                }
            }, args);
        }


        if (pointLine.kind === BranchProjectLevelConstant.qd || pointLine.kind === BranchProjectLevelConstant.de) {
            let parentLine = this._findLine(allData, pointLine.parentId);
            if (!_.isEmpty(parentLine)) {
                if (parentLine.kind === BranchProjectLevelConstant.qd) {
                    this.unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                } else {
                    this.unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                }
            }
        }

        return true;
    }

    /**
     * 展开
     */
    openLine(constructId, singleId, unitWorkId, pointLine) {
        this._baseBranchProjectOptionService.openLine(pointLine, PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId));
    }

    /**
     * 折叠
     */
    closeLine(constructId, singleId, unitWorkId, pointLine) {
        this._baseBranchProjectOptionService.closeLine(pointLine, PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId));
    }

    searchForsequenceNbr(constructId, singleId, unitWorkId, sequenceNbr) {
        return this._baseBranchProjectOptionService.searchForsequenceNbr(sequenceNbr,
            PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId));
    }
    searchPonitAndChild(args){
        let {constructId, singleId, unitId, sequenceNbr} =args;
        let fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
       return  this._baseBranchProjectOptionService.searchPonitAndChild(sequenceNbr,fbfx);
    }
    /**
     * 分页查询
     */
    getFbFx(constructId, singleId, unitWorkId, sequenceNbr, pageNum, pageSize, isAllFlag, colorList) {
        //获取单位数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        let result = this._baseBranchProjectOptionService.pageSearch(pageNum, pageSize, sequenceNbr,
            PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId),
            this.disPlayType, isAllFlag, unit.screenCondition);
        //对数据进行颜色过滤
        if (ObjectUtils.isNotEmpty(colorList)) {
            if (!colorList.includes(null)) {
                this.getFbFxFilter(result, constructId, singleId, unitWorkId, colorList);
            }
        }
        //处理定额收起展开标识
        result.data.forEach(item => {
            if (!item.rcjFlag && item.kind == BranchProjectLevelConstant.de) {//编码 materialCode 名称materialName 类别 type 市场价 marketPrice 合价total 合计数量 totalNumber

                if(item.displaySign!=2){
                    let rcj = unit.constructProjectRcjs.filter(itemrcj => itemrcj.deId == item.sequenceNbr && (itemrcj.kind == 4 || itemrcj.kind == 5));
                    if (rcj && rcj.length > 0) {
                        if(item.displaySign==0){
                            item.displaySign=1;
                        }
                    }else {
                        item.displaySign=0;
                    }
                }

            }
        });
        //挂人材机 名称name 编码fxcode bdcode 单位unit  工程量quantityExpression  单价zjfPrice 合价zjfTotal   主材费单价 zcfee 主材费合价totalZcfee 设备费单价sbfPrice 设备费合价sbfTotal
        let rcjs = [];
        result.data.forEach(item => {
            if (!item.rcjFlag && item.kind == BranchProjectLevelConstant.de) {//编码 materialCode 名称materialName 类别 type 市场价 marketPrice 合价total 合计数量 totalNumber
                let rcj = unit.constructProjectRcjs.filter(itemrcj => itemrcj.deId == item.sequenceNbr && (itemrcj.kind == 4 || itemrcj.kind == 5));
                if (rcj && rcj.length > 0) {
                    //判断定额收起展开状态
                        if(item.displaySign==1){
                            rcjs = rcjs.concat(rcj.map(rcj => {
                                //获取人材机下拉数据
                                let typeList = this.service.rcjProcess.typeListByKind(rcj);
                                let refData = {
                                    type: rcj.type, name: rcj.materialName,
                                    fxCode: rcj.materialCode,
                                    bdCode: rcj.materialCode,
                                    quantity: rcj.totalNumber,
                                    zjfPrice: rcj.marketPrice,
                                    zjfTotal: rcj.total,
                                    zcfee: rcj.kind == 5 ? rcj.marketPrice : 0,
                                    totalZcfee: rcj.kind == 5 ? rcj.total : 0,
                                    sbfPrice: rcj.kind == 4 ? rcj.marketPrice : 0,
                                    sbfTotal: rcj.kind == 4 ? rcj.total : 0,
                                    typeList: typeList,
                                    markSum: rcj.markSum,
                                }
                                return {
                                    ...rcj,
                                    appendType: [rcj.type],//编辑器类型【主材费】改为appendType字段
                                    kind: 90 + Number(rcj.kind),
                                    parentId: item.sequenceNbr, ...refData,
                                    optionMenu: [BranchProjectOptionMenuConstant.removeLine]
                                };
                            }));
                        }

                }

            }
            //定额级别人材机处理类型下拉数据
            if (item.rcjFlag && item.kind == BranchProjectLevelConstant.de) {
                let constructProjectRcjs = unit.constructProjectRcjs.find(itemrcj => itemrcj.deId == item.sequenceNbr );
                //获取人材机下拉数据
                let typeList = this.service.rcjProcess.typeListByKind(constructProjectRcjs);
                item.typeList=typeList;
                item.markSum=constructProjectRcjs.markSum;
            }

        });
        return {...result, data: result.data.concat(rcjs)};
    }

    /**
     * 颜色过滤
     */
    getFbFxFilter(result, constructId, singleId, unitWorkId, colorList) {
        let data = result.data;
        //获取所有的数据颜色数据
        let allNodes = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId).getAllNodes();
        let itemBillProjects = [];
        itemBillProjects = allNodes.filter(item => colorList.includes(item.color));
        //如果包含none标识需要没有颜色的
        if (colorList.includes("none")) {
            itemBillProjects.push(...allNodes.filter(item => ObjectUtils.isEmpty(item.color)));
        }
        //获取每一个数据的父级数据
        let set = new Set();
        itemBillProjects.forEach(item => {
            getParnetNode(item, set);
            //如果是清单需要获取他的子集定额数据    如果是定额需要获取他的同级别其他数据
            if (item.kind == BranchProjectLevelConstant.qd) {
                let filter = allNodes.filter(de => de.parentId == item.sequenceNbr);
                filter.forEach(f => set.add(f.sequenceNbr));
            }
            if (item.kind == BranchProjectLevelConstant.de) {
                let filter = allNodes.filter(de => de.parentId == item.parentId);
                filter.forEach(f => set.add(f.sequenceNbr));
            }
        });

        //过滤分页查询数据
        let filter1 = data.filter(d => set.has(d.sequenceNbr));
        result.data = filter1;

        function getParnetNode(node, set) {
            set.add(node.sequenceNbr);
            if (ObjectUtils.isEmpty(node.parentId) || node.parentId == "0") {
                return;
            }
            let filter = allNodes.find(n => n.sequenceNbr == node.parentId);
            getParnetNode(filter, set);
        }

    }

    _findLine(allData, id) {
        if (!allData) {
            return
        }
        for (let i = 0; i < allData.length; ++i) {
            if (allData[i].sequenceNbr === id) {
                return allData[i];
            }
        }
    }

    async _getDefaultFeeFile(constructId, singleId, unitWorkId, mainDeLib, constructMajorType) {
        // 根据 主定额库 和 工程专业 获取取费文件code
        let querySql = "select default_qf_code as feecode\n" +
            "from base_speciality_de_fee_relation\n" +
            "where library_code = ?\n" +
            "  and unit_project_name = ?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(querySql).all(mainDeLib, constructMajorType);
        let feeCode = sqlRes[0].feecode;
        // 根据 feeCode 查询取费文件
        let feeFile = await this.service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitWorkId, feeCode);

        return feeFile;
    }

    _getbaseListIdByCode(code) {
        let sql = "select sequence_nbr as id from base_list where bd_code_level04 = ?";
        let sqlres = this.app.betterSqlite3DataSource.prepare(sql).all(code);

        if (sqlres.length === 0) {
            return undefined;
        } else {
            return sqlres[0].id;
        }
    }

    _saveCustomerInfo(editLine, updateInfo) {
        // todo 将前端数据合入
    }

    /**
     * 临时删除数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @param idList  选中数据id集合
     * @param modelType  模块类型(数字)  1 分部分项  2 措施项目
     * @param dataType   数据类型(数字)  1 清单  2 定额(含人材机)  3 父级人材机
     * @param tempDeleteFlag   临时删除状态 true/false
     */
    async updateDelTempStatus(args) {
        let {constructId, singleId, unitId, idList, modelType, dataType, tempDeleteFlag} = args;
        let allData;
        //分部分项
        if (modelType == 1) {
            allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            this.delTempStatus(constructId, singleId, unitId, allData, idList, tempDeleteFlag, dataType);
        } else {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
            //措施项目
            this.delTempStatus(constructId, singleId, unitId, allData, idList, tempDeleteFlag, dataType);
        }

        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        //重新计算费用汇总
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
    }


    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param id  选中清单id
     * @param modelType  模块类型(数字)  1 分部分项  2 措施项目
     * @param tempDeleteFlag  删除临时删除状态  false
     */
    async cleanQdDelTempStatus(args) {
        let {constructId, singleId, unitId, id, modelType, tempDeleteFlag} = args;
        let allData;
        //分部分项
        if (modelType == 1) {
            allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        } else {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        let line = allData.getNodeById(id);
        //如果清单是临时删除状态
        if (ObjectUtils.isNotEmpty(line) && line.tempDeleteFlag) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            line.tempDeleteFlag = tempDeleteFlag;
            // //过滤掉所有清单下的定额
            //  let deList = allData.filter(de => de.parentId == line.sequenceNbr);
            // for (const obj of deList) {
            //     obj.tempDeleteFlag = tempDeleteFlag;
            //     this.setDeOrQdTempDelStatust(constructId, singleId, unitId, allData, obj, tempDeleteFlag, unit);
            // }
            //处理清单临时删除
            this.setQdTempDelStatust(constructId, singleId, unitId, allData, line, tempDeleteFlag, unit);
        }

    }

    /**
     * 根据类别批量删除
     * @param constructId
     * @param singleId
     * @param unitId
     * @param dataType  模块类型(数字)  1批量删除所有临时删除项  2 批量删除所有工程量为0项
     * @param rangeType   作用范围(数字)  1 工程项目   2 单位工程
     * @param lockFlag   锁定状态下的清单定额数据根据弹窗开关控制是否受影响 true影响   false不影响
     * @param kindArr   如果删除的是批量删除所有工程量为0项   可选择清单03  定额04      人材机05 【03,04,05】(不区分)
     */
    async batchDelByTypeOf(args) {
        let {constructId, singleId, unitId, dataType, rangeType, lockFlag, kindArr} = args;
        //1批量删除所有临时删除项
        if (dataType == 1) {
            await this.queryAllTempDelData(constructId, singleId, unitId, rangeType, lockFlag);
        } else {
            //批量删除所有工程量为0项
            await this.queryAllQuantityData(constructId, singleId, unitId, rangeType, lockFlag, kindArr);
        }

        return true;
    }


    /**
     * 设置临时删除状态
     * @param allData
     * @param lineId
     * @param status
     * @param dataType   数据类型(数字)  1 清单  2 定额(含人材机)  3 父级人材机  4 子集人材机
     */
    delTempStatus(constructId, singleId, unitId, allData, idList, tempDeleteFlag, dataType) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //明细区人材机数据
        // if (dataType == 3) {
        //     this.setRcjTempDelStatus(constructId, singleId, unitId, idList, tempDeleteFlag, unit,allData);
        //     // 人材机 合计数量 合价  定额 单价构成 TODO 调用兴栋的框架
        // } else {
        //     let line = allData.filter(a => idList.includes(a.sequenceNbr));
        //     for (const obj of line) {
        //         obj.tempDeleteFlag = tempDeleteFlag;
        //         //如果临时删除的是清单 定额
        //         if(obj.kind=="03"){
        //             this.sesetQdTempDelStatust(constructId, singleId, unitId, allData, obj, tempDeleteFlag,unit)
        //         }else {
        //             this.setDeOrQdTempDelStatust(constructId, singleId, unitId, allData, obj, tempDeleteFlag, unit);
        //         }
        //
        //     }
        // }
        let line = allData.filter(a => idList.includes(a.sequenceNbr));
        //过滤出清单
        let qdIds = line.filter(qd => qd.kind == BranchProjectLevelConstant.qd).map(qd => qd.sequenceNbr);
        //过滤掉所有清单下的定额
        line = line.filter(a => !qdIds.includes(a.parentId));
        for (const obj of line) {
            obj.tempDeleteFlag = tempDeleteFlag;
            //如果临时删除的是清单 定额
            if (obj.kind == "03") {
                this.setQdTempDelStatust(constructId, singleId, unitId, allData, obj, tempDeleteFlag, unit)
            } else {
                this.setDeOrQdTempDelStatust(constructId, singleId, unitId, allData, obj, tempDeleteFlag, unit);
            }

        }

    }

    setQdTempDelStatust(constructId, singleId, unitId, allData, qd, tempDeleteFlag, unit) {
        //设置定额工程量表达式为空
        if (tempDeleteFlag) {
            qd.quantityExpressionTemp = qd.quantityExpression;
            qd.quantityExpression = null;
        } else {
            qd.quantityExpression = qd.quantityExpressionTemp;
        }
        //重新计算工程量表达式
        this.service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, qd);
        //获取所有的定额数据
        let deList = allData.filter(obj => obj.parentId == qd.sequenceNbr);
        if (ObjectUtils.isNotEmpty(deList)) {
            let rcjDeleteStrategy = new RcjDeleteStrategy({
                constructId,
                singleId,
                unitId,
                projectObj: PricingFileFindUtils.getProjectObjById(constructId)
            });
            for (const de of deList) {
                if (de.tempDeleteFlag && tempDeleteFlag) {
                    continue;
                } else {
                    de.tempDeleteFlag = tempDeleteFlag;
                    this.setDeOrQdTempDelStatust(constructId, singleId, unitId, allData, de, tempDeleteFlag, unit);
                    rcjDeleteStrategy.execute({de: de, type: 1, tempDeleteFlag});
                }
            }
        }else {
            if(qd.lockPriceFlag){
                qd.price=qd.lockPriceBack;
                qd.total=NumberUtil.numberScale2(NumberUtil.multiply(qd.price,qd.quantity));
            }
        }


    }

    setDeOrQdTempDelStatust(constructId, singleId, unitId, allData, de, tempDeleteFlag, unit) {

        //设置定额工程量表达式为空
        if (tempDeleteFlag) {
            de.quantityExpressionTemp = de.quantityExpression;
            de.quantityExpression = null;
        } else {
            de.quantityExpression = de.quantityExpressionTemp;
        }
        // if (ObjectUtils.isEmpty(de.libraryCode)) {
        //     return;
        // }
        //重新计算工程量表达式
        this.service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, de);
        //获取定额下的人材机数据de
        // let constructProjectRcjs = this.service.rcjProcess.queryRcjDataByDeId(de.sequenceNbr, constructId, singleId, unitId);
        // if(ObjectUtils.isNotEmpty(constructProjectRcjs)){
        //     let idList = constructProjectRcjs.map(rcj=>rcj.sequenceNbr);
        //     this.setRcjTempDelStatus(constructId, singleId, unitId, idList, tempDeleteFlag, unit,allData);
        // }
        let rcjDeleteStrategy = new RcjDeleteStrategy({
            constructId,
            singleId,
            unitId,
            projectObj: PricingFileFindUtils.getProjectObjById(constructId)
        });
        rcjDeleteStrategy.execute({de: de, type: 1, tempDeleteFlag});

        let calculationTool = new CalculationTool({constructId, singleId, unitId, allData});
        calculationTool.calculationChian(de);


    }


    /**
     * 设置人材机以及子集状态
     * @param constructId
     * @param singleId
     * @param unitId
     * @param allData
     * @param idList
     * @param tempDeleteFlag
     */
    setRcjTempDelStatus(constructId, singleId, unitId, idList, tempDeleteFlag, unit, allData) {
        //获取所有的父级人材机
        let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
        let line = rcjList.filter(a => idList.includes(a.sequenceNbr));
        let de = allData.filter(de => de.sequenceNbr == line[0].deId)[0];
        let deMathBase = 1;
        //设置为临时删除
        if (tempDeleteFlag) {
            for (const rcj of line) {
                rcj.tempDeleteFlag = tempDeleteFlag;
                rcj.tempDeleteBackupResQty = rcj.resQty;
                rcj.resQty = null;

                //合计数量
                rcj.totalNumber = NumberUtil.numberScale(NumberUtil.multiplyParams(rcj.resQty, de.quantity, deMathBase), 4);
                //合价
                rcj.total = NumberUtil.numberScale(NumberUtil.numberScale(NumberUtil.multiply(rcj.totalNumber, rcj.marketPrice)), 2);

                //获取人材机的二级人材机
                let rcjDetailListByRcjId = PricingFileFindUtils.getRcjDetailListByRcjId(unit, rcj);
                for (const rcjDetail of rcjDetailListByRcjId) {
                    rcjDetail.tempDeleteFlag = tempDeleteFlag;
                }
            }
        } else {
            //取消临时删除
            for (const rcj of line) {
                rcj.tempDeleteFlag = tempDeleteFlag;
                rcj.resQty = rcj.tempDeleteBackupResQty;
                //合计数量
                rcj.totalNumber = NumberUtil.numberScale(NumberUtil.multiplyParams(rcj.resQty, de.quantity, deMathBase), 4);
                //合价
                rcj.total = NumberUtil.numberScale(NumberUtil.numberScale(NumberUtil.multiply(rcj.totalNumber, rcj.marketPrice)), 2);
                //获取人材机的二级人材机
                let rcjDetailListByRcjId = PricingFileFindUtils.getRcjDetailListByRcjId(unit, rcj);
                for (const rcjDetail of rcjDetailListByRcjId) {
                    rcjDetail.tempDeleteFlag = tempDeleteFlag;
                }
            }
        }


    }


    /**
     * 所有临时删除数据
     */
    async queryAllTempDelData(constructId, singleId, unitId, rangeType, lockFlag) {
        if (rangeType == 1) {
            //获取所有的单位
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (const unit of unitList) {
                await this.batchDelAllData(constructId, unit.spId, unit.sequenceNbr, lockFlag, 1);
            }
        } else {
            await this.batchDelAllData(constructId, singleId, unitId, lockFlag, 1);
        }
    }


    /**
     * 所有工程量为0项
     */
    async queryAllQuantityData(constructId, singleId, unitId, rangeType, lockFlag, kindArr) {
        if (rangeType == 1) {
            //获取所有的单位
            // let singleProjectList = PricingFileFindUtils.getSingleProjectList(constructId);
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (const unit of unitList) {
                await this.batchDelAllData(constructId, unit.spId, unit.sequenceNbr, lockFlag, 2, kindArr);
            }
        } else {
            await this.batchDelAllData(constructId, singleId, unitId, lockFlag, 2, kindArr);
        }
    }


    async batchDelAllData(constructId, singleId, unitId, lockFlag, type, kindArr) {

        let fbfx;
        let csxm;
        //获取分部分项 措施项目数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        fbfx = unit.itemBillProjects.getAllNodes();
        csxm = unit.measureProjectTables.getAllNodes();
        //锁定的也处理
        // if (lockFlag) {
        //     fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        //     csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        // } else {
        //     //锁定的不处理
        //     fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).filter(fb => fb.isLocked != 1);
        //     csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).filter(cs => cs.isLocked != 1);
        // }
        if (!lockFlag) {
            //锁定的不处理
            fbfx = fbfx.filter(fb => fb.isLocked != 1);
            csxm = csxm.filter(cs => cs.isLocked != 1);
        }

        //过滤调所有安文费清单
        let ids = PricingFileFindUtils.getQdByAwf(constructId, singleId, unitId).map(qd => qd.sequenceNbr);
        csxm = csxm.filter(qd => !ids.includes(qd.sequenceNbr));
        //所有临时删除数据
        if (type == 1) {
            let seqs = fbfx.filter(fb => fb.tempDeleteFlag).map(fb => fb.sequenceNbr);
            await this.batchDelete(constructId, singleId, unitId, seqs);
            let csseqs = csxm.filter(cs => cs.tempDeleteFlag).map(cs => cs.sequenceNbr);
            await this.stepItemCostService.batchDelete(constructId, singleId, unitId, csseqs);
            //删除当前单位中所有临时删除的人材机
            // 获取单位下人材机
            let rcjArray = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
            if (ObjectUtils.isEmpty(rcjArray)) {
                return false;
            }
            let delRcjIds = [];
            // 批量删除人材机
            for (let i = rcjArray.length - 1; i >= 0; i--) {
                if (rcjArray[i].tempDeleteFlag) {
                    delRcjIds.push(rcjArray[i].sequenceNbr);
                    rcjArray.splice(i, 1);
                }
            }
            if (ObjectUtils.isNotEmpty(delRcjIds)) {
                // 批量删除人材机配比明细
                this.service.constructProjectRcjService.delConstructRcjDetailBatch(delRcjIds, constructId, singleId, unitId);
            }
        } else {
            //所有工程量为0项
            // let rcjFlag;
            // if (kindArr.includes("05")) {
            //     rcjFlag = true;
            // }
            // let seqs;
            // 临时删除的不可删除   工程量为0  或者为空的删除
            let seqs = fbfx.filter(fb => (fb.quantity == 0 || ObjectUtils.isEmpty(fb.quantity)) && !fb.tempDeleteFlag && kindArr.includes(fb.kind)).map(fb => fb.sequenceNbr);
            // if (!rcjFlag) {
            //     seqs = itemBillProjects.filter(fb => fb.rcjFlag != 1).map(fb => fb.sequenceNbr);
            // } else {
            //     seqs = itemBillProjects.map(fb => fb.sequenceNbr);
            // }
            await this.batchDelete(constructId, singleId, unitId, seqs);
            let csseqs = csxm.filter(cs => (cs.quantity == 0 || ObjectUtils.isEmpty(cs.quantity)) && !cs.tempDeleteFlag && kindArr.includes(cs.kind)).map(cs => cs.sequenceNbr);
            // if (!rcjFlag) {
            //     csseqs = measureProjectTables.filter(cs => cs.rcjFlag != 1).map(cs => cs.sequenceNbr);
            // } else {
            //     csseqs = itemBillProjects.map(cs => cs.sequenceNbr);
            // }
            await this.stepItemCostService.batchDelete(constructId, singleId, unitId, csseqs);

        }
    }


    /**
     * 处理颜色设置后的单位颜色设置
     */
    async updateUnitColorList(constructId, singleId, unitId, type) {
        //获取单位工程
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unit.colorList)) {
            unit.colorList = {};
        }
        if (type == "fbfx") {
            //获取所有分部分项数据的颜色
            let map = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).filter(item => ObjectUtils.isNotEmpty(item.color) && item.color != "none").map(item => item.color);
            unit.colorList[type] = this.unique(map);
            return;
        }

        if (type == "csxm") {
            //获取所有措施项目数据的颜色
            let map = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).filter(item => ObjectUtils.isNotEmpty(item.color) && item.color != "none").map(item => item.color);
            unit.colorList[type] = this.unique(map);
            return;
        }
        if (type == "rcj") {

        }

    }


    unique(arr) {
        return [...new Set(arr)];
    }


    async queryExistsGlDe(constructId, singleId, unitId, idList, type) {
        let flag = false;
        let allData;
        if (type =="fbfx") {
            allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
        } else {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes();
        }

        for(const id of idList){
            let find = allData.filter(de=>de.parentDeId==id);
            if(ObjectUtils.isNotEmpty(find)){
                flag=true;
                break;
            }
        }
        return flag;

    }

    /*
    * 获取当前项目分部分项或者措施项目的分部深度
    * */
    getFbDeep(args){
        let {constructId, singleId, unitId,pageType} = args;
        let fbFxa = pageType=="fbfx"?PricingFileFindUtils.getFbFx(constructId, singleId, unitId):PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let getRealDeep=(root,isroot)=>{
            //获取分布的真实深度
            let isfb = root.kind==BranchProjectLevelConstant.fb||root.kind==BranchProjectLevelConstant.zfb;
            if(isfb||isroot){
                let deep =0;
                if(root.children&&root.children.length>0){
                    root.children.forEach(item=>{
                        let d =getRealDeep(item,false);
                        if(d >deep){
                            deep = d;
                        }
                    })
                }
                return isroot?deep:1+deep;
            }
            return 0
        }
        return getRealDeep(fbFxa.root,true)
    }
    /*展开*/
    spread(args){
        //展开类型  全部 1-4级分部 清单 定额 主材设备
       let {constructId, singleId, unitId,hierachy,pageType} = args;
       //先全部关闭
        let fbFxa = pageType=="fbfx"?PricingFileFindUtils.getFbFx(constructId, singleId, unitId):PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        let unit =PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        fbFxa.getAllNodes().forEach(item=>{
            if(item.displaySign!=null){
                item.displaySign = BranchProjectDisplayConstant.close;
            }
            if(!item.children||!item.children.length){
                item.displaySign = BranchProjectDisplayConstant.noSign;
            }
            if(item.kind==BranchProjectLevelConstant.de){
                let rcj = unit.constructProjectRcjs.filter(itemrcj => itemrcj.deId == item.sequenceNbr && (itemrcj.kind == 4 || itemrcj.kind == 5));
                if(rcj.length>0){
                    item.displaySign = BranchProjectDisplayConstant.close;
                }
            }

        });
        let realDeep=this.getFbDeep(args);
        let kindItems={
           "all":{kind:"99"},
           "1":{kind:BranchProjectLevelConstant.fb,deep:1},
           "2":{kind:BranchProjectLevelConstant.zfb,deep:2},
           "3":{kind:BranchProjectLevelConstant.zfb,deep:3},
           "4":{kind:BranchProjectLevelConstant.zfb,deep:4},
           "qd":{kind:BranchProjectLevelConstant.qd},
           "de":{kind:BranchProjectLevelConstant.de},
           "zcsb":{kind:"99"},
       }

       let type = Number(kindItems[hierachy].kind);
       //获取节点深度
       let getDeep= (item)=>{
           let deep = 0;
           while(item.parent){
               item = item.parent;
               deep++;
           }
           return deep;
       };
       //过滤符合条件的节点
       let filter =(item)=>{
           //获取 当前深度 展开所有
           let kindItem = kindItems[hierachy];
           if(kindItem.deep>realDeep){
               kindItem.deep=realDeep;
           }
           //如果有深度 代表是分部 计算深度并比较
           return ((item.kind==BranchProjectLevelConstant.fb||item.kind==BranchProjectLevelConstant.zfb)&&kindItem.deep)
               ? (Number(item.kind)<=Number(type)&&getDeep(item)<kindItem.deep)
               :(Number(item.kind)<Number(type));
       };
       //获取数据
        let fbFx = pageType=="fbfx"?PricingFileFindUtils.getFbFx(constructId, singleId, unitId):PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        fbFx.getAllNodes().forEach(item=>{
            if(item.displaySign&&filter(item)){
                if(kindItems[hierachy].deep&&item.children&&item.children[0]&&item.children[0].kind==BranchProjectLevelConstant.qd){
                     return;
                }
                item.displaySign = BranchProjectDisplayConstant.open;
            }
        });
        return true;
    }
    /**
     * 批量删除子目-查询列表
     */
    async batchDelBySeachList(args){
        let {constructId, singleId, unitId, scopeType,showType,code,name} = args;
        let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
        let  result = [];
        if(scopeType != 1){
            let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
            result = await this.service.constructProjectService.generateLevelTreeNode(projectObj)
            result.forEach(item=>{
                item.sequenceNbr = item.id;
            });
        }
        let unitList =[];
        if (scopeType == 1){
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unitList.push(unit);
        }else {
            unitList = PricingFileFindUtils.getUnitList(constructId);
        }
        for (let unit of unitList) {
            let {constructId, spId, sequenceNbr} = unit;
            let fbFx = PricingFileFindUtils.getFbFx(constructId, spId, sequenceNbr);
            let csxm = PricingFileFindUtils.getCSXM(constructId, spId, sequenceNbr);
           let f = (item)=>{
                if(item.kind != BranchProjectLevelConstant.de&&!item.children.length)return  false;
                if(item.tempDeleteFlag) return false;
                if (item.kind == BranchProjectLevelConstant.de){
                    if (ObjectUtils.isNotEmpty(name)){
                        if (!item.bdName.includes(name)) return false;
                    }
                    if (ObjectUtils.isNotEmpty(code)){
                        if (!item.bdCode.includes(code)) return false;
                    }
                }
                return true;
            }
            //
            let fbFxAllData = fbFx.flattenTree(fbFx.root, f);
           let filterArr=[];
            let csxmAllData = csxm.flattenTree(csxm.root,(item)=>{
                if(item.constructionMeasureType&&item.constructionMeasureType==2){
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                if(filterArr.includes(item.parentId)){
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                return f(item);
            });
            //分部分项
            for (let item of fbFxAllData) {
                if(item.parentId=="0"){
                    item.parentId = sequenceNbr;
                }
                item.pageType="fbfx";
                result.push(item);
            }
            //措施项目
            for (let item of csxmAllData) {
                if(item.parentId=="0"||!item.parentId){
                    item.parentId = sequenceNbr;
                }
                item.pageType="csxm";
                result.push(item);
            }
        }
        return result;
    }

    async dataDeal(data,type){
        if (type ===1){
            if (ObjectUtils.isEmpty(data.zjfPriceFormula) || !(await this.dataComparison(data.zjfPriceFormula,data.zjfPrice))){
                data.zjfPriceFormula = data.zjfPrice;
            }
            if (ObjectUtils.isNotEmpty(data.projectAttr)){
                data.projectAttr = data.projectAttr.trim();
            }
            if(ObjectUtils.isNull(data.isChangeAva)){
                data.isChangeAva = "undefined"
            }
            if (ObjectUtils.isEmpty(data.zjfPriceFormula)){

            }
        }
       else if (type ===2) {
            if (ObjectUtils.isEmpty(data.priceMarketFormula) || !(await this.dataComparison(data.priceMarketFormula,data.priceMarket))){
                data.priceMarketFormula = data.priceMarket;
            }
            if (ObjectUtils.isEmpty(data.priceMarketTaxFormula) || !(await this.dataComparison(data.priceMarketTaxFormula,data.priceMarketTax))){
                data.priceMarketTaxFormula = data.priceMarketTax;
            }
            if (ObjectUtils.isEmpty(data.marketPriceFormula) || !(await this.dataComparison(data.marketPriceFormula,data.marketPrice))){
                data.marketPriceFormula = data.marketPrice;
            }
            if (ObjectUtils.isNotEmpty(data.rcjDetailsDTOs)){
                data.rcjDetailsDTOs.forEach(e => this.dataDeal(e,2))
            }
        }
    }

    async dataComparison(formula,value){
        const runResult = new Function(`return ${formula}`);
        if (isFinite(runResult())) {
          return runResult() === value;
        }
    }


}

ItemBillProjectOptionService.toString = () => "[class ItemBillProjectOptionService]"
module.exports = ItemBillProjectOptionService
