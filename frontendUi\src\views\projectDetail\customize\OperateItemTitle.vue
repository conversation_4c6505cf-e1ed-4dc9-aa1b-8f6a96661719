<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2025-01-10 09:41:21
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-01-23 17:08:28
-->
<template>
  <a-badge :dot="item.badgeDot">
    <icon-font
      :type="item.iconType"
      class="iconType"
      :style="item.iconStyle ?? {}"
    />
    <div
      class="label"
      v-if="systemConfig.functionalArea.isExpand"
      :style="item.labelStyle ?? {}"
    >
      <slot name="label">{{ item.label }}</slot>
    </div>
  </a-badge>
</template>

<script setup>
import { systemConfigStore } from '@/store/systemConfig';
const systemConfig = systemConfigStore();
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
});
</script>
<style lang="scss" scoped>
.iconType {
  font-size: 26px;
}
.icon {
  width: 28px;
  img {
    width: 100%;
  }
}
.label {
  font-size: 12px;
  margin-top: 2px;
  white-space: nowrap;
}
</style>
