<!--
 * @Descripttion: 从人材机汇总中选择
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-29 09:38:14
-->
<template>
  <common-modal
    className="dialog-comm setAggreScope-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="从人材机汇总中选择"
    width="1000px"
    height="550px"
    :mask="true"
    show-zoom
    resize
    :lock-view="false"
    destroy-on-close
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <p class="hearder">
        <!-- <span class="btnList">
          <a-button
            v-for="i in btnList"
            @click="btnFun(i)"
            size="small"
          >{{i.label}}</a-button>
        </span> -->
        <span class="checkList">
          <a-checkbox
            v-model:checked="state.checkAll"
            :indeterminate="state.indeterminate"
            @change="reloadTableData('check')"
          >
            所有
          </a-checkbox>
          <a-checkbox-group
            @change="reloadTableData('checkList')"
            v-model:value="state.checkedList"
            name="checkboxgroup"
            :options="checkList"
          />
        </span>
        <span class="inputSearch">
          <a-input-search
            size="small"
            v-model:value="searchName"
            :maxlength="50"
            :placeholder="'请输入名称查询'"
            @search="reloadTableData('search')"
          />
        </span>
      </p>

      <div style="height: calc(100% - 75px)">
        <vxe-grid
          class="trends-table-column"
          v-bind="gridOptions"
          ref="vexTable"
          height="100%"
          style="overflow-x: auto;margin-right: 4px"
          :loading="tableLoading"
          :scroll-x="{enabled:true}"
          :scroll-y="{enabled:true}"
        >
          <template #zg_default="{ column, row, $columnIndex }">

            <vxe-checkbox
              v-model="row.isSelect"
              size="small"
              content=""
              :checked-value="true"
              :unchecked-value="false"
              @change="setSelect(row)"
            ></vxe-checkbox>
          </template>
          <template #ifDonorMaterial_default="{ row }">
            {{ getDonorMaterialText(row.ifDonorMaterial) }}
          </template>
          <template #ifProvisionalEstimate_default="{ row }">
            <vxe-checkbox
              v-model="row.ifProvisionalEstimate"
              size="small"
              content=""
              :checked-value="1"
              :unchecked-value="0"
              :disabled="true"
              @change="CheckboxChange(row, 'ifProvisionalEstimate')"
            ></vxe-checkbox>
          </template>
          <template #empty>
            <span style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          ">
              <img
                :src="getUrl('newCsProject/none.png')"
                style="margin: auto"
              />
            </span>
          </template>
        </vxe-grid>
      </div>

      <p class="footer">
        <a-radio-group
          v-model:value="radioValue"
          @change="reloadTableData('radio')"
        >
          <a-radio
            :value="i.value"
            v-for="i in radioList"
          >{{ i.label }}</a-radio>
        </a-radio-group>
        <span class="btn">
          <a-button
            type="primary"
            ghost
            @click="cancel()"
          >取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 14px"
            @click="handleOk()"
          >确定</a-button>
        </span>
      </p>

    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive, toRaw } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import feePro from '@/api/feePro';
import { getUrl } from '@/utils/index';
import { message } from 'ant-design-vue';
import getTableColumns, {
  donorMaterialList,
  getDonorMaterialText,
  otherCodeList,
} from './tableColumns';
const route = useRoute();
const emits = defineEmits(['refresh']);
const props = defineProps(['showInfo']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
let loading = ref(false);
let searchName = ref('');
const btnList = reactive([
  {
    label: '选择全部',
    id: 'all',
  },
  {
    label: '选择主材',
    id: 'material',
  },
  {
    label: '选择设备',
    id: 'device',
  },
  {
    label: '选择全调差材料',
    id: 'diff',
  },
  {
    label: '取消选择',
    id: 'cancelSameMajor',
  },
]);
const getType = {
  0: '其他费',
  1: '人工费',
  2: '材料费',
  3: '机械费',
  4: '设备费',
  5: '主材费',
  6: '商砼',
  7: '砼',
  8: '浆',
  9: '商浆',
  10: '配比',
};
let checkValue = ref([2, 5, 4]);
const state = reactive({
  indeterminate: false,
  checkAll: true,
  checkedList: [2, 5, 4],
});
const checkList = reactive([
  // {
  //   label: '所有',
  //   value: 0,
  // },
  // {
  //   label: '人',
  //   value: 1,
  // },
  {
    label: '材料',
    value: 2,
  },
  // {
  //   label: '机',
  //   value: 3,
  // },
  {
    label: '主材',
    value: 5,
  },
  {
    label: '设备',
    value: 4,
  },
]);
let radioValue = ref(0);
const radioList = reactive([
  {
    label: '所有',
    value: 0,
  },
  {
    label: '已选值',
    value: 1,
  },
  {
    label: '未选值',
    value: 2,
  },
]);
let tableLoading = ref(false);
const gridOptions = reactive({
  headerAlign: 'center',
  showOverflow: true,
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    height: 30,
  },
  checkboxConfig: { showHeader: false },
  columns: [],
  data: [],
  align: 'center',
});
const reloadTableData = type => {
  switch (type) {
    case 'radio':
      gridOptions.data =
        radioValue.value === 0
          ? [...tableData.value]
          : radioValue.value === 1
          ? tableData.value.filter(a => a.isSelect)
          : tableData.value.filter(a => !a.isSelect);
      break;
    case 'search':
      if (searchName.value) {
        gridOptions.data = tableData.value.filter(
          a =>
            a.materialCode.indexOf(searchName.value) !== -1 ||
            a.materialName.indexOf(searchName.value) !== -1
        );
      } else {
        gridOptions.data = tableData.value;
      }
      break;
    case 'checkList':
      console.log('checkValue', state);
      state.indeterminate =
        !!state.checkedList.length &&
        state.checkedList.length < checkList.length;
      state.checkAll = state.checkedList.length === checkList.length;
      refreshData();
      break;
    case 'check':
      console.log('checkValue', state);
      Object.assign(state, {
        checkedList: state.checkAll ? [1, 2, 3, 4, 5] : [],
        indeterminate: false,
      });
      refreshData();
      break;
    default:
  }
};
const refreshData = () => {
  tableData.value = allTableData.filter(a =>
    state.checkedList.includes(a.kind)
  );
  gridOptions.data = toRaw(tableData.value);
};
let selectList = reactive([]);
const setSelect = row => {
  const checked = row.isSelect;
  console.log('选择', row, selectList);
};
const getColumns = async () => {
  let tableColumns = [
    { type: 'seq', title: ' ', minWidth: 50 },
    {
      field: 'isSelect',
      slots: { default: 'zg_default' },
      minWidth: 50,
      title: '选择',
    },
    { minWidth: 80, title: '编码', field: 'materialCode' },
    { field: 'type', title: '类别', minWidth: 50 },
    { field: 'materialName', title: '名称', minWidth: 100 },
    { field: 'specification', title: '规格型号', minWidth: 100 },
    { field: 'unit', title: '单位', minWidth: 50 },
    { field: 'totalNumber', title: '数量', minWidth: 50 },
    {
      title: '市场价',
      field: 'marketPrice',
      minWidth: 80,
    },
    { field: 'priceMarket', title: '不含税市场价', minWidth: 100 },
    { field: 'priceMarketTax', title: '含税市场价', minWidth: 100 },
    { field: 'taxRate', title: '税率', minWidth: 50 },
    {
      field: 'ifDonorMaterial',
      title: '供货方式',
      minWidth: 80,
      slots: { default: 'ifDonorMaterial_default' },
    },
    {
      field: 'ifProvisionalEstimate',
      title: '是否暂估',
      minWidth: 80,
      slots: { default: 'ifProvisionalEstimate_default' },
    },
  ];
  gridOptions.columns = tableColumns.filter(item => {
    const field = item.field;
    if (
      +store.deType === 12 &&
      [
        'taxRate',
        'priceMarketTax',
        'priceMarket',
        'priceMarketTaxTotal',
        'priceMarketTotal',
      ].includes(field)
    ) {
      // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
      return false;
    }
    if (+store.deType === 22) {
      if (['marketPrice', 'total', 'taxRemoval'].includes(field)) {
        // 市场价、市场价合价、除税系数22不显示
        return false;
      }
    }
    return true;
  });
};
const btnFun = item => {
  let bol = item.id === 'cancelSameMajor' ? false : true;
  switch (item.id) {
    case 'all':
    //选择全部
    case 'cancelSameMajor':
      //取消选择
      tableData.value.map(a => {
        a.isSelect = bol;
      });
      break;
    case 'material':
    //选择主材
    case 'device':
      //选择设备
      let target = item.id === 'material' ? getType[5] : getType[4];
      // target = getType[5];
      tableData.value.map(a => {
        if (a.type === target) a.isSelect = bol;
      });
      break;
    case 'diff':
      //选择全调差材料
      tableData.value.map(a => {
        if (+a.priceDifferenc !== 0) a.isSelect = true;
      });
      break;
  }
};

let tableData = ref([]);
let allTableData = reactive([]);
const getTableList = () => {
  let formData = {
    type: store.currentTreeInfo.levelType,
    kind: 0,
    unitIdList: [],
  };
  formData = getParamsData(formData);
  feePro.queryConstructRcjByDeId(formData).then(res => {
    console.log(res, 'queryConstructRcjByDeId', formData);
    if (res.status === 200 && res.result && res.result.length > 0) {
      // row.checkIsShow && !['人工费', '机械费'].includes(row.type)
      let list = checkBoxIsShow([...res.result]);
      list = list.filter(
        a =>
          a.checkIsShow &&
          !['人工费', '机械费'].includes(a.type) &&
          !(otherCodeList.includes(a.materialCode) && a.isBfh)
      );
      list.map(
        a =>
          (a.isSelect =
            +store.asideMenuCurrentInfo?.key === 8 && a.ifProvisionalEstimate
              ? true
              : false)
      );
      allTableData = [...list];
      refreshData();
    } else {
      gridOptions.data = [];
    }
  });
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
const checkBoxIsShow = list => {
  list.map(item => {
    //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
    if (item.markSum === 1 && [1, 2].includes(Number(item.levelMark))) {
      item.checkIsShow = false;
    } else {
      item.checkIsShow = true;
    }
  });
  return list;
};
//承包人-暂估一些api
const apiFunObj = {
  8: {
    apiKey: 'zgjRcjList',
    api: 'zgjRcjBatchInsert',
  },
  10: {
    apiKey: 'cbrRcjList',
    api: 'cbrRcjBatchInsert',
  },
};
// 确认
const handleOk = () => {
  let selectList = tableData.value.filter(a => a.isSelect);
  if (selectList.length === 0) {
    message.warning('请选择数据');
    return;
  }
  let apiData = getParamsData({
    [apiFunObj[+store.asideMenuCurrentInfo?.key]['apiKey']]:
      getFinally(selectList),
  });
  let apiName = apiFunObj[+store.asideMenuCurrentInfo?.key]['api'];
  console.log('handleOk', apiData, apiName);
  csProject[apiName](apiData)
    .then(res => {
      console.log('rcjToZgjRcj---res', apiData, res);
      //刷新上表格数据
      message.success('操作成功');
      emits('refresh');
      dialogVisible.value = false;
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
const getFinally = data => {
  return JSON.parse(JSON.stringify(data));
};
const cancel = (refresh = false) => {
  dialogVisible.value = false;
};
const init = () => {
  state.indeterminate = false;
  state.checkAll = true;
  state.checkedList = [2, 5, 4];
};
const open = k => {
  dialogVisible.value = true;
  init();
  getColumns();
  getTableList();
};
defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    // padding: 17px 22px !important;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .hearder {
    // display: flex;
    // margin: -5px 0px 10px;
    // justify-content: space-between;
    display: flex;
    margin: 0;
    padding-bottom: 10px;
    height: 35px;
    .btnList,
    .inputSearch {
      display: flex;
      width: 42%;
      justify-content: space-between;
      .ant-btn {
        font-size: 12px;
      }
    }
    .inputSearch {
      width: 20%;
      font-size: 12px;
      .ant-input {
        width: 100%;
        font-size: 12px;
        line-height: 22px;
        margin-right: 5px;
      }
    }
    .checkList {
      display: flex;
      // width: 31%;
      .ant-checkbox-wrapper,
      .ant-checkbox-group {
        .ant-checkbox-group-item {
          font-size: 12px;
          margin-right: 5px;
        }
        .ant-checkbox + span {
          padding-right: 5px;
          padding-left: 5px;
        }
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 0;
    height: 40px;
    padding-top: 10px;
    .ant-radio-group {
      width: 30%;
      .ant-radio-wrapper {
        font-size: 12px;
      }
    }
    .btn {
      width: 15%;
    }
  }
  .vxe-table .index-bg {
    background-color: #ffffff;
  }
  .vxe-table--body {
    border-collapse: collapse;
    // border: 2px solid #ffffff;
  }
}
</style>
