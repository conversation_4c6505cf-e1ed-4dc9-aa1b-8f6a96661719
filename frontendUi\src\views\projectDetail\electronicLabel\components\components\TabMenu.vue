<script setup>
import {reactive, ref, watch} from "vue";
const props = defineProps({
  tabContent:{
    type: [String, Number],
    default: '1'
  }
});
const emit = defineEmits(['getActiveKey']);
const obj = {
  '1':[
    {
      key: 'bdxx',
      tab: '标段信息(0)',
      levelType: ['1'],
    },
    {
      key: 'fbfx',
      tab: '分部分项(0)',
      levelType: ['3'],
    },
    {
      key: 'csxm',
      tab: '措施项目(0)',
      levelType: ['3'],
    },
    {
      key: 'qtxm',
      tab: '其它项目(0)',
      levelType: ['3'],
    },
    {
      key: 'zlje',
      tab: '暂列金额(0)',
      levelType: ['3'],
    },
    {
      key: 'zygczgj',
      tab: '专业工程暂估价(0)',
      levelType: ['3'],
    },
    {
      key: 'zcbfwf',
      tab: '总承包服务费(0)',
      levelType: ['3'],
    },
    {
      key: 'jrg',
      tab: '计日工(0)',
      levelType: ['3'],
    },
    {
      key: 'zgjcl',
      tab: '暂估价材料(0)',
      levelType: ['3'],
    },
    {
      key: 'fbrgyclhsb',
      tab: '发包人供应材料和设备(0)',
      levelType: ['3'],
    }
  ],
  '2':[
      { key: 'wppx', tab: '未匹配项',levelType: ['1','3'], }
  ]
};
const optionList = ref([]);
const activeKey = ref(null);
const tabsChange = (val) => {
  const selectTab = optionList.value.find(item => item.key === activeKey.value);
  if(selectTab){
    emit('getActiveKey', selectTab);
  }else{
    activeKey.value = optionList.value[0].key;
    emit('getActiveKey', optionList.value[0]);
  }
};
const setFilter = (treeActiveData) => {
  //处理在分部情况下， 切换是否需要清空的情况
  if(['2'].includes(String(treeActiveData.levelType))){
    return  null;
  }
  optionList.value = [];
  obj[props.tabContent].forEach((item) => {
    if(String(item.levelType).includes(String(treeActiveData.levelType))) {
      optionList.value.push(item);
    }
  });
  if(!optionList.value.length){
    console.warn('optionList.value=> 数组为空', optionList.value);
    return  null;
  }

  // 两种情况：1、如果activeKey 为空时，代表界面无选中项，则默认选中第一个；
  //         2、如果activeKey有值，则代表有选择项。但是当左边树切换时，activeKey存的值，是否在optionList.value能找到：如果找到直接emit；如果没找到则默认新的optionList第一个，并emit第一个。
  if(!!activeKey.value){
    const selectTab = optionList.value.find(item => item.key === activeKey.value);
    if(!!selectTab) {
      emit('getActiveKey', selectTab);
      return null;
    }
  }
  activeKey.value = optionList.value[0].key;
  emit('getActiveKey', optionList.value[0]);
}
watch(()=>props.tabContent,(o,n)=>{
  console.log(props.tabContent,n);
  if(props.tabContent == 2){
    optionList.value = obj[props.tabContent];
    activeKey.value =  optionList.value[0].key;
  }
},{
  immediate: true,
})
defineExpose({setFilter})
</script>

<template>
  <div class="selectTab">
    <a-radio-group v-model:value="activeKey"  @change="tabsChange"  size="small">
      <a-radio-button  :value="item.key" v-for="item of optionList">{{ item.tab }}</a-radio-button>
    </a-radio-group>
  </div>
</template>

<style lang="scss" scoped>
.selectTab {
  //height: 29px;
  background-color: #E7E7E7;
  .ant-radio-button-wrapper {
    font-size: 12px;
    background-color: #e7e7e7;
    border: none;
    box-shadow: none;
    color: #7C7C7C;
  }
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
    background-color: transparent;
  }
  .ant-radio-button-wrapper-checked {
    background-color: white;
    border: none;
    border-top: 2px solid #4786ff;
    color: black;
    box-sizing: content-box;
    &:hover {
      color: black;
    }
  }
}

</style>
