const {parentUpdateRcjRulesDetails, childUpdateRcjRulesDetails, updateRcjRulesDetails} = require("../rules/updateRcjRules");
const { ObjectUtils } = require('../../utils/ObjectUtils');


class ParentRcjUpdateHandler {
    constructor(ctx,upDateInfo) {
        this.ctx = ctx;
        this.unit = ctx.unit;
        this.rcj = this.unit.constructProjectRcjs.find(i=>i.sequenceNbr === this.ctx.rcjId);
        this.de = this.ctx.allData.getNodeById(this.rcj.deId);
        this.isParent = true;
        this.updateRule = updateRcjRulesDetails;
        this.upDateInfo = upDateInfo;
        this.ctx.changeDeList.push(this.rcj.deId);
    }
    async update() {
        const keys = Object.keys(this.upDateInfo);
        for (const key of keys) {
            this.oldValue = this.rcj[key];
            this.rcj[key] = this.upDateInfo[key]
            let fn  =this.updateRule[key];
            if('kind'==key){
                await this.handleKindUpdate(key);

            }
            if(fn){
                await fn(this);
            }
        }
        return this.rcj;
    }


    /**
     * 修改人材机仅修改类型影响定额类型 xhc 2024.12.3
     * @param newValue
     * @returns {Promise<void>}
     */
    async handleKindUpdate(key) {
        let {sequenceNbr,jointStandardRcj,rcjFlag} = this.de;

        if (rcjFlag == 1){
            return ;
        }

        //补充定额的jointStandardRcj 是undefined
        if(ObjectUtils.isEmpty(jointStandardRcj)){
            return ;
        }

        //父级人材机
        let parentRcj = this.unit.constructProjectRcjs.filter(k => k.deId == sequenceNbr);
        //内存数据与标准数据对比
        if(jointStandardRcj.split(',').length==parentRcj.length){

            //修改人材机仅修改类型影响定额类型 xhc 2024.12.3
            if (this.de.libraryCode == this.unit.mainDeLibrary){
                if(this.rcj.kindBackUp == this.rcj[key]){//修改人材机类型为原始类型
                    this.de.appendType = ['定']
                }else {
                    this.de.appendType = ['换'] //【换】
                }

            }else {
                if(this.rcj.kindBackUp == this.rcj[key]) {//修改人材机类型为原始类型
                    this.de.appendType= ['借']
                }else {
                    this.de.appendType= ['借换'] //【借换】
                }

            }
        }else {
            if (this.de.libraryCode == this.unit.mainDeLibrary){
                if(this.rcj.kindBackUp == this.rcj[key]) {//修改人材机类型为原始类型
                    this.de.appendType = ['调']
                }else {
                    this.de.appendType = ['调换']
                }
                //【调换】
            }else {
                if(this.rcj.kindBackUp == this.rcj[key]) {//修改人材机类型为原始类型
                    this.de.appendType= ['借调']
                }else {
                    this.de.appendType= ['借调换'] //【借调换】
                }

            }
        }
    }
}

class ChildRcjUpdateHandler {
    constructor(ctx,upDateInfo) {
        this.ctx = ctx;
        this.unit = ctx.unit;
        this.rcj = this.unit.rcjDetailList.find(i=>i.sequenceNbr === this.ctx.rcjId);
        this.isParent = false;
        this.updateRule = updateRcjRulesDetails;
        this.de = this.ctx.allData.getNodeById(this.rcj.deId);
        this.upDateInfo = upDateInfo;
        this.ctx.changeDeList.push(this.rcj.deId);
    }
    async update() {
        const keys = Object.keys(this.upDateInfo);
        for (const key of keys) {
            this.oldValue = this.rcj[key];
            this.rcj[key] = this.upDateInfo[key]
            let fn  =this.updateRule[key];
            if(fn){
                await fn(this);
            }
        }
        return this.rcj;
    }
}


module.exports = {
    ParentRcjUpdateHandler,ChildRcjUpdateHandler
}
