/*
 * @Descripttion: 
 */
import DecimalUtils from '@/utils/decimalUtils.js';
import { systemConfigStore } from '@/store/systemConfig.js';
export const useDecimalPoint = () => {
  const systemStore = systemConfigStore();
  const {rcjDetailAmount, rcjSummaryAmount, qdDeAmount, costPrice, rate} = systemStore.decimalConfig;
  const rcjDetailAmountFormat = (value, defaultVal = '') => {
    const val = DecimalUtils.toFixed(value,rcjDetailAmount)
    return val === '' ? defaultVal : val
  }
  const rcjSummaryAmountFormat = (value, defaultVal = '') => {
    const val = DecimalUtils.toFixed(value,rcjSummaryAmount)
    return val === '' ? defaultVal : val
  }
  const qdDeAmountFormat = (value, defaultVal = '') => {
    const val = DecimalUtils.toFixed(value,qdDeAmount)
    return val === '' ? defaultVal : val
  }
  const costPriceFormat = (value, defaultVal = '') => {
    const val = DecimalUtils.toFixed(value,costPrice)
    return val === '' ? defaultVal : val
  }
  const rateFormat = (value, defaultVal = '') => {
    const val = DecimalUtils.toFixed(value,rate)
    return val === '' ? defaultVal : val
  }
  return {
    rcjDetailAmountFormat,
    rcjSummaryAmountFormat,
    qdDeAmountFormat,
    costPriceFormat,
    rateFormat
  }
}