<!--
 * @Descripttion: 系统报表，应用到当前报表
 * @Author: 
 * @Date: 2024-12-20 14:33:26
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-26 11:01:58
-->
<template>
  <common-modal
    className="dialog-comm system-report-dialog"
    width="80%"
    v-model:modelValue="dialogVisible"
    title="系统报表"
    @cancel="cancel"
    @close="cancel"
    :destroyOnClose="true"
  >
    <div class="system-report-form">
      <a-button type="text" @click="applyMode" :disabled="applyDisabled"
        ><icon-font type="icon-yingyongdaodangqianbaobiao"></icon-font
        >应用到当前报表</a-button
      >
      <div class="content">
        <div class="aside">
          <sysSubAsideTree
            class="aside-tree"
            ref="subAsideTreeRef"
            @onPreview="previewClick"
            :storageName="storageName"
          ></sysSubAsideTree>
        </div>
        <div class="iframe-content">
          <img
            src="@/assets/img/data-null.png"
            alt=""
            class="noData"
            v-if="!previewData"
          />
          <iframe
            v-if="fileUrl && previewData"
            id="myIframe"
            ref="iframeRef"
            :src="fileUrl"
            style="width: 100%; height: 100%; border: 2px solid #e8e8e7"
          />
        </div>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { ref, defineAsyncComponent, nextTick, computed } from 'vue';
import csProject from '@/api/csProject';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import {
  globalData,
  removeLocalStorage,
  setLocalStorage,
} from './../reportFrom';
const emits = defineEmits(['refreshForm']);
const sysSubAsideTree = defineAsyncComponent(() =>
  import('./sysSubAsideTree.vue')
);
let dialogVisible = ref(false);
const cancel = () => {
  dialogVisible.value = false;
};
const open = () => {
  dialogVisible.value = true;
};

let previewData = ref(false); // 预览数据
const applyDisabled = computed(() => {
  return !previewData.value;
});
const fileUrl = ref();
const storageName = ref('systemReportForm');
let selectTreeInfo = ref(null);
/**
 * 点击预览
 * @param {*} hasData 是否有报表数据
 */
const previewClick = ({ hasData, currentTreeInfo }) => {
  fileUrl.value = null;
  console.log('🌶SystemReportForm.vue|81====>', hasData, currentTreeInfo);
  selectTreeInfo.value = currentTreeInfo;
  previewData.value = hasData;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html?storageName=${storageName.value}`;
  });
};

let selectReportRef = ref(null);
const applyMode = () => {
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '请确认是否进行应用，应用后原报表模版将被替换',
    confirm: () => {
      apply();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const apply = () => {
  const {
    constructObj: { constructId, singleId, unitId },
    itemLevel,
  } = JSON.parse(JSON.stringify(globalData.treeParams));
  const params = {
    constructId,
    singleId,
    unitId,
    lanMuName: selectTreeInfo.value?.lanMuName,
    itemLevel,
    jsonData: JSON.parse(
      JSON.stringify(selectTreeInfo.value?.excelDataTemplate)
    ),
    headLine: selectTreeInfo.value?.headLine,
    originHeadLine: globalData.headLine,
  };
  console.log('🌶SystemReportForm.vue|109====>', params);
  csProject.applySelectedTemplateToTarget(params).then(res => {
    console.log(res);
    if (res.code === 200) {
      emits('refreshForm', selectTreeInfo.value?.headLine);
      cancel();
      message.success('应用成功！');
    } else {
      message.error(res.message);
    }
  });
};
defineExpose({ open });
</script>
<style lang="scss">
.system-report-dialog .vxe-modal--content {
  padding: 2px 13px 13px !important;
}
</style>
<style lang="scss" scoped>
.system-report-form {
  .content {
    display: flex;
    height: 65vh;
    .aside {
      width: 250px;
      background: #f8fbff;
      border: 1px solid #dcdfe6;
      height: 100%;
      overflow: auto;
    }
    .iframe-content {
      position: relative;
      flex: 1;
      height: 100%;
      overflow: hidden;
      border: 1px solid #dcdfe6;
    }
  }
  .noData {
    position: absolute;
    width: 274px;
    height: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
