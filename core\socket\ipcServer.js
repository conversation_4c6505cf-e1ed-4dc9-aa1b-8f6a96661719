const debug = require('debug')('core:ipcServer');
const is = require('is-type-of');
const { ipcMain, app} = require('electron');
const path = require('path');
const fs = require('fs');
const globby = require('globby');
const Utils = require('../core/lib/utils');
const Wrap = require('../utils/wrap');
const Log = require('../log');
const {ParamUtils} = require("../core/lib/utils/ParamUtils");
const { memoize } = require('../../electron/utils');
const RedoCacheUtils = require('../redo/RedoCacheUtils');
// const format = require("string-template");
const {Snowflake} = require('../../electron/utils/Snowflake');
const { TargetLocationHasArrayError } = require('hyperformula');

class IpcServer {
  constructor (app) {
    this.app = app;
    this.register();
  }
  _isObject(variable){
    return typeof variable === "object" && variable !== null;
  }
  _isArray(arr){
    return Array.isArray(arr) && arr !== null;
  }

  _tryToGetConstructId(params,results){
    if(typeof params !== 'object' || params === null) return;
    let keys = Object.keys(params);
    for(let key of keys){
      if(key == 'constructId' || key == 'constructid'){
        results.push(params[key]);
      }

      if(this._isObject(params[key])){
        this._tryToGetConstructId(params[key],results);
      }
    }
  }
  _getParamInfo(func) {
    // 获取函数的参数名称和默认值
    const params = [];

    // 将函数转换为字符串，然后使用正则表达式解析参数
    const funcStr = func.toString();
    // const paramRegex = /(\w+)(?=\s*=[,)]*)(?:\s*=([,)]*))?/g;
    const paramRegex = /(?<=\().*?(?=\))/g;
    let match = paramRegex.exec(funcStr);

    if(match != null){
      for(let item of match){
        params.push(item);
      }
    }
    return params;
  }

  register () {
    const self = this;
    //增加redo列表缓存
    if(!self.app.redoCache)
      self.app.redoCache = new Map();

    // 遍历方法
    const files = Utils.filePatterns();

    //const directory = path.join(this.app.config.baseDir, 'controller');
    const arrfilepaths=[{name:"default",directory:path.join(this.app.config.baseDir, 'controller')}];
    //const filepaths = globby.sync(files, { cwd: directory });
    let buffer = fs.readFileSync(path.join(app.getAppPath(), 'packages', "worker.json"));
    let worker=JSON.parse(buffer.toString());
    if(worker.length>0){
      worker.forEach((item)=>{
        const workerPropaths = path.join(app.getAppPath(), 'packages', item,'controller');
        arrfilepaths.push({name:item,directory:workerPropaths});
      });
    }
    for (const {directory,name} of arrfilepaths) {
      const filepaths = globby.sync(files, { cwd: directory });
      for (const filepath of filepaths) {
        const fullpath = path.join(directory, filepath);
        if (!fs.statSync(fullpath).isFile()) continue;
        const properties = Wrap.getProperties(filepath, {caseStyle: 'lower'});
        let pathName = directory.split(/[/\\]/).slice(-1) + '.' + properties.join('.');
        if(name!="default"){
          pathName = directory.split(/[/\\]/).slice(-1) + '.'+name+ '.'+ properties.join('.');
        }
        let fileObj = Utils.loadFile(fullpath);
        const fns = {};
        // 为了统一，仅支持class文件
        if (is.class(fileObj) || Utils.isBytecodeClass(fileObj)) {
          let proto = fileObj.prototype;
          // 不遍历父类的方法
          //while (proto !== Object.prototype) {
          const keys = Object.getOwnPropertyNames(proto);
          for (const key of keys) {
            if (key === 'constructor') {
              continue;
            }
            const d = Object.getOwnPropertyDescriptor(proto, key);
            if (is.function(d.value) && !fns.hasOwnProperty(key)) {
              fns[key] = 1;
              //解析函数的参数值
              let params = this._getParamInfo(d.value)[0];
              let redo = {}
              params.split(',').forEach(item=>
              {
                if(item.trim().startsWith('redo')||item.trim().startsWith('checkRedo')){
                  if(item.trim().startsWith('redo')){
                    if(item.indexOf('=')>-1){
                      redo.templateStr = item.split('=')[1].replaceAll("\"","");
                    }

                  }
                  if(item.trim().startsWith('checkRedo')){
                    if(item.indexOf('=')>-1){
                      redo.checkTemplateStr = item.split('=')[1].replaceAll("\"","");
                    }
                  }
                  redo.type = 'add';
                  fns[key] = redo;
                }

                if(item.trim().startsWith('clearRedo')){
                  redo.type='clear';
                  fns[key] = redo;
                }
              }
              )
            }
          }
          //proto = Object.getPrototypeOf(proto);
          //}
        }

        debug('register class %s fns %j', pathName, fns);

        for (const key in fns) {
          let channel = pathName + '.' + key;
          debug('register channel %s', channel);
          if(fns[key] !== 1){
            self.app.redoCache.set(channel,fns[key])
          }
          const findFn = (c) => {
            try {
              // 找函数
              const cmd = c;
              let fn = null;
              if (is.string(cmd)) {
                const actions = cmd.split('.');
                let obj = this.app;
                actions.forEach(key => {
                  obj = obj[key];
                  if (!obj) throw new Error(`class or function '${key}' not exists`);
                });
                fn = obj;
              }
              if (!fn) throw new Error('function not exists');

              return fn;
            } catch (err) {
              Log.coreLogger.error('[core] [socket/IpcServer] throw error:', err);
            }
            return null;
          }
          const memoizeFindFn = memoize(findFn)

          // send/on 模型
          ipcMain.on(channel, async (event, params) => {
            const fn = memoizeFindFn(channel);
            const result = await fn.call(this.app, params, event);

            event.returnValue = result;
            event.reply(`${channel}`, result);
          });

          // invoke/handle 模型
          ipcMain.handle(channel, async (event, params) => {
            const fn = memoizeFindFn(channel);
            // constructId, singleId, unitId
            let commonRedoParam =  null;
            if (params) {
              commonRedoParam =  {
                "constructId":params.constructId?params.constructId:params.id,
                "singleId":params.singleId,
                "unitId":params.unitId?params.unitId:params.unitWorkId
              };
              if (params.unitId || params.unitWorkId){
                ParamUtils.setParam("commonParam", {
                  "constructId":params.constructId?params.constructId:params.id,
                  "singleId":params.singleId,
                  "unitId":params.unitId?params.unitId:params.unitWorkId
                });
              }

            }
            var startTime = performance.now();
            // console.log(channel);
            let redo = this.app.redoCache.get(channel);

            let errorChannelAnanotion = false;
            if(redo != null){
              //尝试从对象中获取
              if(commonRedoParam.constructId == undefined || commonRedoParam.constructId == null || commonRedoParam.constructId==''){
                let constructIds = [];
                this._tryToGetConstructId(params,constructIds);
                if(constructIds.length > 0){
                  commonRedoParam.constructId = constructIds[0];
                }
              }
              if(commonRedoParam.constructId == undefined || commonRedoParam.constructId == null || commonRedoParam.constructId == ''){
                errorChannelAnanotion = false;
              }else{
                errorChannelAnanotion = true;
              }
            }
            if(errorChannelAnanotion && redo != null && redo.type === 'add'){
              commonRedoParam.sequenceNbr = Snowflake.nextId();
              // commonRedoParam.name = format(redo.templateStr,params);
              commonRedoParam.name = redo.templateStr
              commonRedoParam.checkName = redo.checkTemplateStr || ''
              let redoCache = new RedoCacheUtils();
              //不能异步处理，否则channel与内存对象不对应
              redoCache.addRedoCache(commonRedoParam, channel);
               var endTime2 = performance.now()-startTime;
               console.log(`---redo调用----${channel}---str:${redo.templateStr}----cost:${endTime2}毫秒`);
            }
            const result = await fn.call(this.app, params, event);
            if(errorChannelAnanotion && redo != null ){
              if(redo.type === 'add')
                result.redo = commonRedoParam;
              else if(redo.type === 'clear'){
                let redoCache = new RedoCacheUtils();
                redoCache.clearRedoList(commonRedoParam);
              }
            }
            var endTime = performance.now();
            // 计算执行时间
            var executionTime = endTime - startTime;
            console.log(channel + '代码执行时间：', executionTime.toFixed(2), '毫秒');
            return result;
          });
        }
      }
    }

  }
}

module.exports = IpcServer;
