<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-04-19 11:27:26
-->
<template>
  <div class="table-content">
    <child-page-table
      :pageType="'zcbfwf'"
      :columnList="columnList"
    ></child-page-table>
    <!-- <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isHover: true, isCurrent: true }"
      :data="tableData"
      height="auto"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      ref="zcbfwfTable"
      @edit-closed="editClosedEvent"
      keep-source
      @current-change="currentChange"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :cell-class-name="selectedClassName"
      class="table-edit-common"
      @cell-click="useCellClickEvent"
    >
      <vxe-column
        field="dispNo"
        min-width="60"
        title="序号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="clear()"
            @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="fxName"
        min-width="180"
        title="项目名称"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="{ row }">
          <cell-textarea
            :clearable="false"
            v-model.trim="row.fxName"
            @blur="clear()"
            :maxlength="2000"
            placeholder="请输入项目名称"
            :textHeight="row.height"
          ></cell-textarea>
        </template>
      </vxe-column>
      <vxe-column
        field="amount"
        min-width="80"
        title="数量"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.amount"
            :maxlength="10"
            type="text"
            @blur="(row.amount = pureNumber(row.amount, 6)), clear()"
          ></vxe-input>
          <span v-else>
            {{ row.amount }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="xmje"
        min-width="100"
        title="项目价值"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row && row.dataType === 1">
            {{ row.xmje }}
          </span>
          <vxe-input
            v-if="row && row.dataType === 2"
            :maxlength="10"
            :clearable="false"
            v-model.trim="row.xmje"
            type="text"
            @blur="(row.xmje = pureNumber(row.xmje, 2)), clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="serviceContent"
        min-width="180"
        title="服务内容"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row && row.dataType === 1">
            {{ row.serviceContent }}
          </span>
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.serviceContent"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="rate"
        min-width="80"
        title="费率(%)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row && row.dataType === 1">
            {{ row.rate }}
          </span>
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.rate"
            :maxlength="10"
            type="text"
            @blur="(row.rate = pureNumber(row.rate, 4)), clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="fwje" min-width="100" title="金额"> </vxe-column>
      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-table> -->
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
const columnList = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'fxName',
    title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'fxName_edit' },
  },
  // {
  //   field: 'amount',
  //   title: '数量',
  //   minWidth: 80,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'amount_edit' },
  // },
  {
    field: 'xmje',
    title: '项目价值',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'xmje_edit' },
  },
  {
    field: 'serviceContent',
    title: '服务内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'service_edit' },
  },

  {
    field: 'rate',
    title: '费率(%)',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'rate_edit' },
  },

  {
    field: 'fwje',
    minWidth: 100,
    title: '金额',
  },
  {
    field: 'description',
    title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dec_edit' },
  },
];
// import FeeHeader from './FeeHeader.vue';
// import {
//   onMounted,
//   onUpdated,
//   ref,
//   watch,
//   reactive,
//   onActivated,
//   getCurrentInstance,
//   nextTick,
//   toRaw,
// } from 'vue';
// import { projectDetailStore } from '../../../../store/projectDetail';
// import csProject from '../../../../api/csProject';
// import qtxmCommon from './qtxmCommon';
// import { getUrl, pureNumber, sortAndlength } from '@/utils/index';
// import { useOtherProRaction } from '@/hooks/useOtherProRaction';
// const {
//   getOperateParams,
//   copyOperate,
//   disposeCurrentIndex,
//   operateParams,
//   getCurrentIndex,
//   msgInfo,
//   deleteOperate,
//   deleteModal,
//   menuConfigOptions,
//   pasteIsDisabled,
//   editCheckLength,
//   tabBtnIsValid,
// } = useOtherProRaction({ pageType: 'zcbfwf' });
// let $table; //全局定义
// import { message, Modal } from 'ant-design-vue';
// import { insetBus } from '@/hooks/insetBus';
// import { useCellClick } from '@/hooks/useCellClick';
// const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
//   useCellClick();
// const cxt = getCurrentInstance();
// const bus = cxt.appContext.config.globalProperties.$bus;
// const projectStore = projectDetailStore();
// let insertType = ref('');
// const zcbfwfTable = ref();
// let copyInfo = ref(''); //复制数据行
// let loading = ref(false);
// let tableData = ref([]);
// let deleteModel = ref(false);
// let deleteInfo = ref();
// let isCurrent = ref(null);
// const activeKey = ref(1);
// const clear = () => {
//   //清除编辑状态
//   $table.clearEdit();
// };
// const getOtherProjectZcbfwfList = (sequenceNbr = '') => {
//   loading.value = true;
//   let apiData = qtxmCommon.requestParams();
//   // tableData.value = [];
//   csProject.getOtherProjectZcbfwfList(apiData).then(res => {
//     if (res.status === 200) {
//       loading.value = false;
//       tableData.value = res.result;
//       if (sequenceNbr) {
//         getCurrentIndex($table, { sequenceNbr });
//       }
//       if (tableData.value && tableData.value.length > 0) {
//         isCurrent.value
//           ? $table.setCurrentRow(tableData.value[isCurrent.value])
//           : $table.setCurrentRow(tableData.value[0]);
//         insertType.value = $table.getCurrentRecord().dataType;
//       } else {
//         insertType.value = 0;
//       }
//       projectStore.SET_DATATYPE(insertType.value);
//       projectStore.isAutoPosition = false;
//     }
//   });
// };
// const editClosedEvent = ({ row, column }) => {
//   const field = column.field;
//   let value = row[field];
//   editCheckLength(row, column, $table);
//   //字符长度限制
//   if ($table.isUpdateByRow(row, field)) {
//     operate('update', row);
//   }
// };

// const menuConfig = reactive({
//   ...menuConfigOptions,
//   visibleMethod({ options, column, columnIndex, row, rowIndex }) {
//     if (!row) return;
//     //根据右键选择的数据设置插入数据行和标题行的置灰状态
//     pasteIsDisabled(row);
//     $table.setCurrentRow(row);
//     insertType.value = row.dataType;
//     projectStore.SET_DATATYPE(insertType.value);
//     return true;
//   },
// });

// const operate = (type, row) => {
//   getOperateParams(type, row, $table);
//   let apiData = toRaw(operateParams.value);
//   csProject.otherProjectServiceCost(apiData).then(res => {
//     if (res.status === 200) {
//       isCurrent.value = disposeCurrentIndex.value;
//       message.success(`${msgInfo.value}成功`);
//       getOtherProjectZcbfwfList();
//     }
//   });
// };
// const contextMenuClickEvent = ({ menu, row }) => {
//   menu.code === 'delete'
//     ? getCurrentIndex($table)
//     : getCurrentIndex($table, row);

//   switch (menu.code) {
//     case 'copy':
//       // 复制
//       copyOperate(row, $table);
//       message.success('复制成功');
//       break;
//     case 'delete':
//       // 删除
//       menuConfig.body.options[0][2].disabled = true;
//       deleteOperate(row, $table);
//       break;
//     case 'paste':
//       // 粘贴
//       row = { ...projectStore.otherProCopyInfo.copyInfo };
//       operate('paste', row);
//       break;
//     case 'addNull':
//       // 插入数据行
//       operate('insertData', row);
//       break;
//     case 'addTitle':
//       // 插入标题行
//       operate('insertTitle', row);
//       break;
//   }
// };

// watch(
//   () => projectStore.asideMenuCurrentInfo,
//   () => {
//     if (projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm05') {
//       getOtherProjectZcbfwfList();
//     }
//   }
// );
// watch(
//   () => deleteModal.value.isDelete,
//   () => {
//     if (
//       projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm05' &&
//       deleteModal.value.isDelete
//     ) {
//       operate('delete', deleteModal.value.deleteRow);
//     }
//   }
// );
// onActivated(() => {
//   insetBus(bus, projectStore.componentId, 'qtxmZcbfwf', async data => {
//     if (data.name === 'insert-op') {
//       if (!data.activeKind) {
//         tabBtnIsValid(data.options);
//       } else {
//         data.activeKind === '01'
//           ? operate('insertData')
//           : operate('insertTitle');
//       }
//     }
//     if (data.name === 'feeExcel') message.info('功能建设中...');
//   });
// });
// onMounted(() => {
//   getOtherProjectZcbfwfList();
//   window.addEventListener('keydown', keyDownOperate);
//   nextTick(() => {
//     $table = zcbfwfTable.value;
//   });
// });
// const keyDownOperate = event => {
//   let select = $table && $table.getCurrentRecord();
//   if (!select || projectStore.tabSelectName !== '其他项目') return;
//   if (!$table || projectStore.asideMenuCurrentInfo?.sequenceNbr !== 'qtxm05')
//     return;
//   if (event.ctrlKey && event.code == 'KeyC') {
//     copyOperate(select, $table);
//     message.success('复制成功');
//   }
//   let copyDataType =
//     projectStore.otherProCopyInfo &&
//     projectStore.otherProCopyInfo.copyInfo &&
//     projectStore.otherProCopyInfo.copyInfo[0]?.dataType;
//   let flag = true;
//   if (
//     !projectStore.otherProCopyInfo ||
//     (copyDataType === 1 && projectStore.dataType === 2)
//   ) {
//     flag = false;
//   }
//   if (event.ctrlKey && event.code == 'KeyV' && flag) {
//     operate('paste', projectStore.otherProCopyInfo.copyInfo);
//   } else if (event.ctrlKey && event.code == 'KeyV' && !flag) {
//     message.info('选中行不可粘贴');
//   }
//   // if (event.ctrlKey && event.code == 'KeyD') {
//   //   deleteItem(select);
//   // }
// };
// const currentChange = ({ row }) => {
//   insertType.value = row.dataType;
//   projectStore.SET_DATATYPE(insertType.value);
//   getCurrentIndex($table, row);
// };

// // 定位方法
// const posRow = sequenceNbr => {
//   getOtherProjectZcbfwfList(sequenceNbr);
// };

// defineExpose({
//   posRow,
// });
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
