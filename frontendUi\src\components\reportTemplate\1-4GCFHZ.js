/*
 * @Descripttion: 表1-4 单项工程费汇总表
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-12-16 15:57:58
 */

export const GCFHZ14BookData = [
  {
    name: '表1-4 单项工程费汇总表',
    deType: [12], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        Fpll_V: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            b: null,
          },
        },
        TK24pH: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            r: null,
            l: null,
            b: null,
          },
        },
        gBKP4g: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            b: null,
            t: null,
          },
        },
        zP_o4Y: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
            t: null,
          },
        },
        CFnLDk: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
            t: null,
          },
        },
        W0V2y6: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        _Eu47r: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            t: null,
          },
        },
        x7883e: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        '56EBYp': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            t: null,
          },
        },
        dkj39T: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            l: null,
            t: null,
          },
        },
        zs_GE4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        GcWSa9: {
          bd: {
            l: null,
            t: null,
          },
        },
        iG1mmP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '5qLeh-': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        xwOJW3: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        msMhj8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        NE3mzo: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        YOOZnv: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        icNRDT: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        PM67BZ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        umQWUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rvCwva: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            b: null,
          },
        },
        '9faeeX': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        N2tVSw: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '0-Salq': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`单项工程费汇总表`',
                t: 1,
                s: 'icNRDT',
                p: '',
                custom: {},
              },
              1: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              2: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              3: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              4: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              5: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              6: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              7: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{项目名称}',
                t: 1,
                s: 'rvCwva',
                p: '',
                custom: {},
              },
              1: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              2: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              3: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              4: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              5: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: '0-Salq',
                p: '',
                custom: {},
              },
              6: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              7: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '`金额(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              4: {
                v: '`其中：(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              1: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              4: {
                v: '`规费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                v: '`安全生产、文明施工费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            5: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '[JE]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              4: {
                v: '[GF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                v: '[AQSCWMSGF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            6: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '[JE]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              4: {
                v: '[GF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                v: '[AQSCWMSGF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            7: {
              0: {
                v: '`/`',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '`合    计(不含设备费)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: 'SUM([JE])',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              4: {
                v: 'SUM([GF])',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                v: 'SUM([AQSCWMSGF])',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            8: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            9: {
              0: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              1: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              2: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              3: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              4: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              5: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 9,
          columnCount: 8,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 29,
              startColumn: 0,
              endRow: 29,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 3,
              endRow: 29,
              endColumn: 5,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 6,
              endRow: 29,
              endColumn: 9,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 0,
              endRow: 9,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 3,
              endRow: 9,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 5,
              endRow: 9,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 3,
              endRow: 0,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 5,
              endRow: 0,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 0,
              endRow: 8,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 3,
              endRow: 8,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 5,
              endRow: 8,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 5,
              endRow: 2,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              startColumn: 1,
              endRow: 4,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 1,
              endRow: 5,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 1,
              endRow: 6,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 1,
              endRow: 7,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 3,
              endRow: 4,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 4,
              endRow: 3,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 6,
              endColumn: 7,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
              ia: 0,
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 42,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ia: 0,
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 30,
              hd: 0,
              field: '',
              parentName: '单项工程汇总',
              rowType: '明细行',
              dataSourceType: '单位工程汇总',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
              dataSourceType: '',
              ah: 33,
              ia: 0,
            },
          },
          columnData: {
            0: {
              w: 97,
              hd: 0,
            },
            1: {
              w: 195,
              hd: 0,
            },
            2: {
              w: 145,
              hd: 0,
            },
            3: {
              w: 136,
              hd: 0,
            },
            4: {
              w: 88,
              hd: 0,
            },
            5: {
              w: 65,
              hd: 0,
            },
            6: {
              w: 79,
              hd: 0,
            },
            7: {
              w: 100,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
      headLine: '',
    },
  },
  {
    name: '表1-4 单项工程费汇总表',
    deType: [22], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        Fpll_V: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            b: null,
          },
        },
        TK24pH: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            r: null,
            l: null,
            b: null,
          },
        },
        gBKP4g: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            b: null,
            t: null,
          },
        },
        zP_o4Y: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
            t: null,
          },
        },
        CFnLDk: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
            t: null,
          },
        },
        W0V2y6: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        _Eu47r: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            t: null,
          },
        },
        x7883e: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        '56EBYp': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            t: null,
          },
        },
        dkj39T: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            l: null,
            t: null,
          },
        },
        zs_GE4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        GcWSa9: {
          bd: {
            l: null,
            t: null,
          },
        },
        iG1mmP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '5qLeh-': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        xwOJW3: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
          n: null,
        },
        msMhj8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        NE3mzo: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        YOOZnv: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        icNRDT: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        PM67BZ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        umQWUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rvCwva: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            b: null,
          },
        },
        '9faeeX': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        N2tVSw: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '0-Salq': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        gKbVrP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        'R0-g-T': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        PE5Qtw: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        TXX3zV: {
          bd: {},
        },
        SDfzLT: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`单项工程费汇总表`',
                t: 1,
                s: 'icNRDT',
                p: '',
                custom: {},
              },
              1: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              2: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              3: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              4: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              5: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              6: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              7: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{项目名称}',
                t: 1,
                s: 'rvCwva',
                p: '',
                custom: {},
              },
              1: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              2: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              3: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              4: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              5: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: '0-Salq',
                p: '',
                custom: {},
              },
              6: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              7: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '`金额(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              4: {
                v: '`其中：(元)`',
                t: 1,
                s: 'gKbVrP',
                p: '',
                custom: {},
              },
              5: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              7: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              1: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'R0-g-T',
                v: '',
                p: '',
                t: 1,
              },
              4: {
                v: '`安全生产、文明施工费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            5: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '[JE]',
                t: 1,
                s: 'R0-g-T',
                p: '',
              },
              4: {
                v: '[AQSCWMSGF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            6: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '[JE]',
                t: 1,
                s: 'R0-g-T',
                p: '',
              },
              4: {
                v: '[AQSCWMSGF]',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            7: {
              0: {
                v: '`/`',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '`合    计(不含设备费)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: 'SUM([JE])',
                t: 1,
                s: 'R0-g-T',
                p: '',
              },
              4: {
                v: 'SUM([AQSCWMSGF])',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            8: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'SDfzLT',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                s: 'SDfzLT',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
              7: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            9: {
              0: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              1: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              2: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              3: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              4: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              5: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 9,
          columnCount: 8,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 29,
              startColumn: 0,
              endRow: 29,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 3,
              endRow: 29,
              endColumn: 5,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 6,
              endRow: 29,
              endColumn: 9,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 0,
              endRow: 9,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 3,
              endRow: 9,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 5,
              endRow: 9,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 3,
              endRow: 0,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 5,
              endRow: 0,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 0,
              endRow: 8,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 3,
              endRow: 8,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 5,
              endRow: 8,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 5,
              endRow: 2,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              startColumn: 1,
              endRow: 4,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 1,
              endRow: 5,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 1,
              endRow: 6,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 1,
              endRow: 7,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 3,
              endRow: 4,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 4,
              endRow: 3,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 4,
              startColumn: 4,
              endRow: 4,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 4,
              endRow: 5,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 4,
              endRow: 6,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 4,
              endRow: 7,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
              ia: 0,
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 42,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ia: 0,
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 30,
              hd: 0,
              field: '',
              parentName: '单项工程汇总',
              rowType: '明细行',
              dataSourceType: '单位工程汇总',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
              dataSourceType: '',
              ah: 33,
              ia: 0,
            },
          },
          columnData: {
            0: {
              w: 97,
              hd: 0,
            },
            1: {
              w: 195,
              hd: 0,
            },
            2: {
              w: 145,
              hd: 0,
            },
            3: {
              w: 136,
              hd: 0,
            },
            4: {
              w: 88,
              hd: 0,
            },
            5: {
              w: 65,
              hd: 0,
            },
            6: {
              w: 79,
              hd: 0,
            },
            7: {
              w: 100,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
      headLine: '',
    },
  },
  {
    name: '表1-4 单项工程费汇总表',
    deType: [12], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['工程量清单报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        Fpll_V: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            b: null,
          },
        },
        TK24pH: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            r: null,
            l: null,
            b: null,
          },
        },
        gBKP4g: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            b: null,
            t: null,
          },
        },
        zP_o4Y: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
            t: null,
          },
        },
        CFnLDk: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
            t: null,
          },
        },
        W0V2y6: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        _Eu47r: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            t: null,
          },
        },
        x7883e: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        '56EBYp': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            t: null,
          },
        },
        dkj39T: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            l: null,
            t: null,
          },
        },
        zs_GE4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        GcWSa9: {
          bd: {
            l: null,
            t: null,
          },
        },
        iG1mmP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '5qLeh-': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        xwOJW3: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        msMhj8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        NE3mzo: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        YOOZnv: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        icNRDT: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        PM67BZ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        umQWUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rvCwva: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            b: null,
          },
        },
        '9faeeX': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        N2tVSw: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '0-Salq': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`单项工程费汇总表`',
                t: 1,
                s: 'icNRDT',
                p: '',
                custom: {},
              },
              1: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              2: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              3: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              4: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              5: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              6: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              7: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{项目名称}',
                t: 1,
                s: 'rvCwva',
                p: '',
                custom: {},
              },
              1: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              2: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              3: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              4: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              5: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: '0-Salq',
                p: '',
                custom: {},
              },
              6: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              7: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '`金额(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              4: {
                v: '`其中：(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              1: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              4: {
                v: '`规费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                v: '`安全生产、文明施工费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            5: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            6: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            7: {
              0: {
                v: '`/`',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '`合    计(不含设备费)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              7: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            8: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            9: {
              0: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              1: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              2: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              3: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              4: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              5: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 9,
          columnCount: 8,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 29,
              startColumn: 0,
              endRow: 29,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 3,
              endRow: 29,
              endColumn: 5,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 6,
              endRow: 29,
              endColumn: 9,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 0,
              endRow: 9,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 3,
              endRow: 9,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 5,
              endRow: 9,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 3,
              endRow: 0,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 5,
              endRow: 0,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 0,
              endRow: 8,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 3,
              endRow: 8,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 5,
              endRow: 8,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 5,
              endRow: 2,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              startColumn: 1,
              endRow: 4,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 1,
              endRow: 5,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 1,
              endRow: 6,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 1,
              endRow: 7,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 3,
              endRow: 4,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 4,
              endRow: 3,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 4,
              endColumn: 5,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 4,
              endColumn: 5,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
              ia: 0,
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 42,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ia: 0,
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 30,
              hd: 0,
              field: '',
              parentName: '单项工程汇总',
              rowType: '明细行',
              dataSourceType: '单位工程汇总',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
              dataSourceType: '',
              ah: 33,
              ia: 0,
            },
          },
          columnData: {
            0: {
              w: 97,
              hd: 0,
            },
            1: {
              w: 195,
              hd: 0,
            },
            2: {
              w: 145,
              hd: 0,
            },
            3: {
              w: 136,
              hd: 0,
            },
            4: {
              w: 88,
              hd: 0,
            },
            5: {
              w: 65,
              hd: 0,
            },
            6: {
              w: 79,
              hd: 0,
            },
            7: {
              w: 100,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
      headLine: '',
    },
  },
  {
    name: '表1-4 单项工程费汇总表',
    deType: [22], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['工程量清单报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        Fpll_V: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            b: null,
          },
        },
        TK24pH: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            r: null,
            l: null,
            b: null,
          },
        },
        gBKP4g: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            b: null,
            t: null,
          },
        },
        zP_o4Y: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
            t: null,
          },
        },
        CFnLDk: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
            t: null,
          },
        },
        W0V2y6: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        _Eu47r: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            t: null,
          },
        },
        x7883e: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        '56EBYp': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            t: null,
          },
        },
        dkj39T: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            l: null,
            t: null,
          },
        },
        zs_GE4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        GcWSa9: {
          bd: {
            l: null,
            t: null,
          },
        },
        iG1mmP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '5qLeh-': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        xwOJW3: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
          n: null,
        },
        msMhj8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        NE3mzo: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        YOOZnv: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        icNRDT: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        PM67BZ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        umQWUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rvCwva: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            b: null,
          },
        },
        '9faeeX': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        N2tVSw: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '0-Salq': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        gKbVrP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        'R0-g-T': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        PE5Qtw: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        TXX3zV: {
          bd: {},
        },
        SDfzLT: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              5: {
                s: 'msMhj8',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`单项工程费汇总表`',
                t: 1,
                s: 'icNRDT',
                p: '',
                custom: {},
              },
              1: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              2: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              3: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              4: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              5: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              6: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              7: {
                s: 'icNRDT',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{项目名称}',
                t: 1,
                s: 'rvCwva',
                p: '',
                custom: {},
              },
              1: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              2: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              3: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              4: {
                s: 'rvCwva',
                v: '',
                p: '',
              },
              5: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: '0-Salq',
                p: '',
                custom: {},
              },
              6: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              7: {
                s: '0-Salq',
                v: '',
                p: '',
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                v: '`金额(元)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              4: {
                v: '`其中：(元)`',
                t: 1,
                s: 'gKbVrP',
                p: '',
                custom: {},
              },
              5: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              7: {
                s: 'gKbVrP',
                v: '',
                p: '',
                t: 1,
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              1: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'R0-g-T',
                v: '',
                p: '',
                t: 1,
              },
              4: {
                v: '`安全生产、文明施工费`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            5: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'R0-g-T',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            6: {
              0: {
                v: '[XH]',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '[MC]+`合计`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'R0-g-T',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            7: {
              0: {
                v: '`/`',
                t: 1,
                s: 'xwOJW3',
                p: '',
              },
              1: {
                v: '`合    计(不含设备费)`',
                t: 1,
                s: 'xwOJW3',
                p: '',
                custom: {},
              },
              2: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              3: {
                s: 'R0-g-T',
                v: '',
                p: '',
              },
              4: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              5: {
                s: 'xwOJW3',
                v: '',
                p: '',
              },
              6: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              7: {
                s: 'PE5Qtw',
                v: '',
                p: '',
              },
              8: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
            },
            8: {
              0: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              1: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              2: {
                s: 'msMhj8',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'msMhj8',
                p: '',
              },
              4: {
                s: 'SDfzLT',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                s: 'SDfzLT',
                v: '',
                t: 1,
                p: '',
              },
              6: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
              7: {
                s: 'TXX3zV',
                v: '',
                p: '',
                t: 1,
              },
              8: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
            9: {
              0: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              1: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              2: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              3: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              4: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              5: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              6: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
              7: {
                s: 'GcWSa9',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 9,
          columnCount: 8,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 29,
              startColumn: 0,
              endRow: 29,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 3,
              endRow: 29,
              endColumn: 5,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 29,
              startColumn: 6,
              endRow: 29,
              endColumn: 9,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 0,
              endRow: 9,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 3,
              endRow: 9,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 9,
              startColumn: 5,
              endRow: 9,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 3,
              endRow: 0,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 5,
              endRow: 0,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 0,
              endRow: 8,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 3,
              endRow: 8,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 8,
              startColumn: 5,
              endRow: 8,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 5,
              endRow: 2,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 4,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              startColumn: 1,
              endRow: 4,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 1,
              endRow: 5,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 1,
              endRow: 6,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 1,
              endRow: 7,
              endColumn: 2,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 3,
              endRow: 4,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 3,
              startColumn: 4,
              endRow: 3,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 4,
              startColumn: 4,
              endRow: 4,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 5,
              startColumn: 4,
              endRow: 5,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 6,
              startColumn: 4,
              endRow: 6,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 7,
              startColumn: 4,
              endRow: 7,
              endColumn: 7,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
              ia: 0,
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 42,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '细节标题行',
              dataSourceType: '单项工程汇总',
              ia: 0,
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 30,
              hd: 0,
              field: '',
              parentName: '单项工程汇总',
              rowType: '明细行',
              dataSourceType: '单位工程汇总',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '单项工程汇总',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
              dataSourceType: '',
              ah: 33,
              ia: 0,
            },
          },
          columnData: {
            0: {
              w: 97,
              hd: 0,
            },
            1: {
              w: 195,
              hd: 0,
            },
            2: {
              w: 145,
              hd: 0,
            },
            3: {
              w: 136,
              hd: 0,
            },
            4: {
              w: 88,
              hd: 0,
            },
            5: {
              w: 65,
              hd: 0,
            },
            6: {
              w: 79,
              hd: 0,
            },
            7: {
              w: 100,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
      headLine: '',
    },
  },
];
