<!--
 * @Descripttion: 主材价格设置弹框
 * @Author: liuxia
 * @Date: 2023-11-29 11:09:54
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-28 16:40:58
-->
<template>
  <div class="set-main-material">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.materialVisible"
      :title="title"
      :mask="true"
      :lockView="false"
      :lockScroll="false"
      width="900px"
      max-height="70%"
      @close="close"
    >
      <div class="content">
        <vxe-table
          ref="vexTable"
          :data="props.mainMaterialTableData"
          show-overflow
          keep-source
          :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
          :column-config="{ resizable: true }"
          height="auto"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
          :cell-class-name="selectedClassName"
          class="table-scrollbar table-edit-common"
          @cell-click="useCellClickEvent"
          @keydown="vxeTableKeydown"
        >
          <vxe-column width="60" title="序号">
            <template #default="{ row, $rowIndex }">
              {{ $rowIndex + 1 }}
            </template>
          </vxe-column>
          <vxe-column title="编码" field="materialCode">
            <template #default="{ row }">
              {{ row.materialCode }}
            </template>
          </vxe-column>
          <vxe-column
            title="名称"
            field="materialName"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              {{ row.materialName }}
            </template>
            <template #edit="{ row }">
              <vxe-input
                v-model="row.materialName"
                type="text"
                placeholder="请输入名称"
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column
            title="规格型号"
            field="specification"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              {{ row.specification }}
            </template>
            <template #edit="{ row }">
              <vxe-input
                v-model="row.specification"
                type="text"
                placeholder="请输入规格型号"
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column title="单位" field="unit" :edit-render="{ autofocus: '.vxe-input--inner' }">
            <template #default="{ row }">
              {{ row.unit }}
            </template>
            <template #edit="{ row }">
              <vxeTableEditSelect
                :transfer="true"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    row.unit = newValue;
                  }
                "
              ></vxeTableEditSelect>
            </template>
          </vxe-column>
          <vxe-column
            title="消耗量"
            field="resQty"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              {{ row.resQty }}
            </template>
            <template #edit="{ row }">
              <vxe-input v-model="row.resQty" type="text" placeholder="请输入市场价"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column
            :title="
              projectStore.deStandardReleaseYear === '12'
                ? '市场价'
                : projectStore.taxMade === 1
                  ? '不含税市场价'
                  : '含税市场价'
            "
            field="marketPrice"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #edit="{ row }">
              <vxe-input
                v-model="row.marketPrice"
                type="text"
                placeholder="请输入市场价"
                @keyup="
                  row.marketPrice = (row.marketPrice.match(/\d{0,8}(\.\d{0,2}|100)?/) || [''])[0]
                "
              ></vxe-input>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-btn-list">
        <vxe-checkbox
          v-model="mainRcjShowFlag"
          :checked-value="true"
          :unchecked-value="false"
          @change="mainRcjShowFlagColl"
          >不再显示该窗口</vxe-checkbox
        >
        <a-button type="primary" @click="handleOk" :loading="loading">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import api from '@/api/projectDetail';
import { useCellClick } from '@/hooks/useCellClick.js';
import { useVxeTableKeyDown } from '@/hooks/useVxeTableKeyDown';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { ref, watch } from 'vue';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } = useCellClick();
const { vxeTableKeydown } = useVxeTableKeyDown();
const props = defineProps(['materialVisible', 'mainMaterialTableData', 'currentInfo', 'type']);
const emits = defineEmits(['setUpdate', 'update:materialVisible']);
const projectStore = projectDetailStore();

const loading = ref(false);
const title = ref('');
let mainRcjShowFlag = ref(false);

watch(
  () => props.materialVisible,
  () => {
    if (props.currentInfo) {
      title.value = (props.currentInfo?.bdName || props.currentInfo?.fxName) + ' 主材价格设置';
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const close = () => {
  emits('setUpdate', 1);
};

const handleOk = () => {
  loading.value = true;
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    rcjList: JSON.parse(JSON.stringify(props.mainMaterialTableData)),
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  console.log('OK参数', apiData);
  api.zcMarketPriceBatchSave(apiData).then(res => {
    console.log('返回结果', res);
    if (res.status === 200 && res.result) {
      loading.value = false;
      message.success('保存成功');
      emits('setUpdate', 2);
    }
  });
};

// 设置主材
const mainRcjShowFlagColl = () => {
  let changeParams = {
    crdezszycljgsztk: !mainRcjShowFlag.value,
  };
  projectStore
    .SET_GLOBAL_CONFIG(projectStore.currentTreeGroupInfo?.constructId, changeParams)
    .then(res => {
      if (res.result) {
        message.success('设置成功');
      } else {
        message.error('设置失败');
      }
    });

  // let apiData = {
  //   constructId: projectStore.currentTreeGroupInfo?.constructId,
  //   mainRcjShowFlag: !mainRcjShowFlag.value,
  // };
  // api.mainRcjShowFlagColl(apiData).then(res => {
  //   if (res.status === 200 && res.result) {
  //     message.success('设置成功');
  //     queryConstructProjectMessageColl();
  //   } else {
  //     message.error('设置失败');
  //   }
  // });
};

// 获取项目数据
// const queryConstructProjectMessageColl = () => {
//   let apiData = {
//     constructId: projectStore.currentTreeGroupInfo?.constructId,
//   };
//   api.queryConstructProjectMessageColl(apiData).then(res => {
//     console.log('ccccccccccccccccc', res);
//     if (res.status === 200 && res.result) {
//       projectStore.SET_GLOBAL_SETTING_INFO({
//         mainRcjShowFlag: res.result.mainRcjShowFlag,
//         standardConversionShowFlag: res.result.standardConversionShowFlag,
//       });
//     }
//   });
// };
</script>

<style lang="scss" scoped>
.footer-btn-list {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
}
</style>
