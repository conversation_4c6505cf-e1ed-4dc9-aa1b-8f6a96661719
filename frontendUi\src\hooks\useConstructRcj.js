import { ref } from 'vue';
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode';
export const useConstructRcj = ({
  pageType = 'fbfx',
  $table,
  selectState,
  bcRcjCallback = () => {},
  refreshList = () => {},
}) => {
  const projectStore = projectDetailStore();
  const codeMap = {
    fbfx: 'bdCode',
    csxm: 'fxCode',
  };
  const codeField = codeMap[pageType] || 'materialCode';
  /**
   * 人材机明细修改
   * @param {*} row
   * @param {*} field
   * @param {*} rcjParam
   * @returns
   */
  const updateConstructRcj = (
    row,
    field,
    rcjParam = null,
  ) => {
    let constructProjectRcj = {};
    if (field) {
      if (field === 'materialCode') {
        isRcjCodeMainQuotaLibrary(field, row);
        return;
      }
      constructProjectRcj = { [field]: row[field] };
      if (field === 'marketPrice') {
        // 主材设备9495类型单价添加公式计算字段，marketPrice是映射过的字段名，
        // 对应的公式字段还是之前zjfPrice的公式字段zjfPriceFormula
        let marketPrice = row[field];
        try {
          const runResult = new Function(`return ${row['zjfPriceFormula']}`);
          if (isFinite(runResult())) {
            marketPrice = runResult();
          }
        } catch (error) {}
        constructProjectRcj = {
          marketPrice,
          zjfPriceFormula: row['zjfPriceFormula'], // 后端要求使用原来的字段名zjfPriceFormula，不需要映射

        }
      }
      if (field === 'type') {
        let value=1
        switch (row[field])  {
          case '人工费':
            value=1
            break;
          case '材料费':
            value=2
            break;
          case '机械费':
            value=3
            break;
          case '设备费':
            value=4
            break;
          case '主材费':
            value=5
            break;
          case '商砼':
            value=6
            break;
          case '砼':
            value=7
            break;
          case '浆':
            value=8
            break;
          case '商浆':
            value=9
            break;
          case '配比':
            value=10
            break;
          default:
        }
        constructProjectRcj.kind = value
      }
    } else {
      constructProjectRcj = rcjParam;
    }
    projectStore.SET_GLOBAL_LOADING({
      loading: true,
      info: '设置中，请稍后...',
    });
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      sequenceNbr: row.sequenceNbr,
      type: !row.rcjDetailsDTOs ? 2 : 1,
      libraryCode: row.libraryCode,
      constructProjectRcj,
      pageType: pageType,
    };
    console.log('修改人材机明细数据参数222', apiData);
    api
      .updateConstructRcj(apiData)
      .then(res => {
        if (res.status === 200 && res.result) {
          console.log('res', res);
          message.success('修改成功');
          refreshList()
        }
      })
      .finally(() => {
        projectStore.SET_GLOBAL_LOADING({
          loading: false,
          info: '设置中，请稍后...',
        });
      });
  };
  // 判断输入的材料编码是否与主定额库编码相同
  const isRcjCodeMainQuotaLibrary = (field, row) => {
    if (field !== 'materialCode') return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: row.materialCode,
    };
    api.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      console.log('难道是这个么', res);
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          if (
            (!row.rcjDetailsDTOs && res.result.levelMark === 0) ||
            row.rcjDetailsDTOs
          ) {
            updateBjqRcjReplaceData(row);
          } else {
            infoMode.show({
              isSureModal: true,
              iconType: 'icon-querenshanchu',
              infoText: '配合比材料下不允许增加配合比材料',
              confirm: () => {
                infoMode.hide();
                $table.value.revertData(row, codeField);
                // row[codeField] = row.originalMaterialCode;
              },
            });
          }
        } else {
          isStandardRcj(row);
        }
      }
    });
  };

  // 分部分项 措施项目 替换编辑区的人材机数据
  const updateBjqRcjReplaceData = row => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(row)),
      code: row.materialCode,
      region: 1,
    };
    console.log('明细区替换人材机', apiData);
    api.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('人材机替换成功');
        // rcjVisible.value = false;
        refreshList();
      }
    });
  };

  // 判断输入的材料编码是否标准人材机数据
  const isStandardRcj = row => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: row.materialCode,
    };
    api.isStandardRcj(apiData).then(res => {
      console.log('================人材机是否是标准数据', res);
      if (res.status === 200) {
        if (res.result) {
          if (
            (!row.rcjDetailsDTOs && res.result.levelMark === 0) ||
            row.rcjDetailsDTOs
          ) {
            updateBjqRcjReplaceData(res.result);
          } else {
            infoMode.show({
              isSureModal: true,
              iconType: 'icon-querenshanchu',
              infoText: '配合比材料下不允许增加配合比材料',
              confirm: () => {
                infoMode.hide();
                $table.value.revertData(row, codeField);
                // row[codeField] = row.originalMaterialCode;
              },
            });
          }
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '主定额库不存在该材料编码,是否补充人材机？',
            confirm: () => {
              // rcjVisible.value = true;
              // bdCode.value = code;
              bcRcjCallback(row);
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              $table.value.revertData(row, codeField);
              // row[codeField] = row.originalMaterialCode;
            },
          });
        }
      }
    });
  };

  let rcjIndexVisible = ref(false);
  let rcjIndexLoading = ref(false);
  const addChildrenRcjData = (row, currentInfo) => {
    rcjIndexLoading.value = true;
    row.isSupplement = 0;
    let apiData = {
      rcjDetail: JSON.parse(JSON.stringify(row)),
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      sequenceNbr: currentInfo?.parentId,
      pointLine: JSON.parse(JSON.stringify(currentInfo)),
      de: JSON.parse(JSON.stringify(currentInfo?.customParent)),
    };
    console.log('人材机配比增加参数', apiData);
    api
      .addChildrenRcjData(apiData)
      .then(res => {
        if (res.status === 200 && res.result) {
          message.success('插入成功');
          console.log('人材机插入结果', res.result);
          rcjIndexLoading.value = false;
          // 插入得是否主材设备，不是则定位到定额
          const posId = ![94,95].includes(res.result?.kind) ? currentInfo?.sequenceNbr : res.result.sequenceNbr;
          refreshList('Refresh',posId);
        }
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        rcjIndexLoading.value = false;
      });
  };
  const retailAreaRcjReplace = (row, currentInfo) => {
    rcjIndexLoading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      replaceLine: JSON.parse(JSON.stringify(row)),
      selectLine: JSON.parse(JSON.stringify(currentInfo)),
      conversionCoefficient: row.conversionCoefficient,
      de: JSON.parse(JSON.stringify(currentInfo?.customParent)),
    };
    console.log('新替换', apiData);
    api.retailAreaRcjReplace(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('替换成功');
        rcjIndexLoading.value = false;
        // 插入得是否主材设备，不是则定位到定额
        const posId = ![94,95].includes(res.result?.kind) ? currentInfo?.customParent?.sequenceNbr : res.result.sequenceNbr;
        console.log('新替换', res.result, posId);
        refreshList('Refresh',posId);
        rcjIndexVisible.value = false;
      }
    });
  };

  /**
   * 人材机批量删除
   * @param {*} rcjIds
   */
  const batchDeleteRcj = (rcjIds, callBack) => {
    const params = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      rcjList: JSON.parse(JSON.stringify(rcjIds))
    }
    console.log(params)
    api.batchDeleteRcj(params).then(res => {
      if (res.status === 200 && res.result) {
        message.success('删除成功');
        callBack();
        refreshList();
      }
    })
  }
  return {
    updateConstructRcj,
    rcjIndexVisible,
    rcjIndexLoading,
    addChildrenRcjData,
    retailAreaRcjReplace,
    batchDeleteRcj
  };
};
