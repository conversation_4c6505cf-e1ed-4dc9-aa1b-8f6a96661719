<!--
 * @Descripttion: 输入框，表格下拉
 * @Author: sunchen
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: liuxia
 * @LastEditTime: 2024-12-10 17:02:59
-->
<template>
  <vxe-pulldown
    ref="pulldownRef"
    class="pulldown-wrap"
    v-model="pulldown.visible"
    :placement="placement"
    transfer="true"
  >
    <template #default>
      <div class="pulldown-wraps">
        <vxe-input
          autofocus
          v-model.trim="props.ruleInfo"
          ref="vxeInputRef"
          :maxlength="50"
          :clearable="false"
          placeholder="请输入或下拉选择内容"
          @blur="blur"
        >
          <template #suffix>
            <i
              style="cursor: pointer"
              class="vxe-icon-caret-down icons"
              @click="focusEvent"
            ></i>
          </template>
        </vxe-input>
      </div>
    </template>
    <template #dropdown>
      <div class="editTable">
        <div class="chapter-info">
          <div class="my-dropdown">
            <div class="my-dropdown-wrap">
              <span class="title">章节选择</span>
              <vxe-select v-model="props.filedValue" @change="selectChange">
                <vxe-option
                  v-for="item in props.groupTypeList"
                  :key="item.libraryName"
                  :value="item.libraryCode"
                  :label="item.libraryName"
                ></vxe-option>
              </vxe-select>
              <CloseOutlined
                @click="cancel"
                class="out-icon"
                :style="{ fontSize: '16px', color: '#888888' }"
              />
            </div>
            <div class="tree-list">
              <vxe-table
                ref="tableRef"
                :column-config="{ resizable: true }"
                :tree-config="{
                  rowField: 'details',
                  children: 'childrenList',
                  expandAll: true,
                }"
                :data="props.treeData"
                border="none"
                :show-header="false"
                :row-config="{
                  isHover: true,
                  isCurrent: true,
                }"
                align="left"
                :scroll-x="{
                  enabled: true,
                }"
                height="auto"
              >
                <vxe-column tree-node show-overflow>
                  <template #default="{ row }">
                    <span class="name" @click="selectEvent(row)">{{
                      handleName(row)
                    }}</span>
                  </template></vxe-column
                >
              </vxe-table>
              <!--              <a-tree-->
              <!--                :tree-data="props.treeData"-->
              <!--                :expandedKeys="expandedKeys"-->
              <!--                v-model:selectedKeys="selectedKeys"-->
              <!--                :field-names="{-->
              <!--                  title: 'details',-->
              <!--                  children: 'childrenList',-->
              <!--                  key: 'details',-->
              <!--                }"-->
              <!--                :defaultExpandAll="true"-->
              <!--                @select="selectEvent"-->
              <!--              >-->
              <!--                <template #switcherIcon="{ switcherCls }"-->
              <!--                  ><down-outlined :class="switcherCls"-->
              <!--                /></template>-->
              <!--                <template #title="data">-->
              <!--                  <a-tooltip placement="rightTop">-->
              <!--                    <template #title>{{ handleName(data) }}</template>-->
              <!--                    <span class="ellipsis"> {{ handleName(data) }}</span>-->
              <!--                  </a-tooltip>-->
              <!--                </template>-->
              <!--              </a-tree>-->
            </div>
          </div>
        </div>
      </div>
    </template>
  </vxe-pulldown>
</template>
<script setup>
import { nextTick, reactive, ref, watch } from 'vue';
import { CloseOutlined, DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail.js';
const emits = defineEmits(['update:filedValue', 'showChapter']);
const props = defineProps({
  filedValue: {
    required: true,
    type: String,
  },
  placement: {
    required: false,
    type: String,
    default: 'top',
  },
  ruleInfo: {
    type: String,
    default: '',
  },
  groupTypeList: {
    type: Array,
    default: () => [],
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  ruleRcjCode: {
    type: String,
    default: '',
  },
  currentRcjLibraryCode: {
    type: String,
    default: '',
  },
});
const modeValues = ref('');
const vxeInputRef = ref(null);
const tableRef = ref(null);

let expandedKeys = ref([]);
const selectedKeys = ref([]);
modeValues.value = props.filedValue;
const projectStore = projectDetailStore();

nextTick(() => {
  console.log('props.filedValue', props.filedValue);
  vxeInputRef.value?.focus();
});

watch(
  () => props.treeData,
  () => {
    selectedKeys.value = [];
    expandedKeys.value = [];
    if (props.treeData.length > 0) {
      props.treeData.forEach(item => {
        // expandedKeys.value.push(item.details);
        item.childrenList.forEach(child => {
          if (
            child.libraryCode === props.currentRcjLibraryCode &&
            child.detailsCode === props.ruleRcjCode
          ) {
            //selectedKeys.value.push(child.details);
            const $table = tableRef.value;
            if ($table) {
              setTimeout(() => {
                expandAllEvent();
                $table.scrollToRow(child);
                $table.setCurrentRow(child);
              }, 200);
            }
          }
        });
      });
    }
  }
);
const pulldown = reactive({
  visible: false,
  values: '',
});

const focusEvent = () => {
  pulldown.visible = !pulldown.visible;
  emits('showChapter');
};
const blur = () => {
  emits('update:filedValue', props.filedValue);
  pulldown.visible = false;
};

const handleName = data => {
  const { details, detailsCode, specification } = data;
  return `${detailsCode}  ${details} ${
    projectStore.deStandardReleaseYear === '12' ? specification || '' : ''
  }`;
};

const expandAllEvent = () => {
  const $table = tableRef.value;
  if ($table) {
    $table.setAllTreeExpand(true);
  }
};
/**
 * 树结构选择
 * @param {} item
 */
const selectEvent = row => {
  console.log('🚀 ~ file: index.vue:109 ~ selectEvent ~ item:', row);
  if (row.childrenList && row.childrenList.length > 0) {
    expandAllEvent();
  } else {
    emits('selectInfo', row);
    emits('update:visible', false);
    cancel();
  }
};

const selectChange = (e, $event) => {
  emits('update:filedValue', e.value);
  emits('selectChange', e.value);
};

const cancel = () => {
  pulldown.visible = false;
  emits('cancel');
};
</script>

<style lang="scss" scoped>
.pulldown-wrap {
  z-index: 999999;
  width: 100%;
  ::v-deep(.vxe-input) {
    width: 98%;
  }
}
.input-wrap-dropdown {
  max-height: 40vh;
  z-index: 9999;
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
  .list-item {
    padding: 5px 4px;
    &:hover {
      background-color: azure;
      cursor: pointer;
    }
  }
}
.editTable {
  width: 500px;
  height: 400px;
  border: 2px solid #409eff;
  padding: 5px;
}
.vxe-modal--content {
  padding: 0;
}
.pulldown-wrap {
  width: 100%;
}
.my-dropdown {
  max-height: 50vh;
  width: 100%;
  height: 100%;
  .my-dropdown-wrap {
    position: relative;
    display: flex;
    align-items: center;
    padding: 2px 14px;
    background: rgba(214, 214, 214, 0.39);
    .title {
      margin-left: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
    .chapter-select {
      width: 300px;
      margin-left: 35px;
    }
    .out-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .tree-list {
    height: 350px;
    overflow-y: hidden;
    &:hover {
      overflow-y: auto;
    }
  }
}
.name {
  cursor: pointer;
}
</style>
