/*
 * @Descripttion: 表1-9 其他项目清单与计价表
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-12-16 14:38:31
 */

export const QTXM19BookData = [
  {
    name: '表1-9 其他项目清单与计价表',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        kJ66Ef: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ArRIyI: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '7tGYlm': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        SsagpX: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        fgkvbf: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '8SPpMY': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '-dwAwy': {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '2aFP8r': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        kvJCu3: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        NaHwuW: {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        bfITOz: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '7GJLOe': {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        hHkIyp: {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '9leVoS': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        _OTDvu: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        l0ddfj: {
          bd: {
            l: null,
            t: null,
          },
        },
        k02dan: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        VkRi5j: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        YuXRg4: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        AOp4bI: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        T07rvv: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ey5O3H: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        wvpkVV: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        D000zA: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        OeU1SF: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        B43KY6: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        VxHV_O: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ix1m_k: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '0hkq56': {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '4g_xR8': {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        N2xf_e: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        dyr2sf: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        LOLbqo: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        aUvkTT: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '30Gtx1': {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '15uVvE': {
          bd: {
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '2DTLNR': {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        lbWUPC: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        bp19pG: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        a9Cz6q: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
        },
        F6Ch1m: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '4gFHC_': {
          bd: {
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        JXj7rd: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        uawV7s: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        QWtcqT: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        xDp71G: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '9ysczg': {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        UyqoYj: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        DRu9e4: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        NsDgCR: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        hwHVeK: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        wG7n7H: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        M9iyos: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        osL2Ej: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        MHjJk8: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '46oSUa': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
        },
        Q3kYlQ: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        zH2s7e: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        nyd2RT: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        'RO3R-9': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '-Cmxo7': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        IyklH5: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        kD28gr: {
          bd: {
            b: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        nqOWTI: {
          bd: {
            b: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        TC3pp1: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '0akikc': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        u3DAI0: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '1Bm3Yh': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        htP7m8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        Bx8kXx: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        ybcgyE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        v6_RA4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        'Qt3-wz': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        NHJe26: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        qCnAAF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        MNbEUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        MlTCBM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        EbUSCP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        IkweBz: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            l: null,
          },
        },
        'C-8IUf': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        R9bqZ1: {
          bd: {
            l: null,
          },
        },
        DMXsTr: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        eLulFi: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        vin1Ex: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ilhOM8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        oL8Tcq: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        Pz8Nlq: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 3,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        WPg82I: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            t: null,
          },
        },
        _8PUyO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            r: null,
            l: null,
            t: null,
          },
        },
        'b6EQ-g': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        '8ReFGg': {
          bd: {
            l: null,
            t: null,
          },
        },
        JhHc66: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        mpuLI5: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        pL9unn: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 3,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
            },
            1: {
              0: {
                v: '`其他项目清单与计价表`',
                t: 1,
                s: 'DMXsTr',
                p: '',
                custom: {},
              },
              1: {
                s: 'DMXsTr',
                v: '',
                p: '',
              },
              2: {
                s: 'vin1Ex',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'oL8Tcq',
                custom: {},
                p: '',
              },
              1: {
                s: 'oL8Tcq',
                v: '',
                p: '',
                t: 1,
              },
              2: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: 'Pz8Nlq',
                p: '',
                custom: {},
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
              1: {
                v: '`项目名称`',
                t: 1,
                s: 'ilhOM8',
                p: '',
              },
              2: {
                v: '`金额(元)`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
            },
            4: {
              0: {
                s: 'ilhOM8',
                custom: {},
                v: '[XH]',
                t: 1,
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'mpuLI5',
                p: '',
                custom: {},
              },
              2: {
                v: 'IF(OR(([MC]=`材料暂估价`) , ([MC] = `设备暂估价`)), `/`,[HJ])',
                t: 1,
                s: 'pL9unn',
                custom: {},
                p: '',
              },
            },
            5: {
              0: {
                v: '`/`',
                t: 1,
                s: 'ilhOM8',
                p: '',
              },
              1: {
                v: '`合计`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
              2: {
                v: 'SUM(IF(OR(([MC]=`材料暂估价`) , ([MC] = `设备暂估价`) ,([MC]= `专业工程暂估价`)), 0,[HJ]))',
                t: 1,
                s: 'pL9unn',
                custom: {},
                p: '',
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'b6EQ-g',
                p: '',
              },
              1: {
                s: 'b6EQ-g',
                v: '',
                p: '',
                t: 1,
              },
              2: {
                v: '',
                t: 1,
                s: 'b6EQ-g',
                p: '',
              },
            },
            7: {
              0: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
              1: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
              2: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 7,
          columnCount: 3,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 13,
              endRow: 13,
              startColumn: 0,
              endColumn: 7,
            },
            {
              startRow: 14,
              endRow: 14,
              startColumn: 0,
              endColumn: 3,
            },
            {
              startRow: 14,
              endRow: 14,
              startColumn: 4,
              endColumn: 7,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 1,
              endColumn: 1,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 2,
              endColumn: 2,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 3,
              endColumn: 3,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 4,
              endColumn: 4,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 5,
              endColumn: 5,
            },
            {
              startRow: 15,
              endRow: 15,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 3,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 1,
            },
            {
              startRow: 1,
              endRow: 1,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 1,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            4: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            5: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 109,
              hd: 0,
            },
            1: {
              w: 535,
              hd: 0,
            },
            2: {
              w: 261,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
  {
    name: '表1-9 其他项目清单与计价表',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['工程量清单报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        kJ66Ef: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ArRIyI: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '7tGYlm': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        SsagpX: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        fgkvbf: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '8SPpMY': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '-dwAwy': {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '2aFP8r': {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        kvJCu3: {
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        NaHwuW: {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        bfITOz: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '7GJLOe': {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        hHkIyp: {
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '9leVoS': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        _OTDvu: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        l0ddfj: {
          bd: {
            l: null,
            t: null,
          },
        },
        k02dan: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        VkRi5j: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        YuXRg4: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        AOp4bI: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        T07rvv: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ey5O3H: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        wvpkVV: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        D000zA: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        OeU1SF: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        B43KY6: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        VxHV_O: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        ix1m_k: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '0hkq56': {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '4g_xR8': {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        N2xf_e: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        dyr2sf: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        LOLbqo: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        aUvkTT: {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '30Gtx1': {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '15uVvE': {
          bd: {
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '2DTLNR': {
          bd: {
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        lbWUPC: {
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 0,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
          bd: {
            l: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 3,
              cl: {
                rgb: '#35322B',
              },
            },
          },
        },
        bp19pG: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        a9Cz6q: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
        },
        F6Ch1m: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '4gFHC_': {
          bd: {
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        JXj7rd: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        uawV7s: {
          bd: {
            t: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        QWtcqT: {
          bd: {
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
            r: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        xDp71G: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '9ysczg': {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        UyqoYj: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        DRu9e4: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        NsDgCR: {
          bd: {
            t: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        hwHVeK: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        wG7n7H: {
          bd: {
            b: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 8,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        M9iyos: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        osL2Ej: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        MHjJk8: {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 16,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '46oSUa': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
          },
        },
        Q3kYlQ: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        zH2s7e: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        nyd2RT: {
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 13,
              cl: {
                rgb: '#35322B',
              },
            },
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        'RO3R-9': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        '-Cmxo7': {
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        IyklH5: {
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: null,
            t: null,
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        kD28gr: {
          bd: {
            b: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        nqOWTI: {
          bd: {
            b: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#35322B',
              },
            },
            r: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
            t: {
              s: 2,
              cl: {
                rgb: '#35322B',
              },
            },
          },
          cl: {
            rgb: 'rgb(0, 0, 0)',
          },
          fs: 10,
          ht: 2,
          vt: 2,
          bl: 0,
          it: 0,
          ff: '宋体',
          tb: 1,
        },
        TC3pp1: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '0akikc': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        u3DAI0: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '1Bm3Yh': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        htP7m8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        Bx8kXx: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
          },
        },
        ybcgyE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        v6_RA4: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        'Qt3-wz': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        NHJe26: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        qCnAAF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        MNbEUY: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        MlTCBM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        EbUSCP: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        IkweBz: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            r: null,
            l: null,
          },
        },
        'C-8IUf': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        R9bqZ1: {
          bd: {
            l: null,
          },
        },
        DMXsTr: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        eLulFi: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        vin1Ex: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ilhOM8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        oL8Tcq: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        Pz8Nlq: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 3,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        WPg82I: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            t: null,
          },
        },
        _8PUyO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            r: null,
            l: null,
            t: null,
          },
        },
        'b6EQ-g': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        '8ReFGg': {
          bd: {
            l: null,
            t: null,
          },
        },
        JhHc66: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: null,
          },
        },
        V7Z8CV: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        VyuQq9: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 3,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'C-8IUf',
                p: '',
              },
            },
            1: {
              0: {
                v: '`其他项目清单与计价表`',
                t: 1,
                s: 'DMXsTr',
                p: '',
                custom: {},
              },
              1: {
                s: 'DMXsTr',
                v: '',
                p: '',
              },
              2: {
                s: 'vin1Ex',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'oL8Tcq',
                custom: {},
                p: '',
              },
              1: {
                s: 'oL8Tcq',
                v: '',
                p: '',
                t: 1,
              },
              2: {
                v: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
                t: 1,
                s: 'Pz8Nlq',
                p: '',
                custom: {},
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
              1: {
                v: '`项目名称`',
                t: 1,
                s: 'ilhOM8',
                p: '',
              },
              2: {
                v: '`金额(元)`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
            },
            4: {
              0: {
                s: 'ilhOM8',
                custom: {},
                v: '[XH]',
                t: 1,
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'V7Z8CV',
                p: '',
                custom: {},
              },
              2: {
                s: 'VyuQq9',
                v: 'IF(OR(([MC]=`材料暂估价`) , ([MC] = `设备暂估价`)), `/`,[HJ])',
                t: 1,
                custom: {},
                p: '',
              },
            },
            5: {
              0: {
                v: '`/`',
                t: 1,
                s: 'ilhOM8',
                p: '',
              },
              1: {
                v: '`合计`',
                t: 1,
                s: 'ilhOM8',
                p: '',
                custom: {},
              },
              2: {
                s: 'VyuQq9',
                v: '',
                p: '',
                t: 1,
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'b6EQ-g',
                p: '',
              },
              1: {
                s: 'b6EQ-g',
                v: '',
                p: '',
                t: 1,
              },
              2: {
                v: '',
                t: 1,
                s: 'b6EQ-g',
                p: '',
              },
            },
            7: {
              0: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
              1: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
              2: {
                s: '8ReFGg',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 7,
          columnCount: 3,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 13,
              endRow: 13,
              startColumn: 0,
              endColumn: 7,
            },
            {
              startRow: 14,
              endRow: 14,
              startColumn: 0,
              endColumn: 3,
            },
            {
              startRow: 14,
              endRow: 14,
              startColumn: 4,
              endColumn: 7,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 1,
              endColumn: 1,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 2,
              endColumn: 2,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 3,
              endColumn: 3,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 4,
              endColumn: 4,
            },
            {
              startRow: 15,
              endRow: 16,
              startColumn: 5,
              endColumn: 5,
            },
            {
              startRow: 15,
              endRow: 15,
              startColumn: 6,
              endColumn: 7,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 23,
              endRow: 23,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 5,
              endColumn: 7,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 11,
              endRow: 11,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 3,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 1,
            },
            {
              startRow: 1,
              endRow: 1,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 1,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
            },
            3: {
              h: 30,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            4: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            5: {
              h: 30,
              hd: 0,
              field: 'sheetStatistic',
              rowType: '明细表统计行',
              dataSourceType: '其他项目',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 109,
              hd: 0,
            },
            1: {
              w: 535,
              hd: 0,
            },
            2: {
              w: 261,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
];
