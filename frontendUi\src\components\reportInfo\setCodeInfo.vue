<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-11-18 09:40:31
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-09 11:08:26
-->
<template>
  <div class="set-code-info">
    <vxe-table
      :data="props.tableData.get('gcbh')"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      class="table-scrollbar table-edit-common"
      :cell-class-name="cellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :tree-config="{
        transform: true,
        rowField: 'sequenceNbr',
        parentField: 'parentId',
        showIcon: true,
        expandAll: true,
        reserve: true,
      }"
      ref="vexTable"
      keep-source
      show-overflow
      height="500"
      @cell-click="useCellClickEvent"
    >
      <vxe-column width="60" field="dispNo"></vxe-column>
      <vxe-column title="名称" field="name" align="left" tree-node></vxe-column>
      <vxe-column
        title="内容"
        field="remark"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <span v-if="row.dispNo === 1">{{ row.remark }}</span>
          <vxe-input
            v-else
            :clearable="false"
            placeholder="请输入内容"
            v-model="row.remark"
            @keyup="row.remark = (row.remark + '').replace(/[^\d*]/g, '')"
            type="text"
            name="name"
          ></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useCellClick } from '@/hooks/useCellClick.js';
import { getPositiveInteger } from '@/utils/index.js';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const props = defineProps(['tableData']);
let vexTable = ref();
const cellClassName = ({ row, column, $columnIndex }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (row.requiredFlag === 1 && column.field === 'name') {
    return 'required-fields';
  }
  return selectName;
};
onMounted(() => {
  setTimeout(() => {
    vexTable.value?.setAllTreeExpand(true);
  }, 10);
});
</script>

<style lang="scss" scoped>
::v-deep(.vxe-table .vxe-body--row .required-fields) {
  color: #de3f3f;
}
</style>
