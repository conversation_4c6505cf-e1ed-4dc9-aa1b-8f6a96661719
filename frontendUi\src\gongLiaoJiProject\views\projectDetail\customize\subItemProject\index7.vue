<!--
 * @Descripttion: 
 * @Author: kongweiqiang
 * @Date: 2024-06-11 09:30:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-07-04 09:58:09
-->
<template>
  <div class="subItem-project-gs">
    <split
      horizontal
      ratio="4/3"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      @onDragHeight="dragHeight"
    >
      <template #one>
        <frameSelect
          eventDom="multiple-select"
          ref="frameSelectRef"
          type="branch"
          :tableData="tableData"
          @scrollTo="scrollTo"
          @selectData="getSelectData"
          class="table-content"
        >
          <vxe-table
            ref="vexTable"
            class="table-edit-common trends-table-column"
            keep-source
            :column-config="{ resizable: true }"
            :mouse-config="{ selected: true }"
            :tree-config="{
              transform: true,
              rowField: 'sequenceNbr',
              parentField: 'parentId',
              line: true,
              showIcon: false,
              expandAll: true,
            }"
            height="auto"
            :loading="loading"
            :data="tableData"
            :scroll-y="{ enabled: true, gt: 20 }"
            @cell-click="
              cellData => {
                useCellClickEvent(cellData, tableCellClickEvent, [
                  'unit',
                  'deCode',
                  'costMajorName',
                  'measureType',
                  'itemCategory',
                ]);
              }
            "
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              beforeEditMethod: cellBeforeEditMethod,
            }"
            :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
            @current-change="currentChangeEvent"
            @edit-closed="editClosedEvent"
            @scroll="getScroll"
            :menu-config="menuConfig"
            @menu-click="contextMenuClickEvent"
            :row-class-name="rowClassName"
            :cell-class-name="cellClassName"
            :custom-config="{ storage: true }"
          >
            <vxe-column field="index" width="35" align="center">
              <template #default="{ row }">
                <div class="multiple-select" @click="clickIndex(row)">
                  <!-- {{ row.index }} -->
                </div>
              </template>
            </vxe-column>

            <vxe-column
              v-for="columns of handlerColumns"
              v-bind="columns"
              :header-class-name="headerClass(columns)"
            >
              <template
                #header="{
                  column,
                  columnIndex,
                  $columnIndex,
                  _columnIndex,
                  $rowIndex,
                }"
              >
                <div class="custom-header">
                  <span>{{ column.title }}</span>
                  <!-- <CloseOutlined
                    class="icon-close"
                    @click="closeColumn(column, $columnIndex)"
                  /> -->
                </div>
              </template>

              <template
                v-if="columns.slot"
                #default="{ column, row, $columnIndex, $rowIndex }"
              >
                <template v-if="columns.field == 'dispNo'">
                  <!-- {{row.dispNo}} -->
                </template>
                <template v-if="columns.field == 'deCode'">
                  <icon-font
                    v-if="row.isLocked"
                    type="icon-qingdan-suoding"
                  ></icon-font>
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 1 && tableData.length > 1"
                    class="vxe-icon-caret-down"
                  ></i>
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 2"
                    class="vxe-icon-caret-right"
                  ></i>
                  <span class="code"
                    >{{ row.deCode }}
                    {{ row.redArray?.length > 0 ? row.redArray : '' }}</span
                  ><span class="code-black" v-if="row.blackArray?.length > 0">{{
                    row.blackArray
                  }}</span>
                </template>

                <template v-else-if="columns.field == 'deName'">
                  <Annotations
                    @close="v => closeAnnotations(v, row)"
                    @onfocusNode="onFocusNode(row)"
                    v-if="
                      (row.isTempRemove !== 1 && row?.noteViewVisible) ||
                      row?.isShowAnnotations ||
                      row?.noteEditVisible
                    "
                    :note="row.annotations"
                    :isDisabled="row?.noteEditVisible || row?.isShowAnnotations"
                    :ref="el => getAnnotationsRef(el, row)"
                  ></Annotations>
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-if="
                      row.isTempRemove !== 1 &&
                      isSelectedCell({
                        $columnIndex,
                        column,
                        row,
                      })
                    "
                    @click.stop="openEditDialog('deName')"
                  ></icon-font>
                  <div>{{ row.deName }}</div>
                </template>

                <div v-else-if="columns.field == 'type'">
                  <span
                    v-if="
                      (!row.borrowFlag && !row.changeFlag) || row.type === '费'
                    "
                    >{{
                      row.type === '06' || row.type === '09'
                        ? deMapFun.rcjMap[row.deResourceKind]
                        : row.type === '03' || row.type === '04'
                          ? row.displayType
                          : deMapFun.deMap[row.type]
                    }}</span
                  >

                  <span class="code-flag" v-if="row.type !== '费'"
                    >{{ row.changeFlag ? row.changeFlag : '' }}
                  </span>
                  <span
                    class="code-flag"
                    v-if="row.type !== '费' && !row.changeFlag"
                    >{{ row.borrowFlag ? row.borrowFlag : '' }}
                  </span>
                </div>
                <template v-else-if="columns.field == 'totalNumber'">
                  <span>{{ row[columns.field] }}</span>
                </template>
                <template
                  v-else-if="columns.field == 'resQty' && row.kind != '09'"
                >
                  <span class="aaa" v-if="!row.isTopLevelType03">{{
                    row[columns.field]
                  }}</span>
                </template>
                <template v-else-if="columns.field == 'remark'">
                  <span class="aaa">{{ row[columns.field] }}</span>
                </template>
                <!-- 备注 施工组织措施类别 取费文件 单价 工程量 单位-->
                <template v-else>
                  <span v-if="deMapFun.isDe(row.kind)">{{
                    columns.field == 'costMajorName' && row.kind == '-1'
                      ? ''
                      : row[columns.field]
                  }}</span>
                </template>
              </template>

              <template
                v-if="columns.slot"
                #edit="{ column, row, $columnIndex }"
              >
                <template v-if="columns.field == 'deName'">
                  <cell-textarea
                    v-if="row.isTempRemove !== 1 && row.type != '-1'"
                    :maxlength="2000"
                    v-model="row.deName"
                    :clearable="false"
                    :textHeight="row.height"
                  ></cell-textarea>
                </template>
                <div v-else-if="columns.field == 'type'">
                  <vxe-select
                    v-model="row.deResourceKind"
                    transfer
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row?.kind) &&
                      (row?.kind !== '07' || row.isCostDe === 4) &&
                      (row.deResourceKind === 2 ||
                        row.deResourceKind === 5 ||
                        row.deResourceKind === 4)
                    "
                  >
                    <vxe-option
                      v-for="item in typeList"
                      :key="item.value"
                      :value="item.value"
                      :label="item.name"
                    ></vxe-option>
                  </vxe-select>
                  <span v-else>
                    <span
                      v-if="
                        (!row.borrowFlag && !row.changeFlag) ||
                        row.type === '费'
                      "
                      >{{
                        row.type === '06' || row.type === '09'
                          ? deMapFun.rcjMap[row.deResourceKind]
                          : row.type === '03' || row.type === '04'
                            ? row.displayType
                            : deMapFun.deMap[row.type]
                      }}</span
                    >

                    <span class="code-flag" v-if="row.type !== '费'"
                      >{{ row.changeFlag ? row.changeFlag : '' }}
                    </span>
                    <span
                      class="code-flag"
                      v-if="row.type !== '费' && !row.changeFlag"
                      >{{ row.borrowFlag ? row.borrowFlag : '' }}
                    </span>
                  </span>
                </div>
                <template v-else-if="columns.field == 'deCode'">
                  <vxe-pulldown
                    ref="pulldownRef"
                    transfer
                    v-if="row.isTempRemove !== 1 && deMapFun.isDe(row.kind)"
                  >
                    <template #default>
                      <vxe-input
                        v-model="row.deCode"
                        placeholder="请输入项目编码"
                        @keyup="keyupEvent(row, $event)"
                      ></vxe-input>
                    </template>
                    <template #dropdown>
                      <div class="my-dropdown4" v-if="row.kind === '03'">
                        <vxe-grid
                          border
                          auto-resize
                          :show-header="false"
                          height="200"
                          width="500"
                          :row-config="{ isHover: true }"
                          :loading="loading"
                          :data="tableList"
                          :columns="tableColumn"
                          @cell-click="cellClickEvent"
                        >
                        </vxe-grid>
                      </div>
                    </template>
                  </vxe-pulldown>
                  <template v-else
                    ><i
                      @click="changeStatus(row)"
                      v-if="row.displaySign === 1"
                      class="vxe-icon-caret-down"
                    ></i>
                    <i
                      @click="changeStatus(row)"
                      v-if="row.displaySign === 2"
                      class="vxe-icon-caret-right"
                    ></i>
                    <span class="code">{{ row.deCode }} </span></template
                  >
                </template>

                <!-- 单位 -->
                <template v-else-if="columns.field == 'unit'">
                  <vxeTableEditSelect
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row.kind) &&
                      row.deCode
                    "
                    :filedValue="row.unit"
                    :list="projectStore.unitListString"
                    @update:filedValue="
                      newValue => {
                        saveCustomInput(newValue, row, 'unit', $rowIndex);
                      }
                    "
                  ></vxeTableEditSelect>
                </template>

                <!-- 消耗量 -->
                <template v-else-if="columns.field == 'resQty'">
                  <vxe-input
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row.kind) &&
                      !row.isTopLevelType03
                    "
                    v-model="row.resQty"
                    :maxlength="10"
                    @blur="
                      row.resQty =
                        removeSpecialCharsFromPrice(row.resQty, 3) || 0
                    "
                  />
                </template>
                <!-- 工程量 -->
                <template v-else-if="columns.field == 'quantity'">
                  <vxe-input
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row.kind) &&
                      row.type != '-1' &&
                      row.type != '07'
                    "
                    v-model="row.originalQuantity"
                    :maxlength="10"
                    @blur="
                      row.quantity = removeSpecialCharsFromPrice(
                        row.quantity,
                        3
                      )
                    "
                  />
                  <span v-else>{{ row.quantity }}</span>
                </template>
                <!-- 合价 -->
                <template v-else-if="columns.field == 'totalNumber'">
                  <span>{{ row.totalNumber }}</span>
                </template>
                <!-- 取费文件 -->
                <template
                  v-else-if="
                    columns.field == 'costMajorName' && columns.type != '-1'
                  "
                >
                  <vxe-select
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row.kind) &&
                      row.deCode &&
                      row.isTopLevelType03
                    "
                    v-model="row.costMajorName"
                    transfer
                  >
                    <vxe-option
                      v-for="item in feeFileList"
                      :key="item.qfCode"
                      :value="item.qfName"
                      :label="item.qfName"
                    ></vxe-option>
                  </vxe-select>
                  <span v-else>{{ row.costMajorName }}</span>
                </template>

                <!-- 单价 -->
                <template v-else-if="columns.field == 'price'">
                  <vxe-input
                    v-if="
                      row.isTempRemove !== 1 &&
                      deMapFun.isDe(row.kind) &&
                      row.levelMark != 2 &&
                      row.levelMark != 3 &&
                      row.type != '-1'
                    "
                    v-model="row.price"
                    :maxlength="10"
                    @blur="row.price = removeSpecialCharsFromPrice(row.price)"
                  />
                  <span v-else>{{ row.price }}</span>
                </template>

                <!-- 备注 -->
                <template v-else-if="columns.field == 'description'">
                  <cell-textarea
                    :disabled="row.isTempRemove === 1"
                    v-model="row.remark"
                    :textHeight="row.height"
                  ></cell-textarea>
                </template>

                <!-- 备注 -->
                <template v-else>
                  <vxe-input v-model="row[columns.field]" />
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </frameSelect>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            ref="quotaInfoRef"
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="1"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            :isComplete="isComplete"
            :isUpdate="isUpdate"
          ></quota-info>
        </div>
      </template>
    </split>
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="fillFromIndexPage"
      @currentInfoReplace="currentInfoReplace"
      :dataType="dataType"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
    ></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <div class="content">执行本操作将会删除本项及其下所有数据和关联关系</div>
      <!-- <div class="content" v-if="deleteList[0] === 5">
        是否确定删除？将删除{{
          currentInfo.kind === '01' ? '分部' : '清单'
        }}及其下挂所有数据.
      </div>
      <div
        class="content"
        v-if="deleteList.length === 1 && deleteList[0] === 4"
      >
        是否确定删除？
      </div> -->
      <div class="footer-btn-list">
        <a-button @click="deleteVisible = false" :disabled="deleteLoading"
          >取消</a-button
        >
        <a-button
          type="primary"
          @click="delFbData(true)"
          :disabled="deleteLoading"
          >确定</a-button
        >
        <!-- <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          ghost
          @click="cancel"
          >取消</a-button
        >
        <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          @click="delFbData(currentInfo.kind !== '04')"
          :disabled="deleteLoading"
          >确定</a-button
        > -->
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350"
    >
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        class="edit-content"
      />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>
    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"
    ></info-modal>
    <bcQd
      v-model:qdVisible="qdVisible"
      :code="deCode"
      :type="1"
      :currentInfo="currentInfo"
      @saveData="saveData"
      @bcCancel="bcCancel"
    ></bcQd>
    <bcDe
      v-model:visible="deVisible"
      :code="deCode"
      :type="1"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"
    ></bcDe>
    <bcRcj
      :code="deCode"
      v-model:visible="rcjVisible"
      :showResqty="false"
      supplement
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"
    ></bcRcj>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="
        showModelType === 'csfy'
          ? '1000'
          : showModelType === 'azfy'
            ? 1200
            : 800
      "
      height="auto"
      @close="closePriceModel"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData"
          @close="closePriceModel"
        ></component>
      </keep-alive>
    </common-modal>
    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio
          v-for="(unit, index) in addCurrentInfo?.unit"
          :key="index"
          :value="unit"
        >
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(addCurrentInfo)"
        >确定</a-button
      >
    </div>
    <set-standard-type
      v-model:standardVisible="standardVisible"
      :type="1"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
    ></set-standard-type>
    <set-main-material
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :mainMaterialTableData="mainMaterialTableData"
      @setUpdate="setUpdate"
    >
    </set-main-material>
    <common-modal
      v-model:modelValue="comMatchModal"
      className="dialog-comm"
      title="组价方案匹配"
      width="600"
      height="400"
    >
      <component-matching @closeComMatch="closeComMatch"></component-matching>
    </common-modal>
    <schedule-file
      v-model:dialogVisible="showSchedule"
      strokeColor="#54a1f3"
      :percent="percent"
      :desc="percentInfo?.dec"
      :pageType="'comMatch'"
      @isContinue="isContinue"
      :isNoClose="isNoClose"
      :percentInfo="percentInfo"
      :width="600"
    ></schedule-file>
    <common-modal
      className="titleNoColor noHeaderHasclose"
      v-model:modelValue="resetModal"
      title=" "
      width="400"
      height="200"
    >
      <div class="reCheck">
        <p style="font-weight: 600">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />组价进行中，是否确定关闭？
        </p>
        <p style="padding-left: 20px; margin-bottom: 26px">
          当前数据已发生变化是否应用组价后数据
        </p>

        <a-button style="margin: 0 30px 0 20px" @click="recover(true)"
          >否，恢复至组价前数据
        </a-button>
        <a-button type="primary" @click="recover(false)"> 确定</a-button>
      </div>
    </common-modal>
    <common-modal
      className="dialog-comm"
      title="组价方案匹配"
      width="550"
      height="400
    "
      v-model:modelValue="reportModel"
      @cancel="reportModel = false"
      @close="reportModel = false"
    >
      <match-pic
        @lookView="lookView"
        :startMatchData="startMatchData"
      ></match-pic>
    </common-modal>
    <!-- 导入项目 -->
    <common-modal
      className="dialog-comm"
      title="导入项目"
      width="auto"
      v-model:modelValue="importProjectVisible"
    >
      <import-project
        @cancel="((importProjectVisible = false), queryBranchDataById('load'))"
        @close="importProjectVisible = false"
        ref="importProjectRef"
      />
    </common-modal>
    <!-- 导入excel -->
    <common-modal
      className="dialog-comm"
      title="导入excel"
      width="auto"
      v-model:modelValue="importExcelVisible"
    >
      <import-excel
        @cancel="importExcelVisible = false"
        @close="importExcelVisible = false"
        ref="importExcelRef"
      />
    </common-modal>
    <gs-batch-delete
      v-model:batchDeleteVisible="batchDeleteVisible"
      :batchDataType="batchDataType"
      @updateData="queryBranchDataById"
    ></gs-batch-delete>
    <!--    <combined-search @filterData="filterData"></combined-search>-->
  </div>
</template>

<script setup>
import {
  onMounted,
  onBeforeUnmount,
  reactive,
  ref,
  watch,
  nextTick,
  markRaw,
  defineAsyncComponent,
  onActivated,
  getCurrentInstance,
} from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  everyNumericHandler,
} from '@/utils/index';
import { checkisOnline } from '@/utils/publicInterface';
import QuotaInfo from '../quotaInfo/index.vue';
import InventoryAndQuotaIndex from '../inventoryAndQuotaIndex/index.vue';
import api from '../../../../api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import ComponentMatching from '../measuresItem/componentMatching.vue';
import MatchPic from '../measuresItem/MatchPic.vue';
import { message } from 'ant-design-vue';
import { ItemBillMenuOperator } from './ItemBillMenuOperator';
import xeUtils from 'xe-utils';
import frameSelect from '@/components/frameSelect/index.vue';
import gsBatchDelete from '@/components/batchDelete/gljIndex.vue';
import split from '@/components/split/index.vue';
import { insetBus } from '@gongLiaoJi/hooks/insetGljBus';
import operateList from '../operate';
import { useCheckBefore } from '@gongLiaoJi/hooks/useCheckBefore';
import infoMode from '@/plugins/infoMode';
import SetStandardType from '../quotaInfo/setStandardType.vue';
import SetMainMaterial from '../quotaInfo/setMainMaterial.vue';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { useReversePosition } from '@gongLiaoJi/hooks/useReversePosition.js';
import { useAttrAssociation } from '@gongLiaoJi/hooks/useAttrAssociation.js';
import { useSubItem } from '@gongLiaoJi/hooks/useGljSubItem.js';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import { useRoute } from 'vue-router';
import importExcel from './components/importExcel.vue';
import importProject from './components/importProject.vue';
import deMapFun from '../deMap';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';

const route = useRoute();
console.log('deMapFun', deMapFun);
const { dataSearchPosition } = useReversePosition();
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  resetCellData,
} = useCellClick();

const { showInfo, isComplete } = useCheckBefore();
// const { checkUnit, showInfo, isComplete } = useCheckBefore();

const components = markRaw(new Map());
components.set(
  'zscy',
  defineAsyncComponent(() => import('../measuresItem/zscyContent.vue'))
);
components.set(
  'zscg',
  defineAsyncComponent(() => import('../measuresItem/zscgContent.vue'))
);
components.set(
  'azfy',
  defineAsyncComponent(() => import('../measuresItem/azfyContent.vue'))
);
// components.set(
//   'csfy',
//   defineAsyncComponent(() => import('../measuresItem/csfyContent.vue'))
// );
let isPriceModel = ref(false);
let showModelType = ref('');
let showPriceTitle = ref('');
// let comMatchModal = ref(false); //组件方案弹框
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
// let currentInfo = ref();
let vexTable = ref();
let frameSelectRef = ref();
const projectStore = projectDetailStore();
const menuOperator = new ItemBillMenuOperator();
let contextmenuList = ref(menuOperator.menus);
let bcContextmenuList = ref(menuOperator.menus);
let szTypeList = ref([]);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let deleteVisible = ref(false);
let splitPercent = ref(0.55);
const emits = defineEmits(['updateMenuList', 'getCurrentInfo']);
let importProjectVisible = ref(false);
let importProjectRef = ref(null);
let importExcelVisible = ref(false);
let importExcelRef = ref(null);
//组价部分
const scheduleFile = defineAsyncComponent(
  () => import('@/components/schedule/schedule.vue')
);
const $ipc = cxt.appContext.config.globalProperties.$ipc;
let comMatchModal = ref(false); //组价方案弹框
let percentInfo = ref(); //进度条描述
let percent = ref(0); //进度条百分比
let resetModal = ref(false); //是否确认关闭进度条
let isNoClose = ref(false); //进度条关闭前执行函数
let showSchedule = ref(false);
let reportModel = ref(false); //组价饼图弹框
let startMatchData = ref();
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备',
    value: 4,
  },
]);

let {
  editClosedEvent,
  onDragHeight,
  initVirtual,
  getScroll,
  renderedList,
  init,
  EnterType,
  scrollToPosition,
  queryBranchDataById,
  currentChangeEvent,
  mainMaterialTableData,
  updateFbData,
  queryFeeFileData,
  loading,
  saveContent,
  openEditDialog,
  showModelTitle,

  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  deCode,
  rcjVisible,
  deVisible,
  qdVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  showUnitTooltip,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  tableData,
  originalTableData,
  materialVisible,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  updateCancelDelTempStatusColl,
  handleNoteClick,
  batchDeleteFun,
  batchDeleteVisible,
  batchDataType,
  selectData,
  onFocusNode,
  closeAnnotations,
  cellMouseEnterEvent,
  getAnnotationsRef,
  AnnotationsRefList,
  areaStatus,
  areaVisibleType,
  closeAreaModal,
} = useSubItem({
  operateList,
  frameSelectRef: frameSelectRef.value,
  resetCellData,
  // checkUnit,
  vexTable,
  emits,
  pageType: 'fbfx',
});

const tableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 50,
    align: 'center',
    slot: true,
  },
  {
    title: '项目编码',
    field: 'deCode',
    align: 'left',
    headerAlign: 'center',
    treeNode: true,
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
  },
  {
    title: '类型',
    field: 'type',
    width: 50,
    align: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
  },
  {
    title: '项目名称',
    field: 'deName',
    width: 260,
    align: 'center',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
  },
  {
    title: '单位',
    field: 'unit',
    width: 60,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
  },
  {
    title: '消耗量',
    field: 'resQty',

    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    title: '工程量',
    field: 'quantity',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    title: '单价',
    field: 'price',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    title: '合价',
    field: 'totalNumber',
    slot: true,
  },
  {
    title: '取费专业',
    field: 'costMajorName',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    title: '备注',
    field: 'remark',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
]);

const { handlerColumns, closeColumn } = useFormatTableColumns({
  Columns: tableColumns.value,
});

const isContinue = type => {
  if (type === '关闭') {
    isNoClose.value = true;
    resetModal.value = true;
  } else if (type === '暂停') {
    api
      .pauseMerge({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {});
  } else if (type === '继续') {
    api
      .startMerge(JSON.parse(JSON.stringify(startMatchData.value)))
      .then(res => {});
  }
};
const closeComMatch = data => {
  //关闭组价方案匹配
  startMatchData.value = data;
  // comMatchModal.value = false;
  // reportModel.value = true; //饼图弹框
  startMatch(data);
};
const startMatch = async data => {
  isNoClose.value = true;
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  //初始化进度条
  percentInfo.value = {
    finish: 0,
    noFinish: 0,
    total: 0,
    dec: '组价方案匹配中，请稍后…',
  };
  percent.value = 0;
  comMatchModal.value = false;
  // setTimeout(() => {
  showSchedule.value = true; //组价进度条开始

  $ipc.on(formData.constructId, (event, arg) => {
    if (arg.percent >= percent.value) {
      percentInfo.value = {
        finish: arg.succeed,
        noFinish: arg.notSuccess,
        total: arg.total,
        dec: arg.percent >= 100 ? '组价方案完成' : '组价方案匹配中，请稍后…',
      };
      percent.value = arg.percent;
    }
    if (arg.percent >= 100) {
      closeSchedule();
    }
  });
  let res = await api.startMerge(data).then();
  if (res.status === 500 && percent.value === 0) {
    setTimeout(() => {
      percentInfo.value = {
        finish: 0,
        noFinish: 0,
        total: 0,
        dec: '组价方案完成',
      };
      percent.value = 100;
      closeSchedule();
    }, 1000);
  }
};
const closeSchedule = () => {
  setTimeout(() => {
    isNoClose.value = false;
    showSchedule.value = false; //组价进度条关闭
    if (resetModal.value) {
      resetModal.value = false;
    }
    $ipc.removeAllListeners('formData.constructId'); //监听事件移除
    if (projectStore.tabSelectName === '预算书') {
      reportModel.value = true; //饼图弹框
      queryBranchDataById('Refresh');
    }
  }, 2000);
};

const recover = async bol => {
  //否，恢复至组价前数据
  //  bol--为true恢复
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let getRes;
  if (bol) {
    getRes = await api.beforeRestoring(formData).then();
  } else {
    getRes = await api.determine(formData).then();
  }
  isNoClose.value = false;
  resetModal.value = false;
  showSchedule.value = false;
  queryBranchDataById('Refresh'); //点击是或否都更新数据
};
//扇形图点击查看功能
const lookView = data => {
  checkList.value = [];
  reportModel.value = false; //饼图弹框
  switch (data.name) {
    case '精准组价':
      checkList.value.push('1');
      break;
    case '近似组价':
      checkList.value.push('2');
      break;
    case '未匹配组价':
      checkList.value.push('0');
      break;
  }
  dataSearchPosition({
    treeId: startMatchData.value.selectedUnitIdList[0],
    tabMenuName: '预算书',
    type: checkList.value,
  });
};
//项目特征关联
let {
  associationVisible,
  associationRef,
  dblClickHandler,
  projectAttrChange,
  projectAttrFocus,
} = useAttrAssociation({ type: 'fbfx' });

// 关联数据双击应用回调
const associationDblClick = (data, row) => {
  dblClickHandler({
    data,
    row,
    callback: () => {
      console.log('asdadsa');
      queryBranchDataById();
      setTimeout(() => {
        quotaInfoRef.value.manualTabChange('groupSchemeTable');
      }, 500);
    },
  });
};

let flag = ref(true); //判断输入的内容是否合法

let menuList = ref([
  {
    type: 0,
    name: '添加分部',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子分部',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加子目',
    kind: '03',
    isValid: false,
  },
]);
let bcMenuList = ref([
  {
    type: 2,
    name: '补充定额',
    kind: '04',
    isValid: false,
  },
  {
    type: 2,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
const quotaInfoRef = ref();
let deleteList = ref([]);
let page = ref(1);
let limit = ref(300000);
let scrollSwitch = ref(false);
// let loading = ref(false);
let deleteLoading = ref(false);
let copyData = ref(null);
// let selectData = ref(null);

// let selectData = ref(null);
// let showModelTitle = ref('名称编辑');
// let isShowModel = ref(false);
// let editContent = ref('');
// let editKey = ref('');
// let infoVisible = ref(false); // 提示信息框是否显示
// let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
// let iconType = ref(''); // 提示信息框的图标
// let isSureModal = ref(false); // 提示信息框是否为确认提示框
// let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
const pulldownRef = ref(); // 编码推荐数据ref
let indexLoading = ref(false); // 索引页面loading

const bcQd = defineAsyncComponent(() => import('./components/bcQd.vue'));
const bcDe = defineAsyncComponent(() => import('./components/bcDe.vue'));
const bcRcj = defineAsyncComponent(() => import('./components/bcRcj.vue'));

let standardVisible = ref(false); //设置标准换算
let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let materialVisible = ref(false); // 是否设置主材市场价弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据
const checkList = ref([]); // 组价方案匹配筛选选中值
watch(
  () => projectStore.tabSelectName,
  () => {
    if (projectStore.tabSelectName === '预算书') {
      page.value = 1;
      // queryUnit();
      // nextTick(() => {
      //   initVirtual(vexTable.value);
      // });
      queryBranchDataById('other');
    }
  }
);
watch(
  () => projectStore.asideMenuCurrentInfo?.sequenceNbr,
  () => {
    if (projectStore.tabSelectName === '预算书') {
      page.value = 1;
      // queryFeeFileData();
      // querySzType();
      // queryUnit();

      nextTick(() => {
        initVirtual(vexTable.value);
      });
      if (!projectStore.isAutoPosition) {
        // 不是自动定位的才调用接口
        queryBranchDataById('other');
      }
    }
  }
);

watch(
  () => lockFlag.value,
  () => {
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
  }
);

watch(
  () => projectStore.positionId,
  () => {
    if (projectStore.tabSelectName === '预算书' && projectStore.positionId) {
      currentInfo.value = { sequenceNbr: projectStore.positionId };
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);

watch(
  () => projectStore.combinedSearchList,
  () => {
    if (
      projectStore.tabSelectName === '预算书' &&
      projectStore.combinedSearchList
    ) {
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
      nextTick(() => {
        filterData(projectStore.combinedSearchList);
      });
    }
  }
);

onMounted(() => {
  if (projectStore.asideMenuCurrentInfo?.sequenceNbr) {
    page.value = 1;
    if (!projectStore.isAutoPosition) {
      // 不是自动定位的才调用接口
      queryBranchDataById('other');
    }
  }
  console.log(projectStore, 'projectStore');
  // if (projectStore.asideMenuCurrentInfo?.sequenceNbr) {
  //   page.value = 1;
  //   if (!projectStore.isAutoPosition) {
  //     // 不是自动定位的才调用接口
  //     queryBranchDataById('other');
  //   }
  //   queryFeeFileData();
  //   querySzType();
  //   // queryUnit();
  // }

  // window.addEventListener('keydown', copyAndPaste);
});
onActivated(() => {
  bus.off('handleCopyEvent');
  bus.on('handleCopyEvent', ({ event, name }) => {
    if (name === 'subItemProject') copyAndPaste(event);
  });
  bus.off('moveDeData');
  bus.on('moveDeData', type => {
    moveDeData(type);
  });
  insetBus(bus, projectStore.componentId, 'subItemProject', async data => {
    console.log('bbb', data);
    if (
      !['insert-subItem', 'supplement'].includes(data.name) &&
      !(await showInfo())
    )
      return;
    if (data.name === 'insert-subItem') {
      console.log('insert-subItem', data);
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(
          data.activeKind === '03' ? '-1' : data.activeKind,
          currentInfo.value
        );
      }
    }
    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
      } else {
        bcData(data);
      }
    }
    if (data.name === 'Import-project') {
      importProjectFun(data.activeKind);
    }
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'code-reset') {
      batchRefresh();
    }
    if (data.name === 'vertical-transport') showModel('zscy');
    if (data.name === 'superelevation') showModel('zscg');
    if (data.name === 'installation-costs') showModel('azfy');
    if (data.name === 'lock-subItem') allLock();
    if (data.name === 'component-matching') checkOnline(data);
  });
});
const importProjectFun = kind => {
  if (kind === '01') {
    importExcelVisible.value = true;
  }
  if (kind === '02') {
    api
      .importYgs({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        unitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        console.log(
          '🚀 ~ file: AsideTree.vue:497 ~ csProject.importYsfFile ~ res:',
          res
        );
        if (res.status !== 200) {
          return message.error(res.message);
        }
        importProjectVisible.value = true;
        nextTick(() => {
          console.log(importProjectRef.value);
          importProjectRef.value.init(res.result);
        });
      });
  }
};
const checkOnline = async data => {
  if (Object.prototype.hasOwnProperty.call(data, 'activeKind')) {
    if (data.activeKind === '01') {
      //点击组件方案匹配
      comMatchModal.value = true;
    } else if (data.activeKind === '02') {
      //点击组件筛选
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
    }
  } else {
    const isOnline = checkisOnline(true);
    isOnline
      ? data.options.forEach(item => (item.isValid = true))
      : data.options.forEach(item => (item.isValid = false));
  }
};
onBeforeUnmount(() => {
  // window.removeEventListener('keydown', copyAndPaste);
});
// const queryBranchDataById = () =>{
//   let apiData = {
//       constructId: projectStore.currentTreeGroupInfo?.constructId,
//       unitId: projectStore.currentTreeInfo?.id,
//     };
//     console.log('apiData---', apiData, api.getList);
//     api.getDeTree4Unit(apiData).then(res => {
//       console.log('==========tableData', tableData.value);
//     });
// }
/**
 *
 * @param {*} event
 * @param {*} isHandCopy 是否手动执行复制操作
 */
const copyAndPaste = (event, isHandCopy = false) => {
  // 如果选中数据为空，情景1，刚开始进入页面，2点击了input,然后点击空白处
  if (!selectData.value || !selectData.value?.data?.length) {
    frameSelectRef.value?.isBranchCopy([currentInfo.value?.sequenceNbr]);
  }

  if (isHandCopy) {
    copyFun();
  }

  if (event.ctrlKey && event.code === 'KeyC') {
    copyFun();
  }
  if (event.ctrlKey && event.code === 'KeyV') {
    if (!vexTable.value.getSelectedCell()) return; //vexTable.value.getSelectedCell()如果当前表格不是选中，就不进行ctr+v
    pasteFun();
  }
};
const batchRefresh = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否确认刷新数据？',
    confirm: () => {
      api.batchRefresh(apiData).then(res => {
        if (res.status === 200 && res.result) {
          page.value = 1;
          message.success('项目编码重刷成功');
          queryBranchDataById();
        }
      });
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const copyFun = () => {
  if (!selectData.value) {
    message.error('暂无选中数据');
  } else {
    if (selectData.value.isCopy) {
      copyData.value = selectData.value;
      let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
      };
      api.copyQdDeFbData(apiData).then(res => {
        if (res.status === 200 && res.result) {
          message.success('已复制');
        }
      });
    } else {
      message.error(selectData.value.msg);
    }
  }
};
const pasteFun = async () => {
  let clipboardText;
  const clipPromise = navigator.clipboard.readText();
  await clipPromise.then(function (clipText) {
    //粘贴板粘贴的数据
    clipboardText = clipText;
    if (!copyData.value && clipboardText) {
      copyData.value = clipboardText;
    }
  });
  // console.log('clipboardText', clipboardText);
  if (!copyData.value) {
    message.error('暂无复制数据');
  } else {
    if (!frameSelectRef.value.getRowCurrent()) {
      return message.error('请选中需要粘贴行！');
    } else {
      let row = frameSelectRef.value.getRowCurrent();
      try {
        await frameSelectRef.value.frameSelectJs.isPasteBranch(
          row,
          copyData.value
        );
        console.log('粘贴数据到此页面：', copyData.value);
        batchPasteQdDeData();
        // frameSelectRef.value.clearSelect();
        // copyData.value = null;
        // selectData.value = null;
      } catch (error) {
        // message.error(error);
      }
    }
  }
};

const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  selectData.value = val;
};

// 定位方法
const posRow = sequenceNbr => {
  currentInfo.value = { sequenceNbr };
  queryBranchDataById('other', sequenceNbr);
};

const addType = () => {
  console.log('插入类型');
};

const bcHandle = async () => {
  if (!(await showInfo())) return;
};

// 删除数据
const deleteType = async () => {
  isBatchDelete.value = false;
  if (!(await showInfo())) {
    return;
  }
  if (currentInfo.value.kind === '00') {
    message.warning('该行不可删除');
    return;
  }
  // deleteList.value = [];
  // if (selectData.value && selectData.value.data?.length > 1) {
  //   isBatchDelete.value = true;
  // } else {
  //   currentInfo.value.optionMenu.forEach(item => {
  //     if (item === 4 || item === 5) {
  //       deleteList.value.push(item);
  //     }
  //   });
  // }
  deleteVisible.value = true;
};

const cancel = () => {
  deleteVisible.value = false;
};
const addData = async (kind, row) => {
  console.log('addData', kind, row);
  if (!(await showInfo())) return;
  let parentId = '';
  if (
    (kind === row.kind && ['00', '01'].includes(row.kind)) ||
    (deMapFun.isDe(row.kind) && kind === '-1') ||
    (kind === '01' && row.kind === '02')
  ) {
    if (row.kind === '02') kind = '02';
    parentId = row.parentId;
  } else {
    parentId = row.sequenceNbr;
  }
  let apiData = {
    de: {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      type: kind,
      displaySign: 0,
      parentId,
    },
    prevDeRowId: row.sequenceNbr || '',
  };
  console.log('addDataapiData', apiData);
  api.createDe(apiData).then(res => {
    if (res.status === 200 && res.result) {
      if (kind === '01' || kind === '02') {
        emits('updateMenuList');
      }
      console.log(res);
      // addDataSequenceNbr.value = res.result.data.sequenceNbr;
      // page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('插入成功');
      queryBranchDataById(tableData.value.length == 1 ? 'Reload' : 'Refresh');
    }
  });
};
// const addData = async kind => {
//   if (!(await showInfo())) return;
//   let apiData = {
//     constructId: projectStore.currentTreeGroupInfo?.constructId,
//     singleId: projectStore.currentTreeGroupInfo?.singleId,
//     unitId: projectStore.currentTreeInfo?.id,
//     rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
//     pointLine: {
//       kind: currentInfo.value.kind,
//       sequenceNbr: currentInfo.value.sequenceNbr,
//       parentId: currentInfo.value.parentId,
//       displayStatu: currentInfo.value.displayStatu,
//       displaySign: currentInfo.value.displaySign,
//     },
//     newLine: {
//       kind: kind,
//     },
//   };
//   api.addQdDeFbData(apiData).then(res => {
//     if (res.status === 200 && res.result) {
//       if (kind === '01' || kind === '02') {
//         emits('updateMenuList');
//       }
//       addDataSequenceNbr.value = res.result.data.sequenceNbr;
//       page.value = Math.ceil((res.result.index + 1) / limit.value);
//       message.success('插入成功');
//       queryBranchDataById();
//     }
//   });
// };

/**
 * 点击index事件 为什么从上面单独拿下来，因为收起分部，然后这时候多次点击，没有触发currentChangeEvent事件。所以拿不到当前行子级数据了就是空数据了
 * @param {*} row
 */
const clickIndex = row => {
  const $table = vexTable.value;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(currentInfo.value)) return;
  currentInfo.value = row;
  projectStore.SET_SUB_CURRENT_INFO(row);
  nextTick(() => {
    if (row.kind === '04') return;
    // 等选中的样式更新完，
    queryAllDataByBranchId();
  });
};

const isFlag = row => {
  //判断输入工程量表达式是否合法
  let input = row.quantityExpression.replace(
    row.originalQuantityExpression,
    ''
  );
  let flag = true;
  let inputList = input.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  const reg = /[^\d.]/g;
  inputList &&
    inputList.map(item => {
      if (reg.test(item)) {
        flag = false;
        return;
      }
    });
  return flag;
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  updateFbData(currentInfo.value, 'quantityExpression');
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = (refreshFeeFile = false) => {
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  DJGCrefreshFeeFile.value = refreshFeeFile ? true : false;
  queryBranchDataById();
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = type => {
  materialVisible.value = false;
  if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    queryBranchDataById();
  }
  queryRule();
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  updateFbData(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

const changeStatus = row => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // updateFbData(row, 'seq');
};

const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.openTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById('load');
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.closeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById('load');
    }
  });
};

const toggleMethod = ({ expanded, row }) => {
  if (expanded) {
  } else {
  }
  // row.closeFlag = expanded
  // updateFbData(row)
  return true;
};

const cellDBLClickEvent = async ({ row, column }) => {
  console.log('column', column);
  console.log('column--------==', row.kind);
  if (['deCode', 'projectAttr'].includes(column.field) && !(await showInfo()))
    return;
  if (['00', '01', '02'].includes(row.kind)) return;
  if (column.field === 'deCode') {
    console.log('openeen');
    indexVisible.value = true;
    dataType.value = row.kind;
  } else if (column.field === 'projectAttr') {
    isAttrContent.value = true;
    setTimeout(() => {
      isAttrContent.value = false;
    }, 100);
  }
  currentInfo.value = row;
};

// 表格单击事件
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  if (dbClickTime < 250) {
    if (row.isTempRemove === 1) {
      return false;
    }
    cellDBLClickEvent({ row, column });
    // 前后点击相差250毫秒触发双击
  }
  const $table = vexTable.value;
  if ($event.ctrlKey) {
    const document1 = document.querySelector('.multiple-check');
    if (document1) {
      const firstInfo = $table.getRowNode(document1);
      $table.setCurrentRow(firstInfo.item);
      currentInfo.value = firstInfo.item;
      projectStore.SET_SUB_CURRENT_INFO(firstInfo.item);
    }
  }
  if (row.isLocked) return false;
  return true;
};

// const currentQdDeInfo = row => {
//   // let apiData = {
//   //   unit: row.unit,
//   //   sequenceNbr: row.sequenceNbr,
//   // };
//   fillFromIndexPage(row);
//   // console.log('1111111111', apiData.formData);
//   // addData(row.deName ? '04' : '03', apiData);
// };
const fillFromIndexPage = (row, type) => {
  console.log('🚀 ~ currentInfoReplace ~ row:', type, row);
  indexLoading.value = true;
  let apiName =
    type === 1 ? 'createDeRowAppendBaseDe' : 'createDeRowAppendBaseResource';
  let apiData = {
    deRow: {
      sequenceNbr: null,
      type: '03',
      prevRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
      parentId: JSON.parse(JSON.stringify(currentInfo.value)).parentId,
      displaySign: 0,
      name: '',
      code: '',
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
    },
    [type === 1 ? 'baseDeId' : 'resourceId']: row.sequenceNbr,
  };
  console.log('api[apiName](apiData)', apiName, apiData);
  api[apiName](apiData).then(res => {
    indexLoading.value = false;
    console.log(res);
    if (res.status === 200) {
      console.log('createDeRowAppendBaseDe', row);
      if (['03', '04', '-1'].includes(row.kind) && row.isDeResource === 0) {
        addDeInfo.value = res.result;
        queryRcjDataByDeId();
      }
      message.success(type === 1 ? '插入成功' : '替换成功');
      queryBranchDataById();
      addDataSequenceNbr.value = res.result.sequenceNbr;
    }
  });
};
// 插入功能
const currentInfoReplace = row => {
  console.log(row, currentInfo.value);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deStandardId: row.sequenceNbr,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    isDe: row.isDe,
  };
  let apiName = 'fillFromIndexPage';
  indexLoading.value = true;
  api[apiName](apiData).then(res => {
    console.log('成功！', row, res);
    indexLoading.value = false;
    if (res.status === 200) {
      // if (
      //   currentInfo.value?.kind !== '03' &&
      //   currentInfo.value?.kind !== '04' &&
      //   row.kind === '03'
      // ) {
      // } else {
      //   isIndexAddInfo.value = true;      // }

      if (
        apiName === 'fillFromIndexPage' &&
        res.result.type === '03' &&
        res.result.isDeResource === 0
      ) {
        // 插入子目
        addDeInfo.value = res.result;
        console.log('addDeInfo.value', addDeInfo.value);
        queryRcjDataByDeId();
      }
      // else if (apiName === 'saveQdAndDeArray') {
      //   addDataSequenceNbr.value = res.result?.saveQdResult?.data?.sequenceNbr;
      // } else {
      //   addDataSequenceNbr.value = res.result.data.sequenceNbr;
      //   page.value = Math.ceil((res.result.index + 1) / limit.value);
      //   // if (row.kind === '01' || row.kind === '02') {
      //   //   emits('updateMenuList');
      //   // }
      // if (row.kind === '04' && row.isDeResource === 0) {
      //   addDeInfo.value = res.result.data;
      //   queryRcjDataByDeId();
      //   // standardVisible.value = true;
      // }
      // }
      message.success('替换成功');
      queryBranchDataById();
    }
  });
};

const contextMenu = () => {
  let tempList = xeUtils.clone(menuList.value, true);
  addRowVisible(currentInfo.value);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  console.log('tempList', tempList);
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options =
    tempList;
};

const bcContextMenu = () => {
  let tempList = xeUtils.clone(bcMenuList.value, true);
  addRowVisible(currentInfo.value);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  bcContextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = async item => {
  if (!(await showInfo())) return;
  deCode.value = '';
  if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

// const querySzType = () => {
//   api
//     .querySzType({ constructId: route.query.constructSequenceNbr })
//     .then(res => {
//       if (res.status === 200 && res.result) {
//         szTypeList.value = res.result;
//       }
//     });
// };

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      console.log('queryUnit', api.queryUnit());
      unitList.value = res.result;
    }
  });
};

const delFbData = type => {
  console.log(type);
  if (deleteLoading.value) return;
  deleteLoading.value = true;
  // if (isBatchDelete.value) {
  //   let index = tableData.value.findIndex(
  //     x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
  //   );
  //   page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
  //   delBatchData();
  //   return;
  // }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value.sequenceNbr)),
  };
  console.log(apiData);

  // let index = tableData.value.findIndex(
  //   x => x.sequenceNbr === currentInfo.value.sequenceNbr
  // );
  // if (isBatchDelete.value) {
  //   let index = tableData.value.findIndex(
  //     x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
  //   );
  //   page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
  //   delBatchData();
  //   return;
  // }
  let index = tableData.value.findIndex(
    x => x.sequenceNbr === currentInfo.value.sequenceNbr
  );
  console.log(index, tableData.value[index - 1]);
  if (tableData.value[index].kind === '03') {
    currentInfo.value = tableData.value[index - 1];
  } else {
    if (index === tableData.value.length - 1) {
      currentInfo.value = tableData.value[index - 1];
    } else {
      currentInfo.value = tableData.value[index + 1];
    }
  }
  api
    .delFbData(apiData)
    .then(res => {
      console.log(res);
      if (res.status === 200) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');

        if (
          currentInfo.value.kind === '01' ||
          currentInfo.value.kind === '02'
        ) {
          emits('updateMenuList');
        }
        queryBranchDataById();
      }
    })
    .catch(err => {
      deleteLoading.value = false;
    });
};

const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'add',
          name: '插入',
          children: [
            {
              code: 0,
              name: '添加分部',
              kind: '01',
              visible: true,
              disabled: false,
            },
            {
              code: 1,
              name: '添加子分部',
              kind: '02',
              visible: true,
              disabled: false,
            },
            {
              code: 2,
              name: '添加子目',
              kind: '-1',
              visible: true,
              disabled: false,
            },
          ],
        },
        // {
        //   code: 'copy',
        //   name: '复制',
        //   visible: true,
        //   disabled: false,
        // },
        // {
        //   code: 'paste',
        //   name: '粘贴',
        //   visible: true,
        //   disabled: false,
        // },
        {
          code: 'delete',
          name: '删除',
          visible: true,
          disabled: true,
        },
        // {
        //   code: 'lock',
        //   name: '清单锁定',
        //   visible: true,
        //   disabled: false,
        // },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'cancelTempDelete',
          name: '取消临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child3',
          name: '批量取消临时删除',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: true,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: true,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'batchDelete',
          name: '批量删除',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'batchDelete-child1',
              name: '批量删除所有临时删除项',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'batchDelete-child2',
              name: '批量删除所有工程量为0项',
              type: 2,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'generateMainMaterials',
          name: '根据定额名称生成主材',
          visible: false,
          disabled: true,
        },
        {
          code: 'generateDevice',
          name: '根据定额名称生成设备',
          visible: false,
          disabled: true,
        },
      ],
    ],
  },
  async visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    currentInfo.value = row;
    projectStore.SET_SUB_CURRENT_INFO(row);
    vexTable.value.setCurrentRow(row);
    options.forEach(list => {
      console.log('list', list, row);
      list.forEach(async (item, index) => {
        if (!copyData.value && item.code === 'paste') {
          item.disabled = true;
        }
        if (copyData.value && item.code === 'paste') {
          item.disabled = false;
          try {
            await frameSelectRef.value.frameSelectJs.isPasteBranch(
              row,
              copyData.value
            );
            item.disabled = false;
          } catch (error) {
            item.disabled = true;
          }
        }
        if (item.code === 'delete') {
          if (currentInfo.value.kind !== '00') {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        } else if (item.code === 'lock') {
          if (currentInfo.value.kind === '03') {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
          if (currentInfo.value.isLocked) {
            item.name = '清单解锁';
          } else {
            item.name = '清单锁定';
          }
        } else if (item.code === 'tempDelete') {
          // let parentInfo = renderedList.value.filter(
          //   x => x.sequenceNbr === currentInfo.value.parentId
          // )[0];
          // if (
          //   (currentInfo.value.kind === '03' && !currentInfo.value.isLocked) ||
          //   (currentInfo.value.kind === '04' && !parentInfo.tempDeleteFlag)
          // ) {
          //   item.disabled = false;
          // } else {
          //   item.disabled = true;
          // }
          // if (currentInfo.value.isTempRemove==1) {
          //   item.name = '取消临时删除';
          // } else {
          //   item.name = '临时删除';
          // }
        }
        if (row.type !== '03' && row.type !== '04') {
          if (item.code === 'tempDelete' || item.code === 'cancelTempDelete') {
            item.visible = false;
          }
        } else if (currentInfo.value.isTempRemove == 1) {
          if (item.code === 'tempDelete') {
            item.visible = false;
          }
          if (item.code === 'cancelTempDelete') {
            item.visible = true;
          }
        } else {
          if (item.code === 'tempDelete') {
            item.visible = true;
          }
          if (item.code === 'cancelTempDelete') {
            item.visible = false;
          }
        }
        if (
          deMapFun.isDe(row.kind) &&
          (item.code === 'generateMainMaterials' ||
            item.code === 'generateDevice')
        ) {
          item.visible = true;
          if (['03', '04', '05'].includes(row.type)) {
            item.disabled = false;
          }
        }
        if (item.children && !['batchDelete', 'noteList'].includes(item.code)) {
          item.disabled = false;
          await addRowVisible(row);
          console.log('row------', row);
          item.children.forEach(childItem => {
            childItem.disabled = true;
            currentInfo.value.optionMenu.forEach(child => {
              if (child === childItem.code) {
                childItem.disabled = false;
              }
            });
          });
        }
      });
    });
    return true;
  },
});

const addRowVisible = async row => {
  if (row.displaySign === 2) {
    let data = originalTableData.value.find(
      a => a.sequenceNbr === row.sequenceNbr
    );
    let res = await api.queryDeChildType({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      singleId: '',
      deRowId: data.deRowId,
    });

    let children = res.result.map(a => {
      return { kind: a };
    });
    row.children = children;
    console.log('res', res);
    methodVisible(row);
  } else {
    methodVisible(row);
  }
  function methodVisible(row) {
    if (row.kind === '00') {
      // 单位工程添加
      if (row.children.length === 0) {
        currentInfo.value.optionMenu = [0, 2];
      } else if (
        row.children.filter(a => a.kind === '01' || a.kind === '02').length ===
        row.children.length
      ) {
        currentInfo.value.optionMenu = [0];
      } else if (
        row.children.filter(a => deMapFun.isDe(a.kind) || a.kind === '-1')
          .length > 0
      ) {
        currentInfo.value.optionMenu = [2];
      }
    }
    if (row.kind === '01') {
      console.log('res', row);
      // 分部添加
      if (row.children.length === 0) {
        currentInfo.value.optionMenu = [0, 1, 2];
      } else if (row.children.filter(a => a.kind === '02').length > 0) {
        currentInfo.value.optionMenu = [0, 1];
      } else if (row.children.filter(a => deMapFun.isDe(a.kind)).length > 0) {
        currentInfo.value.optionMenu = [2];
      }
      console.log(
        calculateDepth(row),
        calculateLevelToRoot(row.sequenceNbr, tableData.value[0])
      );
      if (
        calculateDepth(row) +
          calculateLevelToRoot(row.sequenceNbr, tableData.value[0]) >=
        4
      ) {
        if (row.children.filter(a => a.kind === '02').length > 0) {
          currentInfo.value.optionMenu = [0, 1];
        } else {
          currentInfo.value.optionMenu = [0];
        }
      }
    }
    if (row.kind === '02') {
      // 分部添加
      if (row.children.length === 0) {
        currentInfo.value.optionMenu = [0, 1, 2];
      } else if (row.children.filter(a => a.kind === '02').length > 0) {
        currentInfo.value.optionMenu = [0, 1];
      } else if (row.children.filter(a => deMapFun.isDe(a.kind)).length > 0) {
        currentInfo.value.optionMenu = [0, 2];
      }
      if (
        calculateDepth(row) +
          calculateLevelToRoot(row.sequenceNbr, tableData.value[0]) >=
        4
      ) {
        if (row.children.filter(a => a.kind === '02').length > 0) {
          currentInfo.value.optionMenu = [0, 1];
        } else {
          currentInfo.value.optionMenu = [0, 2];
        }
      }
    }
    if (deMapFun.isDe(row.kind)) {
      // 分部添加
      currentInfo.value.optionMenu = [2];
    }
  }
};
function calculateDepth(node) {
  if (!node.children || node.children.length === 0) {
    return 0; // Leaf node, depth is 0
  } else {
    let maxChildDepth = 0;
    if (node.children.filter(a => deMapFun.isDe(a.kind)).length == 0) {
      for (let child of node.children) {
        const childDepth = calculateDepth(child);
        maxChildDepth = Math.max(maxChildDepth, childDepth);
      }
    }
    return maxChildDepth + 1; // Add 1 to include current node
  }
}
function calculateLevelToRoot(nodeId, treeData) {
  // 辅助函数，用于递归地查找节点
  function findNode(currentNode, targetId, currentLevel) {
    if (currentNode.sequenceNbr === targetId) {
      return currentLevel;
    }
    if (currentNode.children) {
      for (let child of currentNode.children) {
        const result = findNode(child, targetId, currentLevel + 1);
        if (result !== -1) {
          return result;
        }
      }
    }
    return -1;
  }

  // 调用辅助函数来计算层级数
  const levelFromRoot = findNode(treeData, nodeId, 0);
  return levelFromRoot;
}
const contextMenuClickEvent = ({ menu, row }) => {
  if (
    menu.code == 'noteList' ||
    menu.code == 'add-note' ||
    menu.code == 'edit-note' ||
    menu.code == 'del-note' ||
    menu.code == 'show-note' ||
    menu.code == 'hide-note' ||
    menu.code == 'del-all-note'
  ) {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'copy') {
    copyFun();
  } else if (menu.code === 'paste') {
    pasteFun();
  } else if (menu.code === 'lock') {
    if (row.isLocked === 1) {
      fbUnLockQd();
    } else {
      fbLockQd();
    }
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl();
  } else if (menu.code === 'cancelTempDelete') {
    updateCancelDelTempStatusColl();
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2' ||
    menu.code === 'batchDelete-child3'
  ) {
    batchDeleteVisible.value = true;
    batchDataType.value = menu.type;
  } else if (menu.code === 'generateMainMaterials') {
    supplementRcjDataByDe('5');
  } else if (menu.code === 'generateDevice') {
    supplementRcjDataByDe('4');
  } else if (menu.code !== 'add') {
    addData(menu.kind, row);
  }
};
const supplementRcjDataByDe = kind => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deId: currentInfo.value.sequenceNbr,
    deRowId: currentInfo.value.deRowId,
    kind,
  };
  console.log('supplementRcjDataByDeapiData', apiData);
  api.supplementRcjDataByDe(apiData).then(res => {
    console.log('supplementRcjDataByDe', res);
    queryBranchDataById();
  });
};
const rowClassName = ({ row }) => {
  let ClassStr = '';
  if (row.kind === '00') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.isTempRemove === 1) {
    ClassStr = 'temp-delete';
  }
  return ClassStr;
};

const cellStyle = ({ row, column }) => {
  console.log('🚀 ~ cellStyle ~ column:', column.field);
  console.log('🚀 ~ cellStyle ~ row:', row.customLevel);
  if (['deCode'].includes(column.field)) {
    return {
      paddingLeft: row.customLevel * 10 + 'px',
    };
  }
};

const headerClass = column => {
  if (column?.align === 'center') {
    return 'headerCenter';
  } else if (column?.align === 'right') {
    return 'headerRight';
  } else {
    return 'headerLeft';
  }
};

const cellClassName = ({ column, row, $columnIndex }) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'deCode') {
    className += ' code-color ' + `Virtual-gsPdLeft${row.customLevel}`;
  } else if (column.field === 'index') {
    className += ' index-bg';
    // 批注提示
  } else if (column.field == 'materialName' && row?.annotations) {
    className += ' note-tips';
  }
  return className;
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

const tableColumn = ref([
  { field: 'bdCodeLevel04', title: '项目编码' },
  { field: 'bdNameLevel04', title: '项目名称' },
  { field: 'unit', title: '单位' },
]);
const tableList = ref([]);

const keyupEvent = (row, e) => {
  if (row.kind !== '03') return;
  if (e.value.length > 1) {
    const $pulldown = pulldownRef.value;
    if ($pulldown) {
      $pulldown.showPanel();
    }
    searchQdByCode(e.value);
  }
};

const cellClickEvent = ({ row }) => {
  addCurrentInfo.value = row;
  const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
  row.unit = unit;
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    const $table = vexTable.value;
    if ($table) {
      isClearEdit.value = true;
      $table.clearEdit();
    }
    if (row.unit && row.unit.length > 1) {
      showUnitTooltip.value = true;
    } else {
      updateQdByCode(row.bdCodeLevel04, row.unit[0]);
    }
    $pulldown.hidePanel();
    isClearEdit.value = false;
  }
};

// 根据编码模糊搜索标准清单
const searchQdByCode = code => {
  api.searchQdByCode({ code: code }).then(res => {
    if (res.status === 200 && res.result) {
      tableList.value = res.result;
    }
  });
};

// 通过标准编码插入清单
const updateQdByCode = (code, unit) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    unit: unit,
    isSortQdCode: isSortQdCode.value,
  };
  api.updateQdByCode(apiData).then(res => {
    if (res.status === 200 && res.result) {
      selectUnit.value = '';
      message.success('清单插入成功');
      queryBranchDataById();
    }
  });
};

const saveData = inputData => {
  // if (deCode.value) {
  //   updateQdByPage(inputData);
  // } else {
  addBcQdData(inputData);
  // }
};

const deSaveData = inputData => {
  if (deCode.value) {
    addBcDeData(inputData);
  } else {
    updateDeByPage(inputData);
  }
};

const rcjSaveData = inputData => {
  if (deCode.value) {
    addBjqBcRcjData(inputData);
  } else {
    spRcjByPage(inputData);
  }
};

// 通过修改编码补充清单替换当前行数据
const updateQdByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
  };
  api.updateQdByPage(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      deCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

const updateDeByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('appendUserDeNextRow', apiData);
  api.appendUserDeNextRow(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      deCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充清单数据
const addBcQdData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };

  api.addBcQdData(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      deCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 点击补充按钮补充定额数据
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('点击补充按钮补充定额数据', apiData);
  api.appendUserDe(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = JSON.parse(
        JSON.stringify(currentInfo.value)
      ).sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      deCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};

// 预算书 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    userResource: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('spRcjByPage', apiData);
  api.spRcjByPage(apiData).then(res => {
    addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
    message.success('人材机插入成功');
    rcjVisible.value = false;
    queryBranchDataById();
  });
};
// 预算书 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deId: currentInfo.value.sequenceNbr,
    detail: JSON.parse(JSON.stringify(inputData)),
    deRowId: props.currentInfo.deRowId,
  };
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      rcjVisible.value = false;
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'zscy':
      // 装饰垂运
      showModelType.value = 'zscy';
      showPriceTitle.value = '设置装饰垂运';
      break;
    case 'zscg':
      // 装饰超高
      showModelType.value = 'zscg';
      showPriceTitle.value = '设置装饰超高降效';
      break;
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用记取';
      break;
    // case 'csfy':
    //   // 自动计算措施费用
    //   showModelType.value = 'csfy';
    //   showPriceTitle.value = '自动记取总价措施';
    //   break;
  }
};
// 记取费用数据更新
const updateData = () => {
  isPriceModel.value = false;
  queryBranchDataById();
  queryFeeFileData();
  // querySzType();
  // queryUnit();
};

const closePriceModel = () => {
  isPriceModel.value = false;
};

// 整体锁定
const allLock = () => {
  if (lockFlag.value) {
    fbUnLockAll();
  } else {
    fbLockAll();
  }
};

// 清单整体锁定
const fbLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体解锁';
      queryBranchDataById();
    }
  });
};

// 清单整体解锁
const fbUnLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitUnLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体锁定';
      queryBranchDataById();
    }
  });
};

// 清单锁定
const fbLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 1;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      queryBranchDataById();
    }
  });
};

// 清单解锁
const fbUnLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 0;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbUnLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      queryBranchDataById();
    }
  });
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  console.log(deCode.value);
  if (deCode.value) {
    currentInfo.value.deCode = currentInfo.value.originalDeCode;
  }
};

// 清单多单位时选择单位确定事件
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  dataRef.unit = selectUnit.value;
  let obj = {
    deCode: dataRef.bdCodeLevel04,
    deName: dataRef.bdNameLevel04,
    sequenceNbr: dataRef.sequenceNbr,
    unit: dataRef.unit,
    quantityExpression: dataRef.quantityExpression,
    libraryCode: dataRef.libraryCode,
  };
  updateQdByCode(obj.deCode, obj.unit);
};

// 批量删除
const delBatchData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
  };
  api.fbBatchDelete(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      if (currentInfo.value.kind === '01' || currentInfo.value.kind === '02') {
        emits('updateMenuList');
      }
      queryBranchDataById();
    }
  });
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.searchForSequenceNbr(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData =
        await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
          res.result
        );
      selectData.value.data = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      if (handleData.length) {
        selectData.value.isCopy = true;
      }
    }
  });
};

const batchPasteQdDeData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  api.fbBatchPaste(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('粘贴成功');
      queryBranchDataById();
    } else {
      message.error('粘贴失败');
    }
  });
};

// 获取定额是否存在标准换算信息
const queryRule = () => {
  if (!addDeInfo.value?.standardId) {
    return;
  }
  let apiData = {
    standardDeId: addDeInfo.value?.standardId,
    fbFxDeId: addDeInfo.value?.sequenceNbr,
    libraryCode: addDeInfo.value?.libraryCode,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('标准换算列表参数4', apiData);
  api.queryRule(apiData).then(res => {
    console.log('标准换算列表参数结果', res);
    if (res.status === 200 && res.result) {
      if (res.result && res.result.length > 0) {
        standardVisible.value = true;
        standardData.value = res.result;
      }
    }
  });
};

// 获取人材机明细数据
const queryRcjDataByDeId = (bol = true, deItem = null) => {
  let apiData = {
    deRowId: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  console.log('// 获取人材机明细数据', apiData);
  if (!apiData.deRowId) return;
  api.queryRcjDataByDeId(apiData).then(res => {
    // console.log('定额明细数据', res);
    if (res.status === 200) {
      if (res.result?.length > 0) {
        ishasRCJList.value = true;
      }
      console.log('  mainMaterialTableData.value', mainMaterialTableData.value);
      if (bol) {
        mainMaterialTableData.value = res.result.filter(
          x => x.kind === 5 && x.marketPrice === 0
        );
        if (mainMaterialTableData.value.length > 0) {
          materialVisible.value = true;
        } else {
          queryRule();
        }
      }
    }
  });
};

/**
 * 拖动了高度
 */
const dragHeight = xeUtils.debounce(() => {
  console.log('修改了高度');
  onDragHeight();
}, 200);

// 预算书定额上移下移
const moveDeData = type => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    qdId: currentInfo.value.parentId,
    deId: currentInfo.value.sequenceNbr,
    operateAction: type === 1 ? 'up' : 'down',
    type: 'fbfx',
  };
  api.moveDeData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('移动成功');
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      queryBranchDataById();
    }
  });
};

// 组价方案匹配条件筛选
const filterData = val => {
  let tempList = [];
  tableData.value = [];
  if (val.length === 0 || !val) {
    tableData.value = originalTableData.value;
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  } else {
    originalTableData.value.forEach(item => {
      if (val.includes(item.matchStatus)) {
        tempList.push(item.sequenceNbr);
      }
    });
    for (let i = 0; i < originalTableData.value.length; i++) {
      if (
        tempList.includes(originalTableData.value[i].sequenceNbr) ||
        tempList.includes(originalTableData.value[i].parentId)
      ) {
        tableData.value.push(originalTableData.value[i]);
      }
    }
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  }
  const initList = init(tableData.value);
  nextTick(() => {
    initList();
  });
};

defineExpose({
  copyAndPaste,
  posRow,
});
</script>

<style lang="scss" scoped>
.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}

.multiple-select {
  width: 35px;
  height: 16px;
  line-height: 16px;
  margin-left: -10px;
  //text-indent: 10px;
  cursor: pointer;
}
.subItem-project-gs {
  background: #ffffff;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    height: 100%;
    //user-select: none;
    ::v-deep(.vxe-table) {
      .vxe-icon-caret-right:before {
        position: relative;
        top: 1px;
        content: '+';
      }
      .rotate90 {
        transform: rotate(180deg) !important;
      }
      .rotate90:before {
        content: '-';
      }
    }
    ::v-deep(.vxe-table .row-unit) {
      background: #f0ecf2;
    }
    ::v-deep(.vxe-table .row-sub) {
      background: #f9f7fa;
    }
    ::v-deep(.vxe-table .row-qd) {
      background: #e9eefa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.vxe-table .code-color) {
      color: #a73d3d;
    }
    ::v-deep(.vxe-table .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.vxe-table .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      text-decoration: line-through;
    }
  }
  .quota-content {
    height: 100%;
    //overflow: auto;
    //user-select: none;
    // user-drag: none;
  }
  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}
.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 200px;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}
.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.code {
  margin-left: 8px;
}
</style>
