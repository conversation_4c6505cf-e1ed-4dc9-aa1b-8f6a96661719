<script setup>
import {nextTick, onActivated, onMounted, reactive, ref, watch} from "vue";
import { useElectronicLabel } from '@/views/projectDetail/electronicLabel/hooks/useElectronicLabel.js';
import TabMenu from "@/views/projectDetail/electronicLabel/components/components/TabMenu.vue";
import api from "@/api/electronicLabel.js";
import {message} from "ant-design-vue";
import { electronicLabel } from '@/views/projectDetail/electronicLabel/store/electronicLabelStore';
const electronicLabelStore = electronicLabel();
const props =  defineProps(['activeKey','treeActiveData']);
// 上一项 下一项
let { tableOptions, differenceItems, gridRef,listActiveList,gridOptionsData } = useElectronicLabel();
const gridOptionsTop = reactive({
  ...tableOptions,
  height: '100%',
  // scrollY: { enabled: false, gt: 0},
  columns: [
    { field: 'seq', width: 50, title: '',slots: { default: 'seqDefault' } },
    {
      title: '最新招标清单项目',
      children: [
        { field: 'bdCode', title: '项目编码',showOverflow: true,width:140, treeNode: true },
        { field: 'name', title: '项目名称', minWidth: 120,showOverflow: true },
        { field: 'unit', title: '单位', minWidth: 40, showOverflow: true },
        { field: 'quantity', title: '工程量',showOverflow: true, minWidth:80 },
        { field: 'projectAttr', title: '项目特征', minWidth: 120,showOverflow: true },
      ]
    },
    {
      title: '当前对应匹配清单项目',
      children: [
        { field: 'itemBillProjects.bdCode', title: '项目编码',width:120,showOverflow: true },
        { field: 'itemBillProjects.name', title: '项目名称', minWidth: 120,showOverflow: true },
        { field: 'itemBillProjects.unit', title: '单位',minWidth: 40, showOverflow: true },
        { field: 'itemBillProjects.quantity', title: '工程量',showOverflow: true,minWidth:80 },
        { field: 'itemBillProjects.projectAttr', title: '项目特征', minWidth: 120,showOverflow: true },
      ]
    },
    { field: 'updateExplanation', title: '更新说明',showOverflow: true,minWidth: 100},
  ],
  data: []
});
const gridOptionsBottom = reactive({
  ...tableOptions,
  checkboxConfig: {
    labelField: 'id',
    reserve: true,
    highlight: true,
    range: true,
    showHeader: false
  },
  treeConfig:{},
  minHeight: '110',
  height:'100%',
  columns: [
    { field: 'seq', width: 50, title: '',slots: { default: 'seqDefault' } },
    {field: 'updateExplanation', type: 'checkbox',title: '保存到新工程',width:120},
    {field: 'bdCode', title: '项目编码', width: 120,showOverflow: true},
    {field: 'name', title: '项目名称',showOverflow: true},
    {field: 'unit', title: '单位',width: 60, showOverflow: true},
    {field: 'quantity', title: '工程量', showOverflow: true},
    {field: 'projectAttr', title: '项目特征', showOverflow: true},
    {field: 'zjfPrice', title: '单价',width: 100, showOverflow: true},
    {field: 'zjfTotal', title: '合价',width: 100, showOverflow: true},
  ],
  data: []
})
const tabMenuRef = ref(null);
const inputQuery = ref({
  mc: '',
  bm: ''
});
// 查询函数
const fuzzySearch = (searchTerm, items, type) => {
  // 使用正则表达式创建模糊匹配
  const regex = new RegExp(searchTerm, 'gi');
  // 使用filter方法进行模糊查询并返回结果
  return items.filter(item => {
    regex.lastIndex = 0; //这里我将 lastIndex 重置为 0
    return regex.test(item[type]);
  });
}
//搜索
const onSearch = (type) => {
  const fullData = gridOptionsBottomData.value;
  let searchTerm = inputQuery.value.mc;
  if(type === 'bdCode'){
    searchTerm = inputQuery.value.bm;
  }
  gridOptionsBottom.loading = true;
  const results = fuzzySearch(searchTerm, fullData, type);
  gridOptionsBottom.loading = false;
  console.log('加载后的数据:', results);
  vxeTableRefTb.value.reloadData(results);//解决树不展开bug
  Array.isArray(results) && results.length && vxeTableRefTb.value.setCurrentRow(results[0]);
};
const vxeTableRefTb = ref(null);
const loading = ref(false);
const gridOptionsBottomData = ref([]);
const gridOptionsTopData = ref({});// 缓存选中项
const getScrollData = ref(null);
//匹配与不匹配
const doesItMatch = async (type) => {
  console.log('doesItMatch', type)
  let zbCurrentRecord = gridRef.value.getCurrentRecord();
  let tbCurrentRecord = vxeTableRefTb.value.getCurrentRecord();
  if(zbCurrentRecord?.kind !== '03'){
    message.error('请选择清单数据进行匹配');
    return null;
  }
  console.log(gridRef.value.getCurrentRecord(),'111111111111111');
  console.log(vxeTableRefTb.value.getCurrentRecord(),'2222222222222');
  const mapping = {
    'correct': 'compareItemBillMatch',
    'deny': 'compareItemBillCancelMatch',
  }
  let apiData = {};
  if(electronicLabelStore.treeActive?.levelType == 3){
    apiData.zbConstructId = electronicLabelStore.treeActiveList[0].key;
    apiData.singleId = electronicLabelStore.treeActiveList[1].key;
    apiData.unitId = electronicLabelStore.treeActiveList[2].key;
  }
  apiData.zbSequenceNbr = zbCurrentRecord.sequenceNbr;
  apiData.tbSequenceNbr = tbCurrentRecord.sequenceNbr;
  console.log(apiData,'apiData');
  try {
    loading.value = true;
    console.log(`api-${[mapping[type]]}参数`, apiData);
    const res = await api[mapping[type]](apiData);
    console.log(`api-${[mapping[type]]}返回值`, res);
    if (res.status === 200) {
      gridOptionsTopData.value = gridRef.value.getCurrentRecord();
      getScrollData.value = gridRef.value.getScroll();
      console.log(gridRef.value.getScroll(),5555555555);
      await selectFbfx();
      if(type === 'correct'){
        message.success('匹配成功');
      }else {
        message.success('取消匹配成功');
      }
    } else {
      message.error(res.message);
    }
  } catch (e) {
    console.error(`api-${[mapping[type]]}=>catch`,e);
    message.error(e.message);
  }finally {
    loading.value = false;
  }
}
const selectFbfx= async ()=>{
  let apiData = {
    tbConstructId:electronicLabelStore.ppjg.tbId,
  };
  if(electronicLabelStore.treeActive?.levelType == 3){
    apiData.zbConstructId = electronicLabelStore.treeActiveList[0].key;
    apiData.singleId = electronicLabelStore.treeActiveList[1].key;
    apiData.unitId = electronicLabelStore.treeActiveList[2].key;
  }
  console.log(electronicLabelStore.treeActiveList);
  try {
    gridOptionsTop.loading = true;
    gridOptionsBottom.loading = true;
    loading.value = true;
    console.log(`api-selectFbfx参数`,apiData);
    const res = await api.selectFbfx(apiData);
    console.log(`api-selectFbfx返回值`,res);
    if(res.status === 200){
      let { zbItemBillProjects,mismatched } = res.result;
      zbItemBillProjects = zbItemBillProjects.map((item,index)=>{return {...item,indexRow:index+1}});//添加序号
      // 有差异项的数据过滤
      listActiveList.value = zbItemBillProjects.filter(item => {
        return !!item.updateExplanation;
      }).map(item =>item.sequenceNbr);
      if(Array.isArray(zbItemBillProjects)){
        gridOptionsData.value = zbItemBillProjects;
        // gridOptionsTop.data = zbItemBillProjects;
        gridRef.value.reloadData(zbItemBillProjects);//解决树不展开bug reloadData
        let currentRow = null;
        if(Object.keys(gridOptionsTopData.value).length){
          currentRow = zbItemBillProjects.find(item => item.sequenceNbr === gridOptionsTopData.value.sequenceNbr);
        }else{
          currentRow = zbItemBillProjects[0]
        }
        zbItemBillProjects.length > 0 && gridRef.value.setCurrentRow(currentRow);
        console.log(getScrollData.value,888888888888);
        setTimeout(()=>{
          zbItemBillProjects.length > 0 && gridRef.value.scrollTo(getScrollData.value.scrollLeft, getScrollData.value.scrollTop);
          // zbItemBillProjects.length > 0 && gridRef.value.scrollToRow(currentRow);
        },300)
      }
      if(Array.isArray(mismatched)){
        gridOptionsBottomData.value = mismatched;
        vxeTableRefTb.value.reloadData(mismatched);//解决树不展开bug
        mismatched.length > 0 && vxeTableRefTb.value.setCurrentRow(mismatched[0]);
      }
    }else{
      message.error(res.message);
    }
  }catch (e) {
    console.error(`api-selectFbfx=>catch`,e);
    message.error(e.message);
  }finally {
    loading.value = false;
    gridOptionsTop.loading = false;
    gridOptionsBottom.loading = false;
  }
}
const checkboxChange = ({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event })=>{
  console.log(row,checked);
  saveToNewProject(row,checked);
}

const saveToNewProject = async (row,checked)=>{
  let apiData = {
    tbConstructId: electronicLabelStore.ppjg.tbId,
    zbConstructId: electronicLabelStore.treeActiveList[0].key,
    singleId:electronicLabelStore.treeActiveList[1].key,
    unitId: electronicLabelStore.treeActiveList[2].key,
    tbSequenceNbr: row.sequenceNbr,
    saveFlag: checked
  };
  try {
    gridOptionsBottom.loading = true;
    console.log(`api-saveToNewProject参数`,apiData);
    const res = await api.saveToNewProject(apiData);
    console.log(`api-saveToNewProject返回值`,res);
    if(res.status === 200){

    }else{
      message.error(res.message);
    }
  }catch (e) {
    console.error(`api-saveToNewProject=>catch`,e);
    message.error(e.message);
  }finally {
    gridOptionsBottom.loading = false;
  }
}

// 界面已完全加载完成，需要执行的逻辑方法
const initPage = (arge)=>{
  console.log('initPage',arge)
  // tabMenuRef.value.setFilter(treeActiveData.value)
  selectFbfx();
};
let stableHeight = ref(270);
let splitRef = ref(null);
const dragHeight = h => {
  console.log('dragHeight',h,gridOptionsBottom.minHeight);
  gridOptionsTop.maxHeight = h;
  // gridOptionsBottom.maxHeight = 500;
};
defineExpose({
  initPage
})
</script>

<template>
  <split horizontal ratio="4/3" :horizontalBottom="35" style="height: 100%" mode="vertical" @onDragHeight="dragHeight" ref="splitRef">
    <template #one>
      <div class="right-top">
        <a-button type="text" size="small" @click.stop.prevent='differenceItems("upper")' >上一条差异项</a-button>
        <a-button type="text" size="small" @click.stop.prevent='differenceItems("below")'>下一条差异项</a-button>
      </div>
      <div style="height:calc(100% - 34px)">
        <vxe-grid class='table-line' v-bind="gridOptionsTop" ref='gridRef' height="auto">
          <template #seqDefault="{ row,rowIndex,$rowIndex }">
            <span style="display: inline-block;width: 10px">
              <icon-font v-if="row.kind==='03' && !row.updateExplanation" type="icon-duigou"/>
              <icon-font v-else-if="row.kind==='03' && row.updateExplanation === '新标书新增项'" type="icon-zengjia-lan"/>
              <icon-font v-else-if="row.kind==='03'"  type="icon-tishi-cheng"/>
            </span>
            <span style="margin-left: 5px">{{ row.indexRow }}</span>
          </template>
        </vxe-grid>
      </div>
    </template>
    <template #two>
      <TabMenu :tabContent='2' ref="tabMenuRef" />
      <div class="right-top">
        <a-button type="primary" size="small" @click="doesItMatch('correct')">匹配</a-button>
        <a-button type="primary" size="small" @click="doesItMatch('deny')" style="margin-left:10px">取消匹配</a-button>
        <a-input-search size="small" v-model:value.trim="inputQuery.mc" placeholder="名称查询" style="width: 200px;margin-left:10px;"  allowClear @search="onSearch('name')" />
        <a-input-search size="small" v-model:value.trim="inputQuery.bm" placeholder="编码查询" style="width: 200px;margin-left:10px;"  allowClear @search="onSearch('bdCode')" />
      </div>
      <div style="height:calc(100% - 60px)">
        <vxe-grid v-bind="gridOptionsBottom" ref='vxeTableRefTb' @checkbox-change="checkboxChange" height="auto">
          <template #seqDefault="{row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex}">
            {{$rowIndex + 1}}
          </template>
        </vxe-grid>
      </div>
    </template>
  </split>
</template>

<style scoped lang="scss">
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';
//.edit-right {
//  height: calc( 100% - 26px );
//  .right-top {
//    font-size: 12px;
//    color: #000000;
//    margin: 5px 2px;
//  }
//}
::v-deep(.vxe-icon-checkbox-unchecked:before){
  color: #b9b9b9;
}
.table-content {
  height: 100%;
  //user-select: none;
  ::v-deep(.vxe-table) {
    // .vxe-cell--tree-node{
    //   // left: 0!important;
    //   padding-left: 0!important;
    // }
  }
  ::v-deep(.vxe-table .row-unit) {
    background: #e6dbeb;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #efe9f2;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #dce6fa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .row-qd .code-color) {
    color: #ce2929;
  }
  ::v-deep(.vxe-table .normal-info .code-color) {
    color: #ce2929;
    .vxe-tree-cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      //max-height: 3.0em; /* 高度为字体大小的两倍 */
      //line-height: 1.5em; /* 行高 */
      //height: auto; /* 高度为行高的两倍 */
    }
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
  ::v-deep(.surely-table-header-cell):hover {
    .icon-close-s {
      opacity: 1;
    }
  }
}
</style>