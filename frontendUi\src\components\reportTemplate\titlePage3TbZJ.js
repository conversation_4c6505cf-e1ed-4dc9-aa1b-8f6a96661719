/*
 * @Descripttion: 扉页3 投标总价
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-12-17 10:39:56
 */

export const titlePage3TbZJBookData = [
  {
    name: '扉页3 投标总价',
    deType: [12, 22], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        arRIXl: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        P2nLHO: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        TnnF8S: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        '03xwLB': {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        '79BwO-': {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        'bB0t-1': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        cWHptF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
            r: null,
          },
        },
        '5tUL5G': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        tJI9Yu: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rkQ65K: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        '9Elfug': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rD_0gK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        JmRClP: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        'yp1j0-': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        WRyhf7: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        fxVzXi: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        aIrSCx: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        YSx5OM: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        e8rH42: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        mwBzMz: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        O73yQR: {
          bd: {},
        },
        yeCCSS: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        My5OCy: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        Sye_Wz: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        xdys7m: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        sxWF0H: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        'A_vpI-': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        uErNGJ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        TnomEo: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
          },
        },
        Hp31mj: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
        },
        a2VI0l: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        v8Zdla: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        HcUrIt: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        A7ZHFv: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
          bg: null,
          n: null,
        },
        'gY-sxF': {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        UF3V_D: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        _0y71b: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
          },
        },
        HJp6QK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rGJYvc: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: null,
            t: null,
          },
        },
        elecuG: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        YMvtWh: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        oFt94g: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        gkGVcc: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        MD0hR7: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            1: {
              0: {
                v: '',
                t: 1,
                s: 'Sye_Wz',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'xdys7m',
                p: '',
              },
              2: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              3: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              4: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              5: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`投标总价`',
                t: 1,
                s: 'A_vpI-',
                p: '',
                custom: {},
              },
              1: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              2: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              3: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              4: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              5: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
              1: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              2: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              3: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              4: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              5: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                v: '`招  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{招标信息\\招标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            5: {
              0: {
                v: '`工程名称：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{项目名称}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            6: {
              0: {
                v: '` 投标总价`',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(小写)：`',
                t: 1,
                s: 'gkGVcc',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价}+`元`',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'v8Zdla',
                p: '',
              },
            },
            7: {
              0: {
                s: 'TnomEo',
                p: {
                  id: '__INTERNAL_EDITOR__DOCS_NORMAL',
                  documentStyle: {
                    pageSize: {
                      width: 215,
                      height: null,
                    },
                    marginTop: 0,
                    marginBottom: 1,
                    marginRight: 2,
                    marginLeft: 2,
                    renderConfig: {
                      horizontalAlign: 3,
                      verticalAlign: 3,
                      centerAngle: 0,
                      vertexAngle: 0,
                      wrapStrategy: 3,
                    },
                  },
                  body: {
                    dataStream: '  \r\n',
                    textRuns: [
                      {
                        ts: {
                          ff: '宋体',
                          fs: 12,
                          bl: 1,
                          ul: {
                            s: 0,
                            cl: {
                              rgb: 'rgb(0,0,0)',
                            },
                          },
                          st: {
                            s: 0,
                            cl: {
                              rgb: 'rgb(0,0,0)',
                            },
                          },
                          ol: {
                            s: 0,
                            cl: {
                              rgb: 'rgb(0,0,0)',
                            },
                          },
                          cl: {
                            rgb: 'rgb(0,0,0)',
                          },
                        },
                        st: 2,
                        ed: 3,
                      },
                    ],
                    paragraphs: [
                      {
                        startIndex: 2,
                        paragraphStyle: {
                          horizontalAlign: 3,
                        },
                      },
                    ],
                    customRanges: [],
                    customDecorations: [],
                  },
                  drawings: {},
                  drawingsOrder: [],
                  settings: {
                    zoomRatio: 1,
                  },
                  resources: [
                    {
                      name: 'SHEET_AuthzIoMockService_PLUGIN',
                      data: '{}',
                    },
                  ],
                },
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(大写)：`',
                t: 1,
                s: 'gkGVcc',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价 大写}',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            8: {
              0: {
                v: '`投  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\投标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(单位公章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            9: {
              0: {
                v: '`法定代表人或委托代理人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\法定代表人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字/盖章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            10: {
              0: {
                v: '`造价工程师或造价员：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\造价工程师}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字盖专用章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
                custom: {},
              },
            },
            11: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            12: {
              0: {
                v: '`编制时间：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\编制时间}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            13: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            14: {
              0: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              1: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              2: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              3: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              4: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              5: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 14,
          columnCount: 6,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 1,
              endRow: 1,
              startColumn: 1,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 2,
              rangeType: 0,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 3,
              endColumn: 4,
              rangeType: 0,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 0,
              endColumn: 5,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 100,
              hd: 0,
              ia: 0,
              field: 'headLine',
              rowType: '报表标题行',
            },
            2: {
              h: 48,
              hd: 0,
              field: 'headLine',
              rowType: '报表标题行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 162,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            5: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 118,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 125,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            9: {
              h: 123,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 39,
              ia: 0,
            },
            10: {
              hd: 0,
              h: 87,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            11: {
              hd: 0,
              h: 76,
              ah: 33,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            12: {
              hd: 0,
              h: 88,
              ah: 32.6,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            13: {
              hd: 0,
              h: 30,
              field: 'pageFoot',
              rowType: '页脚行 ',
            },
          },
          columnData: {
            0: {
              w: 100,
              hd: 0,
            },
            1: {
              w: 60,
              hd: 0,
            },
            2: {
              w: 55,
              hd: 0,
            },
            3: {
              w: 110,
              hd: 0,
            },
            4: {
              w: 515,
              hd: 0,
            },
            5: {
              w: 168,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 88,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
  {
    name: '扉页3 投标总价(不含甲供设备及其税金)',
    deType: [12, 22], //12.22都展示
    levelType: [1], // 1工程，2 单项，3，单位
    lanMuName: ['投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        arRIXl: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        P2nLHO: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        TnnF8S: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        '03xwLB': {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        '79BwO-': {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        'bB0t-1': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        cWHptF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
            r: null,
          },
        },
        '5tUL5G': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        tJI9Yu: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rkQ65K: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        '9Elfug': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rD_0gK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        JmRClP: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        'yp1j0-': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        WRyhf7: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        fxVzXi: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        aIrSCx: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        YSx5OM: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        e8rH42: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        mwBzMz: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        O73yQR: {
          bd: {},
        },
        yeCCSS: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        My5OCy: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        Sye_Wz: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        xdys7m: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        sxWF0H: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        'A_vpI-': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        uErNGJ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        TnomEo: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
          },
        },
        Hp31mj: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
        },
        a2VI0l: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        v8Zdla: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        HcUrIt: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        A7ZHFv: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
          bg: null,
          n: null,
        },
        'gY-sxF': {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        UF3V_D: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        _0y71b: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
          },
        },
        HJp6QK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rGJYvc: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: null,
            t: null,
          },
        },
        '4OM2ep': {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        YjBNMQ: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        k4_jeY: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        '6Pu3NH': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        YcPtBO: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            1: {
              0: {
                v: '',
                t: 1,
                s: 'Sye_Wz',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'xdys7m',
                p: '',
              },
              2: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              3: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              4: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              5: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '` 投标总价(不含甲供设备及其税金)`',
                t: 1,
                s: 'A_vpI-',
                custom: {},
                p: '',
              },
              1: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              2: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              3: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              4: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              5: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
              1: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              2: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              3: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              4: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              5: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                v: '`招  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{招标信息\\招标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            5: {
              0: {
                v: '`工程名称：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{项目名称}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            6: {
              0: {
                v: '` 投标总价`',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(小写)：`',
                t: 1,
                s: '6Pu3NH',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价}+`元`',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'v8Zdla',
                p: '',
              },
            },
            7: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(大写)：`',
                t: 1,
                s: '6Pu3NH',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价 大写}',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            8: {
              0: {
                v: '`投  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\投标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(单位公章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            9: {
              0: {
                v: '`法定代表人或委托代理人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\法定代表人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字/盖章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            10: {
              0: {
                v: '`造价工程师或造价员：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\造价工程师}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字盖专用章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
                custom: {},
              },
            },
            11: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            12: {
              0: {
                v: '`编制时间：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\编制时间}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            13: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            14: {
              0: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              1: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              2: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              3: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              4: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              5: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 14,
          columnCount: 6,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 1,
              endRow: 1,
              startColumn: 1,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 2,
              rangeType: 0,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 3,
              endColumn: 4,
              rangeType: 0,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 0,
              endColumn: 5,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 100,
              hd: 0,
              ia: 0,
              field: 'headLine',
              rowType: '报表标题行',
            },
            2: {
              h: 48,
              hd: 0,
              field: 'headLine',
              rowType: '报表标题行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 162,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            5: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 118,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 125,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            9: {
              h: 123,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 39,
              ia: 0,
            },
            10: {
              hd: 0,
              h: 87,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            11: {
              hd: 0,
              h: 76,
              ah: 33,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            12: {
              hd: 0,
              h: 88,
              ah: 32.6,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            13: {
              hd: 0,
              h: 30,
              field: 'pageFoot',
              rowType: '页脚行 ',
            },
          },
          columnData: {
            0: {
              w: 100,
              hd: 0,
            },
            1: {
              w: 60,
              hd: 0,
            },
            2: {
              w: 55,
              hd: 0,
            },
            3: {
              w: 110,
              hd: 0,
            },
            4: {
              w: 515,
              hd: 0,
            },
            5: {
              w: 168,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 88,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 0,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
  {
    name: '扉页3 投标总价',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 项，3，单位
    lanMuName: ['投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        arRIXl: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        P2nLHO: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        TnnF8S: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        '03xwLB': {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        '79BwO-': {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        'bB0t-1': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        cWHptF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
            r: null,
          },
        },
        '5tUL5G': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        tJI9Yu: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rkQ65K: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        '9Elfug': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rD_0gK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        JmRClP: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        'yp1j0-': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        WRyhf7: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        fxVzXi: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        aIrSCx: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        YSx5OM: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        e8rH42: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        mwBzMz: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        O73yQR: {
          bd: {},
        },
        yeCCSS: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        My5OCy: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        Sye_Wz: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        xdys7m: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        sxWF0H: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        'A_vpI-': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        uErNGJ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        TnomEo: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
          },
        },
        Hp31mj: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
        },
        a2VI0l: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        v8Zdla: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        HcUrIt: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        A7ZHFv: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
          bg: null,
          n: null,
        },
        'gY-sxF': {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        UF3V_D: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        _0y71b: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
          },
        },
        HJp6QK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rGJYvc: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: null,
            t: null,
          },
        },
        nw4RR8: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        n5oFwp: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        pagr1L: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        Z13e0o: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        '6joTpj': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        '-7TM5n': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '1oB4E2': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        cDOQqG: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            1: {
              0: {
                v: '',
                t: 1,
                s: 'Sye_Wz',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'xdys7m',
                p: '',
              },
              2: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              3: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              4: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              5: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: ' `投标总价`',
                t: 1,
                s: 'A_vpI-',
                custom: {},
              },
              1: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              2: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              3: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              4: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              5: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
              1: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              2: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              3: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              4: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              5: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                v: '`招  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{招标信息\\招标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            5: {
              0: {
                v: '`工程名称：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{单项名称}+{单位名称}',
                t: 1,
                s: '-7TM5n',
                custom: {},
                p: '',
              },
              4: {
                s: '-7TM5n',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            6: {
              0: {
                v: '` 投标总价`',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(小写)：`',
                t: 1,
                s: '1oB4E2',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价}+`元`',
                t: 1,
                s: 'cDOQqG',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'v8Zdla',
                p: '',
              },
            },
            7: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(大写)：`',
                t: 1,
                s: 'Z13e0o',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价 大写}',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            8: {
              0: {
                v: '`投  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\投标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(单位公章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            9: {
              0: {
                v: '`法定代表人或委托代理人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\法定代表人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字/盖章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            10: {
              0: {
                v: '`造价工程师或造价员：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\造价工程师}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字盖专用章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
                custom: {},
              },
            },
            11: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            12: {
              0: {
                v: '`编制时间：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\编制时间}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            13: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            14: {
              0: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              1: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              2: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              3: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              4: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              5: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 14,
          columnCount: 6,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 1,
              endRow: 1,
              startColumn: 1,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 2,
              rangeType: 0,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 3,
              endColumn: 4,
              rangeType: 0,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 0,
              endColumn: 5,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 100,
              hd: 0,
              ia: 0,
              field: 'headLine',
              rowType: '报表标题行',
            },
            2: {
              h: 48,
              hd: 0,
              field: 'headLine',
              rowType: '报表标题行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 162,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            5: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 118,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 125,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            9: {
              h: 123,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 39,
              ia: 0,
            },
            10: {
              hd: 0,
              h: 87,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            11: {
              hd: 0,
              h: 76,
              ah: 33,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            12: {
              hd: 0,
              h: 88,
              ah: 32.6,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            13: {
              hd: 0,
              h: 30,
              field: 'pageFoot',
              rowType: '页脚行 ',
            },
          },
          columnData: {
            0: {
              w: 100,
              hd: 0,
            },
            1: {
              w: 60,
              hd: 0,
            },
            2: {
              w: 55,
              hd: 0,
            },
            3: {
              w: 110,
              hd: 0,
            },
            4: {
              w: 515,
              hd: 0,
            },
            5: {
              w: 168,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 88,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 0,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
  {
    name: '扉页3 投标总价(不含甲供设备及其税金)',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['投标项目报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: {
        arRIXl: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        P2nLHO: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        TnnF8S: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        '03xwLB': {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        '79BwO-': {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        'bB0t-1': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        cWHptF: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
            r: null,
          },
        },
        '5tUL5G': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        tJI9Yu: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rkQ65K: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        '9Elfug': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        rD_0gK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        JmRClP: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        'yp1j0-': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        WRyhf7: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        fxVzXi: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        aIrSCx: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        YSx5OM: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        e8rH42: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        mwBzMz: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        O73yQR: {
          bd: {},
        },
        yeCCSS: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        My5OCy: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        Sye_Wz: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        xdys7m: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        sxWF0H: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        'A_vpI-': {
          ff: '宋体',
          fs: 20,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        uErNGJ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
          },
        },
        TnomEo: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
          },
        },
        Hp31mj: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
        },
        a2VI0l: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        v8Zdla: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
          },
        },
        HcUrIt: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        A7ZHFv: {
          ff: '等线',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 3,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
            t: null,
          },
          bg: null,
          n: null,
        },
        'gY-sxF': {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        UF3V_D: {
          ff: '微软雅黑 Light',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        _0y71b: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        HJp6QK: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rGJYvc: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 0,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: null,
            t: null,
          },
        },
        nw4RR8: {
          ff: '宋体',
          fs: 9,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        n5oFwp: {
          ff: '宋体',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        pagr1L: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 1,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          bg: {
            rgb: 'rgb(255,255,255)',
          },
        },
        Ax3ITn: {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            r: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        '4asC3g': {
          ff: '宋体',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 3,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            1: {
              0: {
                v: '',
                t: 1,
                s: 'Sye_Wz',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'xdys7m',
                p: '',
              },
              2: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              3: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              4: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
              5: {
                s: 'xdys7m',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: ' `投标总价(不含甲供设备及其税金)`',
                t: 1,
                s: 'A_vpI-',
                p: '',
                custom: {},
              },
              1: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              2: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              3: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              4: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
              5: {
                s: 'A_vpI-',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
              1: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              2: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              3: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              4: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
              5: {
                s: 'uErNGJ',
                v: '',
                p: '',
              },
            },
            4: {
              0: {
                v: '`招  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{招标信息\\招标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            5: {
              0: {
                v: '`工程名称：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '{单项名称}+{单位名称}',
                t: 1,
                s: '_0y71b',
                custom: {},
                p: '',
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            6: {
              0: {
                v: '` 投标总价`',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(小写)：`',
                t: 1,
                s: 'Ax3ITn',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价}+`元`',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'v8Zdla',
                p: '',
              },
            },
            7: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                v: '`(大写)：`',
                t: 1,
                s: 'Ax3ITn',
                p: '',
                custom: {},
              },
              4: {
                v: '{造价分析\\工程总造价 大写}',
                t: 1,
                s: 'rGJYvc',
                p: '',
                custom: {},
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            8: {
              0: {
                v: '`投  标  人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\投标人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(单位公章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            9: {
              0: {
                v: '`法定代表人或委托代理人：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\法定代表人}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字/盖章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            10: {
              0: {
                v: '`造价工程师或造价员：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\造价工程师}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '`(签字盖专用章)`',
                t: 1,
                s: 'uErNGJ',
                p: '',
                custom: {},
              },
            },
            11: {
              0: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              1: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              2: {
                v: '',
                t: 1,
                s: 'TnomEo',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'Hp31mj',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            12: {
              0: {
                v: '`编制时间：`',
                t: 1,
                s: 'TnomEo',
                p: '',
                custom: {},
              },
              1: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              2: {
                s: 'TnomEo',
                v: '',
                p: '',
              },
              3: {
                v: '{投标信息\\编制时间}',
                t: 1,
                s: '_0y71b',
                p: '',
                custom: {},
              },
              4: {
                s: '_0y71b',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'uErNGJ',
                p: '',
              },
            },
            13: {
              0: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              1: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              2: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              3: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
              4: {
                s: 'UF3V_D',
                v: '',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'UF3V_D',
                p: '',
              },
            },
            14: {
              0: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              1: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              2: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              3: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              4: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
              5: {
                s: 'O73yQR',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 14,
          columnCount: 6,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 1,
              endRow: 1,
              startColumn: 1,
              endColumn: 5,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 4,
              endRow: 4,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 5,
              endRow: 5,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 6,
              endRow: 6,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 8,
              endRow: 8,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 10,
              endRow: 10,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 0,
              endColumn: 2,
            },
            {
              startRow: 12,
              endRow: 12,
              startColumn: 3,
              endColumn: 4,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 2,
              rangeType: 0,
            },
            {
              startRow: 0,
              endRow: 0,
              startColumn: 3,
              endColumn: 4,
              rangeType: 0,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 0,
              endColumn: 5,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 100,
              hd: 0,
              ia: 0,
              field: 'headLine',
              rowType: '报表标题行',
            },
            2: {
              h: 48,
              hd: 0,
              field: 'headLine',
              rowType: '报表标题行',
              ah: 30,
              ia: 0,
            },
            3: {
              h: 30,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            4: {
              h: 162,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            5: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            6: {
              h: 95,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            7: {
              h: 118,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 125,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            9: {
              h: 123,
              hd: 0,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 39,
              ia: 0,
            },
            10: {
              hd: 0,
              h: 87,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ah: 32.6,
              ia: 0,
            },
            11: {
              hd: 0,
              h: 76,
              ah: 33,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            12: {
              hd: 0,
              h: 88,
              ah: 32.6,
              field: '',
              rowType: '明细行',
              dataSourceType: '',
              ia: 0,
            },
            13: {
              hd: 0,
              h: 30,
              field: 'pageFoot',
              rowType: '页脚行 ',
            },
          },
          columnData: {
            0: {
              w: 100,
              hd: 0,
            },
            1: {
              w: 60,
              hd: 0,
            },
            2: {
              w: 55,
              hd: 0,
            },
            3: {
              w: 110,
              hd: 0,
            },
            4: {
              w: 515,
              hd: 0,
            },
            5: {
              w: 168,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 88,
              hd: 0,
            },
            8: {
              w: 88,
              hd: 0,
            },
          },
          showGridlines: 0,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
];
