import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { includes } from 'lodash';
const store = projectDetailStore();
/**
 * 定额类型判断
 * @param {*} type
 * @param {*} rowType 有则判断得是工程项目重得行数据类型
 * @returns
 */
export const isDeType = (type, rowType = '') => {
  // debugger;
  if (store.currentTreeInfo.levelType < 3 && rowType) {
    return rowType === type;
  }
  return store.deStandardReleaseYear === type;
};
const getTableColumns = (emits, type) => {
  let tableColumns = [
    // 常用项
    {
      title: '序号',
      field: 'dispNo',
      width: 50,
      align: 'center',
      classType: 1,
      slot:
        store.currentTreeInfo.levelType === 3 && [7, 8].includes(+store.asideMenuCurrentInfo?.key),
      editRender: {
        enabled:
          store.currentTreeInfo.levelType === 3 && +store.asideMenuCurrentInfo?.key === 7
            ? true
            : false,
        autofocus: '.vxe-input--inner',
      },
      fixed: 'left',
    },
    {
      title: '关联',
      field: 'hasAssociation',
      width: 50,
      classType: 1,
      fixed: 'left',
      slot: true,
    },
    {
      title: '关联材料编码',
      field: 'relevancyMaterialCodeList',
      width: 100,
      classType: 1,
      fixed: 'left',
      slot: true,
    },
    // {
    //   title: '承包人材料编码',
    //   field: 'materialCodeCBR',
    //   width: 120,
    //   classType: 1,
    //   editRender: {
    //     autofocus: '.vxe-input--inner',
    //   },
    //   slot: true,
    //   cellType: 'string',
    //   fixed: 'left',
    // },
    {
      title: +store.asideMenuCurrentInfo?.key === 10 ? '承包人材料编码' : '材料编码',
      field: 'materialCode',
      width: +store.asideMenuCurrentInfo?.key === 10 ? 120 : 100,
      editRender: {
        enabled: [8, 10].includes(+store.asideMenuCurrentInfo?.key) ? true : false,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      fixed: 'left',
      cellType: 'string',
      slot: true,
    },
    {
      title: '类别',
      field: 'type',
      width: 80,
      slot: true,
      classType: 1,
      fixed: ![8, 10].includes(+store.asideMenuCurrentInfo?.key) ? 'left' : '',
      editRender: {
        enabled: store.currentTreeInfo.levelType < 3 ? false : true,
      },
    },
    {
      title: '名称',
      field: 'materialName',
      width: 150,
      editRender: {
        enabled: store.currentTreeInfo.levelType < 3 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      fixed: 'left',
      slot: true,
    },
    {
      title: '规格型号',
      field: 'specification',
      width: 100,
      editRender: {
        enabled: store.currentTreeInfo.levelType < 3 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '单位',
      field: 'unit',
      width: 60,
      editRender: {
        enabled: store.currentTreeInfo.levelType < 3 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '合计数量',
      field: 'totalNumber',
      width: 80,
      editRender: {
        enabled: ![8, 10].includes(+store.asideMenuCurrentInfo?.key) ? false : true, //暂估价+承包人未关联数据可编辑
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '风险系数%',
      field: 'riskCoefficient',
      width: 80,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '基准单价',
      field: 'benchmarkUnitPrice',
      width: 80,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '投标单价',
      field: 'tbdj',
      width: 80,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '暂定金额',
      field: 'zdje',
      width: 80,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: +store.asideMenuCurrentInfo?.key === 8 ? '合计金额' : '合价',
      field: 'hjje',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: isDeType('12')
        ? '定额价'
        : Number(store.taxMade) === 1
          ? '不含税基期价'
          : '含税基期价',
      field: 'dePrice',
      width: 80,
      classType: 1,
      slot: true,
    },
    {
      title: '市场价',
      field: 'marketPrice',
      width: 80,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '不含税市场价',
      field: 'priceMarket',
      width: 100,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '含税市场价',
      field: 'priceMarketTax',
      width: 90,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '市场价合计',
      field: 'total',
      width: 90,
      classType: 1,
      slot: true,
    },
    {
      title: '不含税市场价合计',
      field: 'priceMarketTotal',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: '含税市场价合计',
      field: 'priceMarketTaxTotal',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '税率系数(%)',
      field: 'taxRate',
      width: 60,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '标准税率(%)',
      field: 'taxRateStandard',
      width: 60,
      classType: 1,
      slot: true,
    },
    {
      title: '价格来源',
      field: 'sourcePrice',
      width: 100,
      classType: 1,
    },
    {
      title: '来源',
      field: 'source',
      width: 150,
      classType: 1,
    },
    {
      title: '是否汇总(二次分析)',
      field: 'markSum',
      width: 130,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.markSum && [1, 2].includes(Number(row.levelMark)) ? '是' : '';
      },
    },
    {
      title: '除税系数(%)',
      field: 'taxRemoval',
      width: 120,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '进项税额',
      field: 'jxTotal',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '价差',
      field: 'priceDifferenc',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '价差合计',
      field: 'priceDifferencSum',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '供货方式',
      field: 'ifDonorMaterial',
      width: 100,
      classType: 1,
      slot: true,
      editRender: {
        enabled:
          store.standardGroupOpenInfo.isOpen || +store.asideMenuCurrentInfo?.key === 10
            ? false
            : true,
      },
      exportMethod: ({ row }) => {
        return getDonorMaterialText(row.ifDonorMaterial);
      },
    },
    {
      title: '甲供数量',
      field: 'donorMaterialNumber',
      width: 100,
      classType: 1,
      slot: true,
      editRender: {
        enabled:
          store.currentTreeInfo.levelType < 3 || store.standardGroupOpenInfo.isOpen ? false : true,
        autofocus: '.vxe-input--inner',
      },
      className: ({ row }) => {
        return row.donorMaterialNumber > row.totalNumber ? 'background-red' : '';
      },
    },
    {
      title: '三材类别',
      field: 'kindSc',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '三材系数',
      field: 'transferFactor',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '是否暂估',
      field: 'ifProvisionalEstimate',
      width: 100,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.ifProvisionalEstimate && row.checkIsShow ? '是' : '';
      },
    },
    {
      title: '市场价锁定',
      field: 'ifLockStandardPrice',
      width: 100,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.ifLockStandardPrice && row.checkIsShow ? '是' : '';
      },
    },
    {
      title: '产地',
      field: 'producer',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '厂家',
      field: 'manufactor',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '品牌',
      field: 'brand',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '送达地点',
      field: 'deliveryLocation',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '质量等级',
      field: 'qualityGrade',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '是否输出',
      field: 'output',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '备注',
      field: 'remark',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '锁定',
      field: 'lock',
      width: 100,
      classType: 1,
      slot: true,
      fixed: 'right',
      exportMethod: ({ row }) => {
        return row.lock ? '是' : '';
      },
    },
  ];
  tableColumns = tableColumns.filter(item => {
    // debugger;
    const field = item.field;
    if (store.asideMenuCurrentInfo?.key !== '7' && 'output' === field) {
      return false;
    }
    if (
      (+store.asideMenuCurrentInfo?.key !== 8 &&
        ['relevancyMaterialCodeList', 'glCode', 'lock', 'zdje'].includes(field)) ||
      (+store.asideMenuCurrentInfo?.key !== 10 &&
        ['riskCoefficient', 'benchmarkUnitPrice', 'tbdj', 'hj'].includes(field))
    )
      return false;
    if (
      ![8, 10].includes(+store.asideMenuCurrentInfo?.key) &&
      ['remark', 'hasAssociation', 'hjje', 'source'].includes(field)
    )
      return false;

    let limitList = [
      'dePrice',
      'priceMarket',
      'priceMarketTax',
      'priceMarketTotal',
      'priceMarketTaxTotal',
      'taxRate',
      'taxRateStandard',
      'markSum',
      'taxRemoval',
      'jxTotal',
      'priceDifferenc',
      'priceDifferencSum',
      'donorMaterialNumber',
      'kindSc',
      'transferFactor',
      'ifProvisionalEstimate',
      'ifLockStandardPrice',
      'brand',
      'deliveryLocation',
      'qualityGrade',
      'output',
      'total',
      'marketPrice',
      'sourcePrice',
    ];
    if (
      ([8].includes(+store.asideMenuCurrentInfo?.key) &&
        [...limitList, 'ifDonorMaterial'].includes(field)) ||
      ([10].includes(+store.asideMenuCurrentInfo?.key) &&
        [...limitList, ...['type', 'lock']].includes(field))
    )
      return false;
    if (['priceMarketTotal'].includes(field) && Number(store.taxMade) === 0) {
      // 'priceMarket',如果是不含税并且是简易计税的话，不显示;一般-不含税
      return false;
    }
    if (['priceMarketTaxTotal'].includes(field) && Number(store.taxMade) === 1) {
      // 'priceMarketTax',如果是含税并且是一般计税的话，不显示;简易-含税
      return false;
    }
    if (store.currentTreeInfo.levelType !== 3) {
      // 工程项目级别
      if (['producer', 'manufactor', 'brand', 'deliveryLocation', 'qualityGrade'].includes(field)) {
        return false;
      }
      if (isDeType('22') && ['marketPrice', 'total'].includes(field)) {
        return false;
      }
      if (store.deType === '12') {
        if (
          [
            'taxRate',
            'taxRateStandard',
            'priceMarketTax',
            'priceMarket',
            'priceMarketTaxTotal',
            'priceMarketTotal',
          ].includes(field)
        ) {
          // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
          return false;
        }
      }
      return true;
    }

    if (
      isDeType('12') &&
      [
        'taxRate',
        'taxRateStandard',
        'priceMarketTax',
        'priceMarket',
        'priceMarketTaxTotal',
        'priceMarketTotal',
      ].includes(field)
    ) {
      // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
      return false;
    }
    console.log('isDeType(12)', isDeType('12'), store.deType);
    if (isDeType('22')) {
      if (['marketPrice', 'total', 'taxRemoval'].includes(field)) {
        // 市场价、市场价合价、除税系数22不显示
        return false;
      }
    }
    if ('jxTotal' === field && (isDeType('22') || Number(store.taxMade) === 0)) {
      //进项税额 是22或者简易计税不显示
      return false;
    }
    return true;
  });
  return tableColumns;
};

export const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
export const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
//获取展示的市场价-合计等字段--区分12,22含税不含税等
export const getShowFeild = () => {
  let showList = {
    scjField: '',
    scjName: '',
    hjfield: '',
    hjname: '',
  };
  const showFeild = {
    scjField: {
      12: {
        0: 'marketPrice',
        1: 'marketPrice',
      },
      22: {
        0: 'priceMarketTax',
        1: 'priceMarket',
      },
    },
    scjName: {
      12: {
        0: '市场价',
        1: '市场价',
      },
      22: {
        0: '含税市场价',
        1: '不含税市场价',
      },
    },
    hjfield: {
      12: {
        0: 'total',
        1: 'total',
      },
      22: {
        0: 'priceMarketTaxTotal',
        1: 'priceMarketTotal',
      },
    },
    hjname: {
      12: {
        0: '市场价合计',
        1: '市场价合计',
      },
      22: {
        0: '含税市场价合计',
        1: '不含税市场价合计',
      },
    },
  };
  for (let key in showList) {
    showList[key] = showFeild[key][+store.deStandardReleaseYear][+store.taxMade];
  }
  return showList;
};
export const otherCodeList = [
  'QTCLFBFB',
  '34000001-2',
  'J00004',
  'J00031',
  'J00031',
  'C11384',
  'C00007',
  'C000200',
  'C11408',
  'C11388',
  'J00006',
  'J00008',
  'CSZ00482',
  'JSZ00235',
];
export default getTableColumns;
