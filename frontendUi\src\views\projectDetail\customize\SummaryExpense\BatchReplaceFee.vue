<!--
 * @Descripttion: 批量替换费用表
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-29 14:39:21
-->
<template>
  <common-modal
    className="dialog-comm setAggreScope-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="批量替换费用表"
    width="550px"
    height="550px"
    :mask="true"
    show-zoom
    resize
    :lock-view="false"
    destroy-on-close
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >

    <div class="group-content-wrap">
      <p style="margin: 0px; height: 20px;line-height: 20px;">
        选择需要替换“费用汇总表”的单位工程
      </p>
      <div style="height:calc(100% - 90px)">
        <vxe-table
          ref="vexTable"
          :column-config="{ resizable: true }"
          :data="TableData"
          height="100%"
          :row-config="{
            isCurrent: true,
            keyField: 'id',
          }"
          :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
          :tree-config="{
            transform: true,
            rowField: 'id',
            children: 'children',
            line: true, 
            expandAll: true,
            iconOpen: 'vxe-icon-square-minus',
            iconClose: 'vxe-icon-square-plus'
          }"
          :show-overflow="true"
          :show-header="false"
        >
          <vxe-column
            title=" "
            tree-node
            align="left"
          >
            <template #default="{ row, rowIndex }">
              <a-checkbox
                v-model:checked="row.merge"
                :indeterminate="recursionFun(row)"
                :disabled="row.levelType===3&&row.deStandardReleaseYear!==store.deStandardReleaseYear||row.id===store.currentTreeInfo?.id"
                @change=selectChangeEvent(row)
              > </a-checkbox>
              <span style="margin-left:8px">{{ row.name }}</span>
            </template></vxe-column>
        </vxe-table>
      </div>

      <p class="footer-btnList">
        <a-button
          :disabled="!TableData.find(a => a.merge && a.levelType === 3)"
          v-for="i in btnList"
          @click="btnFun(i)"
          size="small"
        >{{i.label}}</a-button>
      </p>
      <p class="footer-box">
        <a-button
          type="primary"
          ghost
          @click="cancel()"
        >取消</a-button>
        <a-button
          type="primary"
          style="margin-left: 14px"
          @click="handleOk()"
        >确定</a-button>
      </p>

    </div>
  </common-modal>
  <common-modal
    className="dialog-comm"
    @close="sureModal=false"
    v-model:modelValue="sureModal"
    title="成功"
    width="200px"
    height="180px"
    :mask="true"
    :lock-view="false"
  >
    <p style="display: flex;
    align-items: center;">
      <icon-font
        type="icon-ruotixing"
        style="font-size: 54px"
      >

      </icon-font>
      <span style="margin-left: 10px;">
        已成功替换
      </span>
    </p>
    <p>
      <a-button
        type="primary"
        style="margin-left: 14px;float: right;"
        @click="sureModal=false,dialogVisible=false"
      >确定</a-button>
    </p>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
const route = useRoute();
const emits = defineEmits(['refresh']);
const props = defineProps(['']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
let loading = ref(false);
const btnList = reactive([
  {
    label: '选择同名工程',
    id: 'selectSameName',
  },
  {
    label: '取消同名工程',
    id: 'cancelSameName',
  },
  {
    label: '选择同专业工程',
    id: 'selectSameMajor',
  },
  {
    label: '取消同专业工程',
    id: 'cancelSameMajor',
  },
]);
const btnFun = item => {
  let selectList = TableData.value.filter(a => a.merge && a.levelType === 3);
  let target = vexTable.value.getCurrentRecord(); //当前选中目标
  //选中按照勾选的selectList   取消按照鼠标target
  if (selectList?.length === 0) {
    return;
  }
  let deType = store.deStandardReleaseYear;
  switch (item.id) {
    case 'selectSameName':
      1;
      //选中同名
      let nameList = selectList.map(a => {
        return a.name;
      });
      TableData.value.map(a => {
        if (
          nameList.includes(a.name) &&
          !a.merge &&
          deType == a.deStandardReleaseYear
        ) {
          a.merge = true;
          handleClearMergeMer(a);
        }
      });
      break;
    case 'cancelSameName':
      if (
        !TableData.value.find(
          a => target?.name === a?.name && target.id !== a.id && a.merge
        )
      )
        return;
      TableData.value.map(a => {
        if (
          target.name === a.name &&
          a.merge &&
          a.id !== store.currentTreeInfo?.id
        ) {
          a.merge = false;
          handleClearMergeMer(a);
        }
      });
      //取消同名
      break;
    case 'selectSameMajor':
      //选择同专业
      let majorList = selectList.map(a => {
        return a.constructMajorType;
      });
      TableData.value.map(a => {
        if (
          majorList.includes(a.constructMajorType) &&
          !a.merge &&
          deType == a.deStandardReleaseYear
        ) {
          a.merge = true;
          handleClearMergeMer(a);
        }
      });
      break;
    case 'cancelSameMajor':
      //取消同专业
      if (
        !TableData.value.find(
          a =>
            target?.constructMajorType === a?.constructMajorType &&
            target?.id !== a?.id &&
            a.merge
        )
      )
        return;
      TableData.value.map(a => {
        if (
          target?.constructMajorType === a?.constructMajorType &&
          a.merge &&
          a.id !== store.currentTreeInfo?.id
        ) {
          a.merge = false;
          handleClearMergeMer(a);
        }
      });
      break;
  }
};
const cancel = (refresh = false) => {
  dialogVisible.value = false;
};

const open = k => {
  dialogVisible.value = true;
  getTableList();
};
let initTreeData = reactive([]);
const getTableList = async () => {
  let result;
  await constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      initTreeData = JSON.parse(JSON.stringify(res.result));
      TableData.value = JSON.parse(JSON.stringify(res.result));
    }
  );
  let currentRow;
  console.log('设置汇总范围-treeData.value', result);
  TableData.value.map(a => {
    if (a.id === store.currentTreeInfo?.id) {
      //第一次打开每一项设置为选中
      currentRow = a;
      a.merge = true;
      handleClearMergeMer(a);
    }
  });
  nextTick(() => {
    vexTable.value.setCurrentRow(currentRow);
    vexTable.value.setAllTreeExpand(true);
  });
};
const selectChangeEvent = row => {
  console.log('selectChangeEvent', row);
  if (row.id === store.currentTreeInfo?.id && !row.merge) row.merge = true;
  handleClearMergeMer(row);
};
const recursionFun = row => {
  //设置半选状态--子级非全选且下级有选中状态
  let list = row?.children?.length > 0 ? xeUtils.toTreeArray([row]) : [];
  let flag = list.some(a => a.id !== row.id && a.merge);
  // console.log('recursionFun', row, list, flag);
  return row.levelType === 3 ? false : !list.every(a => a.merge) && flag;
};
const setChildMergeValue = target => {
  //设置子级选中状态
  if (target?.children?.length > 0 && target.levelType < 3) {
    target.children.map(a => {
      if (store.deStandardReleaseYear == a.deStandardReleaseYear) {
        a.merge = a.id == store.currentTreeInfo?.id ? true : target.merge;
        setChildMergeValue(a);
      }
    });
  }
};
const setParentMergeValue = row => {
  //设置父级选中状态
  if (row.levelType === 1) return;
  let parentId = initTreeData.find(a => a.id === row.id)?.parentId;
  let parent = TableData.value.find(a => a.id === parentId);
  if (parent?.children?.every(a => a.merge)) {
    parent.merge = true;
  } else if (parent) {
    parent.merge = false;
  }
  parent ? setParentMergeValue(parent) : '';
};
const handleClearMergeMer = row => {
  console.log(TableData.value, row);
  setChildMergeValue(row);
  setParentMergeValue(row);
  // if (row.mergeTarget === null && !row.cyhb) {
  //   row.mergeTarget = false;
  // }
};

// 确认
let sureModal = ref(false);
const handleOk = () => {
  let selectList = TableData.value.filter(a => a.merge && a.levelType === 3);
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    idArray: [],
  };
  selectList.map(a => {
    apiData.idArray.push({
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: a.parentId,
      unitId: a.id,
    });
  });
  console.log(apiData, selectList);
  csProject.batchReplacement(apiData).then(res => {
    sureModal.value = true;
  });
};
defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .footer-btnList {
    display: flex;
    margin-top: 10px;
    /* height: 30px; */
    /* padding: 3px 0; */
    margin: 8px 0;
    /* line-height: 30px; */
    justify-content: space-between;
  }
  .footer-box {
    width: 100%;
    margin: 0;
    height: 30px;
    .ant-btn {
      float: right;
    }
    .ant-btn:nth-of-type(1) {
      margin-left: 10px;
    }
  }
  .vxe-table .index-bg {
    background-color: #ffffff;
  }
  .vxe-table--body {
    border-collapse: collapse;
    // border: 2px solid #ffffff;
  }
}
</style>
