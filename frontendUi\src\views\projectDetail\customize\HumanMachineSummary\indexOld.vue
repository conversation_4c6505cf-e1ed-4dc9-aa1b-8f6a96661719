<!--
 * @Descripttion: 
 * @Author: 
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: wangru
 * @LastEditTime: 2025-03-27 11:17:11
-->
<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <teleport
    to=".tab-menus"
    v-if="
      projectStore.tabSelectName === '人材机汇总' &&
      ![7, 8, 9, 10].includes(+projectStore.asideMenuCurrentInfo?.key)
    "
  >
    <HeadSummary
      :selectData="selectData"
      :tableData="tableData"
    ></HeadSummary>
  </teleport>
  <div
    class="table-content table-content-flex-column"
    id="humanTable"
  >
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <vxe-table
          align="center"
          ref="humanTable"
          height="auto"
          :menu-config="menuConfig"
          :column-config="{ resizable: true }"
          :row-config="{
            isHover: true,
            isCurrent: true,
          }"
          :data="tableData"
          :cell-style="
            projectStore.currentTreeInfo.levelType === 3
              ? cellStyle
              : cellTableStyle
          "
          :row-style="rowStyle"
          @edit-closed="editClosedEvent"
          keep-source
          @menu-click="contextMenuClickEvent"
          @cell-click="
            cellData => {
              resetDragFillInfo();
              currentCellDragFill = cellData;

              useCellClickEvent(cellData, null, ['']);

              if(displayedFields.includes(cellData.column.field) && (!cellData.column?.editRender || cellData.column.editRender?.notCanEdit)){
                 currentCellData.columnIndex = cellData.$columnIndex;
                 currentCellData.rowId = cellData.row.sequenceNbr;
              }
            }
          "
          class="table-edit-common"
          :cell-class-name="cellClassName"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: activeRowMethod,
          }"
          :scroll-x="{ enabled: true }"
          :scroll-y="{ enabled: true, gt: 30 }"
          @current-change="currentChange"
          :export-config="{}"
          show-overflow
          :header-cell-class-name="setHeaderCellClassName"
          @cell-mouseenter="cellMouseenterEvent"
        >
          <vxe-column
            type="checkbox"
            width="40"
            fixed="left"
          >
            <template #header="{ checked, indeterminate }">
              <span
                class="custom-checkbox"
                @click.stop="toggleAllCheckboxEvent"
              >
                <i
                  v-if="indeterminate"
                  style="color: #1888d7;"
                  class="vxe-icon-square-minus-fill"
                ></i>
                <i
                  v-else-if="checked"
                  style="color: #1888d7;"
                  class="vxe-icon-square-checked-fill"
                ></i>
                <i
                  v-else
                  style="color: #1888d7;"
                  class="vxe-icon-checkbox-unchecked"
                ></i>
              </span>
            </template>
          </vxe-column>
          <vxe-column
            v-for="columns of showColumns"
            v-bind="columns"
          >
            <template #header="{ column }">
              <span
                class="custom-header"
                v-if="columns.slot && sortList.includes(column.field)"
              >
                <span
                  style="cursor: pointer"
                  @click="sortClick(column.field)"
                >{{ column.title }}</span>
                <img
                  class="sortImg"
                  src="@/assets/img/upSort.png"
                  v-if="sortFiled == column.field && sortVal"
                  alt=""
                />
                <img
                  class="sortImg"
                  src="@/assets/img/downSort.png"
                  v-if="sortFiled == column.field && !sortVal"
                  alt=""
                />
                <CloseOutlined
                  class="icon-close"
                  @click="closeColumn({ column })"
                />
              </span>
              <span
                class="custom-header"
                v-else
              >
                <span>{{ column.title }}</span>
                <CloseOutlined
                  class="icon-close"
                  @click="closeColumn({ column })"
                />
              </span>
            </template>

            <template
              v-if="columns.slot"
              #default="{ column, row, $columnIndex, $rowIndex }"
            >
              <template v-if="['materialCode'].includes(column.field)">
                {{ '‎' + row[column.field] ? row[column.field] : '' }}
              </template>
              <template v-else-if="column.field === 'hasAssociation'">
                <span :style="{ color: row.hasAssociation ? 'green' : 'red' }">
                  {{ row.hasAssociation ? '✔️' : '❌' }}
                </span>
              </template>
              <template v-else-if="column.field === 'relevancyMaterialCodeList'">
                {{
                  '‎' + row.relevancyMaterialCodeList
                    ? row.relevancyMaterialCodeList
                    : ''
                }}
              </template>
              <template v-else-if="
                  [
                    'priceMarketTotal',
                    'marketPrice',
                    'total',
                    'priceMarketTotal',
                    'priceMarketTaxTotal',
                  ].includes(column.field)
                ">
                {{ isChangeAva(row) ? '-' : row[column.field] }}
              </template>

              <template v-else-if="['materialName'].includes(column.field)">
                <!-- <a-popover
                  v-if="popoverDisplay({ column, row, $columnIndex, $rowIndex })"
                  v-model:visible="row.annotationsVisible"
                  trigger="click"
                  placement="rightTop"
                  :align="{offset: [10, -10]}"
                  overlayClassName="annotations-pop"
                  :overlayStyle="{'cursor': 'pointer','width':'250px','height':'140px','z-index': '100',}"
                  @visibleChange="val => visibleChange(val, row)"
                  :getPopupContainer="triggerNode => deNameRef(triggerNode)"
                >
                  <template #content>
                    <Annotations
                      @click="annotationClick"
                      @close="v => closeAnnotations(v, row)"
                      @onfocusNode="onFocusNode(row,$event)"
                      :note="row.annotations"
                      style="left: 10px"
                      :isDisabled="row?.noteEditVisible"
                      :ref="el => getAnnotationsRef(el, row)"
                    >
                    </Annotations>
                  </template>
                  <div
                    @mouseover="cellMouseEnterEvent({ row, column })"
                    @mouseout="cellMouseLeaveEvent({ row, column })"
                  >
                    {{ row.materialName }}
                  </div>
                </a-popover> -->
                <div
                  @mouseover="cellMouseEnterEvent({ row, column })"
                  @mouseout="cellMouseLeaveEvent({ row, column })"
                >
                  {{ row.materialName }}
                </div>
              </template>

              <template v-else-if="
                  ['priceMarketTax', 'priceMarket'].includes(column.field)
                ">{{ getValueByDeType('12', row, column.field) }}</template>
              <template v-else-if="['markSum'].includes(column.field)">
                <vxe-checkbox
                  v-model="row.markSum"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  :disabled="projectStore.currentTreeInfo.levelType < 3"
                  @change="CheckboxChange(row, 'markSum')"
                  v-if="[1, 2].includes(Number(row.levelMark))"
                ></vxe-checkbox>
                <!-- 王浩让工程项目级别禁止勾选是否汇总复选框 -->
              </template>
              <template v-else-if="column.field === 'ifDonorMaterial'">
                {{ getDonorMaterialText(row.ifDonorMaterial) }}
              </template>
              <template v-else-if="column.field === 'donorMaterialNumber'">
                <span v-if="row.checkIsShow">{{
                  row.donorMaterialNumber
                }}</span>
              </template>

              <template v-else-if="column.field === 'ifProvisionalEstimate'">
                <vxe-checkbox
                  v-model="row.ifProvisionalEstimate"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  :disabled="isChangeAva(row)"
                  @change="CheckboxChange(row, 'ifProvisionalEstimate')"
                  v-if="
                    row.checkIsShow &&
                    !['人工费', '机械费'].includes(row.type) &&
                    !(otherCodeList.includes(row.materialCode) && row.isBfh)
                  "
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'ifLockStandardPrice'">
                <vxe-checkbox
                  v-model="row.ifLockStandardPrice"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifLockStandardPrice')"
                  v-if="row.checkIsShow"
                  :disabled="Number(row.edit) === 1"
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'output'">
                <vxe-checkbox
                  v-model="row.output"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'output')"
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'taxRate'">
                {{ getValueByDeType('12', row, column.field) }}
              </template>
              <template v-else-if="['taxRemoval', 'jxTotal'].includes(column.field)">
                {{
                  isDeType('22', row.deStandardReleaseYear)
                    ? '/'
                    : row[column.field]
                }}
              </template>
              <template v-else-if="column.field === 'lock'">
                <vxe-checkbox
                  v-model="row.lock"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'lock')"
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'unit'">
                <span v-if="
                    Number(row.edit) !== 1 &&
                    !otherCodeList.includes(row.materialCode)
                  ">
                  {{ row.unit }}
                </span>
                <span v-else>
                  {{
                    otherCodeList.includes(row.materialCode) && row.unit === '%'
                      ? '元'
                      : row.unit
                  }}
                </span>
              </template>
              <template v-else>{{ row[column.field] }}</template>
              <div
                v-if="handLabelDisplay({ $columnIndex, row, column })"
                class="fill-handle"
                @mousedown=" handleMouseDown($event, row, column, $columnIndex, $rowIndex, useDragCb)"
              ></div>

              <a-popover
                v-if="popoverDisplay({ column, row, $columnIndex, $rowIndex })"
                v-model:visible="row.annotationsVisible"
                trigger="click"
                placement="rightTop"
                :align="{offset: [75, -25]}"
                overlayClassName="annotations-pop"
                @visibleChange="val => visibleChange(val, row)"
                :overlayStyle="{'cursor': 'pointer','width':'250px','height':'140px','z-index': '100','position':'absolute', 'position-anchor': '--trigger-element','left':'anchor(center)'
              }"
                :getPopupContainer="triggerNode => deNameRef(triggerNode)"
              >
                <template #content>
                  <Annotations
                    @click="annotationClick"
                    @close="v => closeAnnotations(v, row)"
                    @onfocusNode="onFocusNode(row,$event)"
                    :note="row.annotations"
                    style="left: 10px"
                    :isDisabled="row?.noteEditVisible"
                    :ref="el => getAnnotationsRef(el, row)"
                  >
                  </Annotations>
                </template>
              </a-popover>
            </template>

            <template
              v-if="columns.slot"
              #edit="{ column, row, $columnIndex }"
            >
              <template v-if="column.field === 'type'">
                <vxe-select
                  v-model="row.type"
                  :clearable="false"
                  transfer
                  v-if="
                    selectOptions.find(a => a.type === row.type) &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                >
                  <vxe-option
                    v-for="item in selectOptions"
                    :key="item.type"
                    :value="item.type"
                    :label="item.type"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{ row.type }} </span>
              </template>

              <template v-else-if="column.field === 'dispNo'">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.dispNo"
                  type="text"
                  @blur="clear()"
                  @keyup="row.dispNo = row.dispNo.replace(/[^\d*]/g, '')"
                ></vxe-input>
              </template>
              <template v-else-if="column.field === 'materialName'">
                <vxe-input
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                  :clearable="false"
                  v-model.trim="row.materialName"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
                <span v-else>{{ row.materialName }}</span>
              </template>
              <template v-else-if="column.field === 'specification'">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.specification"
                  type="text"
                  @blur="clear()"
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                ></vxe-input>
                <span v-else>{{ row.specification }}</span>
              </template>
              <template v-else-if="column.field === 'unit'">
                <vxeTableEditSelect
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                  :filedValue="row.unit"
                  :isNotLimit="true"
                  :list="projectStore.unitListString"
                  @update:filedValue="
                    newValue => {
                      saveCustomInput(newValue, row, 'unit', $rowIndex);
                    }
                  "
                ></vxeTableEditSelect>
                <span v-else>
                  {{ isOtherMaterial && row.unit === '%' ? '元' : row.unit }}
                </span>
              </template>
              <template v-else-if="column.field === 'marketPrice'">
                <vxe-input
                  v-if="isEditPrice(row,'marketPrice')"
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                    row.marketPrice = pureNumber(row.marketPrice, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.marketPrice
                }}</span>
              </template>
              <template v-else-if="column.field === 'priceMarket'">
                <template v-if="row.deStandardReleaseYear === '12'">
                  <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                  <vxe-input
                    v-else-if="isEditPrice(row,'priceMarket')
                    "
                    :clearable="false"
                    v-model.trim="row.marketPrice"
                    type="text"
                    @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                  ></vxe-input>
                  <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
                </template>
                <vxe-input
                  v-else-if="isEditPrice(row,'priceMarket')
                  "
                  :clearable="false"
                  v-model.trim="row.priceMarket"
                  type="text"
                  @blur="
                    row.priceMarket = pureNumber(row.priceMarket, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarket
                }}</span>
              </template>
              <template v-else-if="column.field === 'priceMarketTax'">
                <template v-if="row.deStandardReleaseYear === '12'">
                  <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                  <vxe-input
                    v-else-if="isEditPrice(row,'priceMarketTax')
                    "
                    :clearable="false"
                    v-model.trim="row.marketPrice"
                    type="text"
                    @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                  ></vxe-input>
                  <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
                </template>
                <vxe-input
                  v-else-if="isEditPrice(row,'priceMarketTax')
                  "
                  :clearable="false"
                  v-model.trim="row.priceMarketTax"
                  type="text"
                  @blur="
                    row.priceMarketTax = pureNumber(row.priceMarketTax, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarketTax
                }}</span>
              </template>

              <!-- totalNumber -->
              <template v-else-if="column.field === 'totalNumber'">
                <vxe-input
                  v-if="!row.hasAssociation"
                  :clearable="false"
                  v-model.trim="row.totalNumber"
                  type="text"
                  @blur="
                    row.totalNumber = pureNumber(row.totalNumber, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{ row.totalNumber }}</span>
              </template>
              <!-- 风险系数 -->
              <template v-else-if="column.field === 'riskCoefficient'">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.riskCoefficient"
                  type="text"
                  @blur="
                    row.riskCoefficient = pureNumber(row.riskCoefficient, 2);
                    clear();
                  "
                ></vxe-input>
              </template>
              <!-- 基准单价+投标单价+暂定金额 -->
              <template v-else-if="
                  ['benchmarkUnitPrice', 'tbdj', 'zdje'].includes(column.field)
                ">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row[column.field]"
                  type="text"
                  @blur="
                    row[column.field] = pureNumber(row[column.field]);
                    clear();
                  "
                ></vxe-input>
              </template>
              <template v-else-if="column.field === 'taxRate'">
                <vxe-input
                  v-if="
                   isEditPrice(row,'taxRate')
                  "
                  :clearable="false"
                  v-model.trim="row.taxRate"
                  type="text"
                  @blur="
                    row.taxRate = pureNumber(row.taxRate, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  getValueByDeType('12', row, column.field)
                }}</span>
              </template>
              <template v-else-if="column.field === 'taxRemoval'">
                <span v-if="isDeType('22', row.deStandardReleaseYear)">/</span>
                <vxe-input
                  v-else-if="row.type !== '人工费'"
                  :clearable="false"
                  v-model.trim="row.taxRemoval"
                  type="text"
                  :maxlength="10"
                  @blur="
                    (row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()
                  "
                ></vxe-input>
                <span v-else>{{ row.taxRemoval }}</span>
              </template>
              <template v-else-if="column.field === 'ifDonorMaterial'">
                <vxe-select
                  v-if="row.checkIsShow"
                  v-model="row.ifDonorMaterial"
                  @change="CheckboxChange(row, 'ifDonorMaterial')"
                  transfer
                >
                  <vxe-option
                    v-for="item in donorMaterialList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{
                  getDonorMaterialText(row.ifDonorMaterial)
                }}</span>
              </template>
              <template v-else-if="column.field === 'donorMaterialNumber'">
                <vxe-input
                  v-if="row.checkIsShow"
                  :clearable="false"
                  v-model.trim="row.donorMaterialNumber"
                  type="text"
                  @blur="
                    (row.donorMaterialNumber =
                      row.donorMaterialNumber * 1 + ''),
                      clear()
                  "
                  @keyup="
                    row.donorMaterialNumber = row.donorMaterialNumber.replace(
                      /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                      '$1$2.$3'
                    )
                  "
                ></vxe-input>
                <span v-else></span>
              </template>
              <template v-else-if="column.field === 'kindSc'">
                <vxe-select
                  v-model="row.kindSc"
                  :clearable="true"
                  v-if="row.showScxs !== '0'"
                >
                  <!-- transfer -->
                  <vxe-option
                    v-for="item in classOptions"
                    :key="item"
                    :value="item"
                    :label="item"
                  ></vxe-option>
                </vxe-select>
              </template>
              <template v-else-if="column.field === 'transferFactor'">
                <vxe-input
                  v-if="row.showScxs !== '0' && row.kindSc"
                  :clearable="false"
                  v-model.trim="row.transferFactor"
                  type="text"
                  @blur="
                    row.transferFactor = pureNumber(row.transferFactor, 4);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{ row.transferFactor }}</span>
              </template>
              <template v-else-if="
                  ['remark', 'producer', 'manufactor'].includes(column.field)
                ">
                <vxe-input
                  v-if="
                    !(
                      +projectStore.asideMenuCurrentInfo?.key === 10 &&
                      row.hasAssociation
                    )
                  "
                  :clearable="false"
                  v-model.trim="row[column.field]"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
                <span v-else>{{ row[column.field] }} </span>
              </template>
              <template v-else>
                <vxe-input
                  :clearable="false"
                  v-model.trim="row[column.field]"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
              </template>
            </template>
          </vxe-column>
          <template #empty>
            <span style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              ">
              <img :src="getUrl('newCsProject/none.png')" />
            </span>
          </template>
        </vxe-table>
      </template>
      <template #two>
        <div
          v-if="projectStore.currentTreeInfo.levelType < 3"
          style="height: 100%"
        >
          <p class="selectTab">
            <a-radio-group
              v-model:value="selectdTab"
              :style="{ marginBottom: '8px' }"
            >
              <a-radio-button
                :value="item.key"
                v-for="item of activeOptions"
              >{{
                item.tab
              }}</a-radio-button>
            </a-radio-group>
          </p>

          <keep-alive>
            <component
              :is="components.get(selectdTab)"
              @getUpList="getHumanMachineData"
              @upDateMarketPrice="upDateMarketPrice"
              :showInfo="currentInfo"
              :unitIdList="setAggreUnitList"
            ></component>
          </keep-alive>
        </div>
        <associated-information
          v-if="
            projectStore.currentTreeInfo.levelType === 3 &&
            [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)
          "
          :showInfo="currentInfo"
          @refresh="getHumanMachineData"
        >
        </associated-information>
        <machine-service
          v-else
          @getUpList="getHumanMachineData"
          @upDateMarketPrice="upDateMarketPrice"
          :showInfo="currentInfo"
          :tableData="tableData"
        ></machine-service>
      </template>
    </split>
  </div>
  <common-modal
    className="dialog-comm"
    :title="typeModal"
    :width="typeModal === '载价编辑' ? 1130 : 1020"
    :height="
      typeModal === '载价编辑' ? 560 : typeModal === '载价报告' ? 500 : 530
    "
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="closeReportModel"
    :mask="true"
    :lockView="true"
  >
    <!--     :mask="typeModal === '载价编辑' ? false : true"
    :lockView="typeModal === '载价编辑' ? false : true" -->
    <batch-load-price
      ref="batchLoad"
      v-if="typeModal === '批量载价'"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal === '载价编辑'"
      :propsData="propsData"
      @close="close"
    ></edit-load-price>
    <report-load-price v-if="typeModal === '载价报告'"></report-load-price>
    <!-- 载价报告 -->
  </common-modal>

  <!-- 关联定额弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.levelType"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>
  <material-machine-index
    pageFr="rcjSummary"
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="indexLoading"
    @addChildrenRcjData="() => {}"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
  />
  <!-- 调整市场价系数 -->
  <common-modal
    className="dialog-comm noMask"
    title="调整市场价系数"
    width="300"
    height="200"
    v-model:modelValue="adjustFactor"
    :mask="false"
  >
    <div class="adjustFactorMoadl">
      <!-- <p class="title">
        该功能针对所有选中行进行调整
      </p> -->
      <div>
        <span> 市场价系数： </span>
        <a-input
          v-model:value="marcketFactor"
          placeholder="请输入市场价系数"
          @blur="marcketFactor = selfCheck(marcketFactor, 2, 0, 1000)"
          @keyup="marcketFactor = marcketFactor.replace(/[^\d.]/g, '')"
        />
      </div>
      <p class="footor">
        <a-button @click="adjustFactor = false">取消</a-button>
        <a-button
          type="primary"
          @click="sureOrCancel()"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
  <!-- 人材机无价差 -->
  <common-modal
    className="dialog-comm noMask"
    title="人材机无价差"
    width="300"
    height="200"
    v-model:modelValue="spreadModal.visible"
    :mask="false"
  >
    <div class="apreadModal">
      <div class="single">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-anzhuangfeiyongshuchufangshi"
          ></icon-font>选择范围
        </div>
        <a-radio-group
          v-model:value="spreadModal.value"
          style="margin:13px 0 0"
        >
          <a-radio :value="1">选中的范围</a-radio>
          <a-radio
            :value="2"
            style="margin-left: 20px"
          >所有工料机</a-radio>
        </a-radio-group>
      </div>
      <p class="footor">
        <span>
          <icon-font
            type="icon-querenshanchu"
            class="iconFont"
          ></icon-font>
          不修改市场价锁定的人材机数据
        </span>
        <span>
          <a-button @click="spreadBtnFun(0)">取消</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            @click="spreadBtnFun(1)"
          >确定</a-button>
        </span>
      </p>
    </div>
  </common-modal>
  <merge-materials
    ref="mergeMaterialsRef"
    :list="tableData"
    @refresh="getHumanMachineData"
    @selfTestLocateTable="selfTestLocateTable"
    @uploadList="mergeUploadList"
  ></merge-materials>
  <import-excel
    ref="importExcelRef"
    @updateImportData="updateImportData"
    @closeImportExcel="getHumanMachineData"
  ></import-excel>
  <!-- 设置汇总范围 -->
  <set-aggre-scope
    ref="setAggreScopeRef"
    @refresh="setAggreScopeOk"
    :unitIdList="setAggreUnitList"
    :isFirstOpen="isFirstOpen"
  ></set-aggre-scope>
  <!-- 从人材机汇总中选择 -->
  <select-from-machineSummary
    ref="selectFromMachineRef"
    @refresh="getHumanMachineData"
    :showInfo="currentInfo"
  ></select-from-machineSummary>
  <common-modal
    className="dialog-comm area-modal"
    @close="delNoteMol.visible=false"
    width="500"
    v-model:modelValue="delNoteMol.visible"
    title="是否删除所有批注？"
  >
    <div class="tree-content-wrap">
      <div class="group-list">
        <p>需要确定删除范围：</p>
        <a-radio-group v-model:value="delNoteMol.dataStatus">
          <a-radio
            value="3"
            v-if="projectStore.currentTreeInfo.levelType===3"
          >删除当前单位工程所有批注</a-radio>
          <a-radio
            value="2"
            v-if="projectStore.currentTreeInfo.levelType==2"
          >删除当前单项工程所有批注</a-radio>
          <a-radio value="1">删除工程项目所有批注</a-radio>

        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="delNoteMol.visible=false">取消</a-button>
        <a-button
          type="primary"
          @click="delAllNote(+delNoteMol.dataStatus)"
        >确定</a-button>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
  provide,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  watchEffect,
  onActivated,
  onDeactivated,
  markRaw,
} from 'vue';
import Annotations from '@/components/Annotations/index.vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import loadApi from '@/api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import MachineService from './MachineService.vue';
import AssociatedInformation from './AssociatedInformation.vue';
import SourceAnalysis from './SourceAnalysis.vue';
import MergeMaterials from './mergeMaterials.vue';
import { disposeDeTypeData, setGlobalLoading } from '@/hooks/publicApiData';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import { updateOperateByName } from '../operate';
import { getUrl, pureNumber } from '@/utils/index';
import HumanHeader from './HumanHeader.vue';
import { insetBus } from '@/hooks/insetBus';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList from '../operate';
import BatchLoadPrice from './BatchLoadPrice.vue';
import EditLoadPrice from './EditLoadPrice.vue';
import ReportLoadPrice from './ReportLoadPrice.vue';
import SetAggreScope from './SetAggreScope.vue';
import mergeMaterials from './mergeMaterials.vue';
import selectFromMachineSummary from './SelectFromMachineSummary.vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import ImportExcel from './ImportExcel.vue';
import HeadSummary from './HeadSummary.vue';
import ObjectUtils from '@/components/qdQuickPricing/utils/ObjectUtils';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import { useDragFillCell } from '@/hooks/useDragFillCell';
import { onClickOutside } from '@vueuse/core';
import getTableColumns, {
  isDeType,
  donorMaterialList,
  getDonorMaterialText,
  getShowFeild,
  otherCodeList,
} from './tableColumns';
const emits = defineEmits(['getMoveInfo']); //人材机汇总数据行上下移动
const projectStore = projectDetailStore();
// 设置主要材料
const SetMainMaterials = defineAsyncComponent(() =>
  import('./SetMainMaterials.vue')
);
const components = markRaw(new Map());
components.set(
  'lyfx',
  defineAsyncComponent(() => import('./SourceAnalysis.vue'))
);
components.set(
  'xxfw',
  defineAsyncComponent(() => import('./MachineService.vue'))
);
// 一、二、三类工
const renGongCodeList = [
  '10000001',
  '10000002',
  '10000003',
  'JXPB-005',
  'R00001',
];
//下表格-两部分
let selectdTab = ref('lyfx');
const activeOptions = reactive([
  {
    key: 'lyfx',
    tab: '来源分析',
  },
  {
    key: 'xxfw',
    tab: '信息价服务',
  },
]);
const isOtherMaterial = computed(() => {
  const { materialCode } = currentInfo.value || {};
  return (
    otherCodeList.includes(materialCode) &&
    ![8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)
  );
});

/**
 * 获取是否勾选政策文件
 */
const isSelectFeePolicyDoc = ref(false);
const getFeePolicyDocData = () => {
  let apiData = {
    type: projectStore.currentTreeInfo.levelType,
  };
  apiData = getParamsData(apiData);
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      const { rgfId, awfId, gfId } = res.result;
      isSelectFeePolicyDoc.value = rgfId;
    }
  });
};
const classOptions = ['钢材', '木材', '水泥', '钢筋', '商砼', '商品砂浆'];

let currentInfo = ref(null);
let reportModel = ref(false);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级
let batchLoad = ref();

const closeReportModel = () => {
  if (typeModal.value == '批量载价') {
    batchLoad?.value.hideSelect();
  }
  setTimeout(() => {
    reportModel.value = false;
  }, 30);
};

import { useCellClick } from '@/hooks/useCellClick';
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  currentCellData,
} = useCellClick();

const quotaPopup = defineAsyncComponent(() =>
  import('@/components/SummaryPopup/index.vue')
);

let unifyData = operateList.value.find(
  item => item.name === 'unify-humanMachineSummary'
);
let isLoad = operateList.value.find(item => item.name === 'batch-loadprice');
let machuneSpreadsBtn = operateList.value.find(
  item => item.name === 'machine-no-spreads'
); //人材机无价差按钮--未选中数据置灰
// let isSeeReport = operateList.value.find(
//   item => item.name === 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

// 人材机索引
let indexVisible = ref(false);
// 人材机索引数据loading
let indexLoading = ref(false);
/**
 * 菜单右键替换数据处理
 */
const menuReplaceHandler = () => {
  indexVisible.value = true;
};
/**
 * 关闭替换人材机索引
 */
const closeReplaceRCJ = () => {
  indexVisible.value = false;
};

/**
 * 数据替换
 * replaceRcj 被替换的人材机
 * targetRcj 目标人材机
 */
const currentInfoReplace = targetInfo => {
  const params = getParamsData({
    replaceRcj: JSON.parse(JSON.stringify(currentInfo.value)),
    targetRcj: JSON.parse(JSON.stringify(targetInfo)),
  });
  api.replaceRcjToUnit(params).then(res => {
    console.log('人材机数据替换', params, res);
    if (res.status === 200) {
      message.success('替换成功!');
      getHumanMachineData();
      closeReplaceRCJ();
    }
  });
};

let humanTable = ref();
let tableData = ref([]);
let upDateRow = ref();
let typeList = [{ type: '主材费' }, { type: '材料费' }, { type: '设备费' }];
let selectOptions = reactive([...typeList]);
const setTypeList = () => {
  selectOptions = ![8].includes(+projectStore.asideMenuCurrentInfo?.key)
    ? [...typeList]
    : [...typeList, { type: '无' }];
};
const mergeUploadList = info => {
  for (let type in info) {
    for (let data of info[type]) {
      data.isChange = true;
      if (type === 'replace') {
        tableData.value.splice(data.originalDataIndex, 1, data);
      }
      if (type === 'remove') {
        tableData.value.splice(data.originalDataIndex, 1);
      }
    }
  }
  let upDateList = getPropData();
  console.log(upDateList);
  if (upDateList && upDateList.length > 0) {
    setProUpdate(upDateList);
  }
};
// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    projectStore.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
const quotaPopupVisible = ref(false); // 关联定额弹窗
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: false,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: false,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: false,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'search',
          name: '查询关联定额',
          visible: true,
          disabled: false,
        },
        {
          code: 'replace',
          name: '替换数据',
          visible: true,
          disabled: false,
        },
        {
          code: 'remove',
          name: '清除载价',
          visible: true,
          disabled: false,
        },
        {
          code: 'export',
          name: '导出excel',
          visible: true,
          disabled: false,
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
        {
          code: 'cancelSort',
          name: '取消排序',
          visible: true,
          disabled: false,
        },
        {
          code: 'insert',
          name: '插入',
          visible: true,
          disabled: false,
        },
        {
          code: 'delRow',
          name: '删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'select',
          name: '从人材机汇总中选择',
          visible: true,
          disabled: false,
        },
        {
          code: 'tractorCode',
          name: '重置承包人材料号',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, row);
    if (!row) return;
    options[0].find(item => item.code === 'remove').disabled =
      !row.highlight || row.sourcePrice == '自行载价';
    //22人材机也可以清除载价-bug20487
    // if (row.libraryCode?.startsWith('2022')) {
    //   options[0].find(item => item.code === 'remove').visible = false;
    //   // console.log('options[0][1]', options[0][1]);
    // }
    let replaceInfo = options[0].find(item => item.code === 'replace');
    replaceInfo.visible =
      projectStore.currentTreeInfo.levelType === 3 &&
      ![8, 10].includes(+projectStore.asideMenuCurrentInfo?.key);
    replaceInfo.disabled = !!row.rcjmx || row.isFyrcj === 0;

    let insert = options[0].find(item => item.code === 'insert');
    let delRow = options[0].find(item => item.code === 'delRow');
    let select = options[0].find(item => item.code === 'select');

    insert.visible = [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key);
    delRow.visible = [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key);
    select.visible = [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key);
    let limitList = ['search', 'remove', 'export']; //暂估表和承包人不需要的功能
    let showList = ['insert', 'delRow', 'select']; //暂估表和承包人单独需要的功能
    limitList.map(a => {
      let target = options[0].find(item => item.code === a);
      target.visible = ![8, 10].includes(
        +projectStore.asideMenuCurrentInfo?.key
      );
    });
    showList.map(a => {
      let target = options[0].find(item => item.code === a);
      target.visible = [8, 10].includes(
        +projectStore.asideMenuCurrentInfo?.key
      );
    });
    let tractorCode = options[0].find(item => item.code === 'tractorCode');
    tractorCode.visible = [10].includes(
      +projectStore.asideMenuCurrentInfo?.key
    );
    //无排序字段取消排序禁止点击
    let sortInfo = options[0].find(item => item.code === 'cancelSort');
    // //本期迭代取消排序都需要
    sortInfo.disabled = !sortFiled.value;
    let noteList = options[0].find(item => item.code === 'noteList');
    noteList.children.map(a => {
      if (['add-note'].includes(a.code)) {
        a.visible = !row.annotations;
      }
      if (
        ['edit-note', 'del-note', 'show-note', 'hide-note'].includes(a.code)
      ) {
        a.visible = row.annotations?.length > 0;
      }
      if (a.code === 'show-note') a.disabled = row?.isShowAnnotations;
      if (a.code === 'hide-note') a.disabled = !row?.isShowAnnotations;
    });

    return true;
  },
});

// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (!row) return;
  changeCurrentInfo(row);
  switch (menu.code) {
    case 'search':
      quotaHeaderData.value = row;
      quotaPopupVisible.value = true;
      break;
    case 'replace':
      menuReplaceHandler();
      break;
    case 'export':
      exportExcel('all');
      break;
    case 'pageColumnSetting':
      showPageColumnSetting();
      break;
    case 'remove':
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '该条材料市场价已被锁定，'
            : '是否确定清除选中数据的载价数据？',
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '请取消勾选后再进行清除载价操作'
            : '删除后无法撤销恢复',
        isFunction: false,
        isSureModal: +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice,
        confirm: () => {
          if (!(+row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice)) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
    case 'cancelSort':
      rcjCancelSort();
      break;
    case 'insert':
      operate('add');
      break;
    case 'delRow':
      operate('del');
      break;
    case 'select':
      selectFromMachine();
      break;
    case 'tractorCode':
      resetCodeFun();
      break;
    //批注操作
    case 'add-note':
    case 'edit-note':
    case 'del-note':
    case 'show-note':
    case 'hide-note':
    case 'del-all-note':
      operateNote(menu, row);
      break;
  }
};
const resetCodeFun = () => {
  //重置承包人材料号
  console.log('重置resetCodeFun');
  let apiData = getParamsData({});
  csProject.cbrRcjCzCode(apiData).then(res => {
    if (res.status === 200) {
      getHumanMachineData();
    }
  });
};
const apiNameObj = {
  8: {
    key: 'zgjRcj',
    add: 'addZgjRcj',
    del: 'deleteZgjRcj',
    update: 'updateZgjRcj',
    move: 'zgjRcjMove',
  },
  10: {
    key: 'cbrRcj',
    add: 'addCbrRcj',
    del: 'deleteCbrRcj',
    update: 'updateCbrRcj',
    move: 'cbrRcjMove',
  },
};
const operate = type => {
  //右键插入数据行
  console.log('addDataRow', currentInfo.value);
  let apiData = getParamsData({
    targetSequenceNbr: currentInfo.value?.sequenceNbr,
  });
  apiData[apiNameObj[+projectStore.asideMenuCurrentInfo?.key]['key']] =
    type === 'add' ? {} : JSON.parse(JSON.stringify(currentInfo.value));
  let apiFunName = apiNameObj[+projectStore.asideMenuCurrentInfo?.key][type];
  console.log('操作数据', apiFunName, apiData);
  csProject[apiFunName](apiData)
    .then(res => {
      if (res.status === 200) {
        console.log('操作数据', apiFunName, res);
        getHumanMachineData();
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    projectStore.currentTreeInfo.levelType === 1
      ? projectStore.currentTreeInfo?.id
      : projectStore.currentTreeGroupInfo?.constructId;
  if (projectStore.currentTreeInfo.levelType === 2) {
    apiData.singleId = projectStore.currentTreeInfo?.id; //单项ID
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
// 清除载价格
const clearZaijia = data => {
  let postData = {
    type: projectStore.currentTreeInfo.levelType,
    rcj: { ...data },
    sequenceNbr: data.sequenceNbr,
  };
  postData = getParamsData(postData);
  console.log(
    '🚀 ~ file: index.vue:768 ~ csProject.clearLoadPriceUse ~ postData:',
    postData
  );
  csProject
    .clearLoadPriceUse(JSON.parse(JSON.stringify(postData)))
    .then(res => {
      console.log(
        '🚀 ~ file: index.vue:760 ~ csProject.clearLoadPriceUse ~ res:',
        res
      );
      if (res.result) {
        message.success('清除成功');
        if (projectStore.currentTreeInfo.levelType < 3) {
          data.isChange = true; //标识编辑行
          setProUpdate();
          unifyData.disabled = false;
        }
        getHumanMachineData();
      }
    });
};
const setProUpdate = (updataData = [], type = 'update') => {
  projectStore.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify-humanMachineSummary',
    updataData:
      type === 'update' ? updataData : projectStore.humanUpdataData?.updataData,
    unitIdList: [...setAggreUnitList.value], //设置汇总范围选中的单位列表
    adjustFactor: projectStore.humanUpdataData?.adjustFactor,
    rcjwjcList:
      type === 'rcjwjc' ? updataData : projectStore.humanUpdataData?.rcjwjcList,
  });
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};
const upDateMarketPrice = row => {
  //工程项目更新载价市场价
  let target = tableData.value.find(
    a => a.sequenceNbr === currentInfo.value.sequenceNbr
  );
  if (
    projectStore.deStandardReleaseYear === '12' ||
    (target.deStandardReleaseYear === '12' &&
      projectStore.deStandardReleaseYear === '22')
  ) {
    target.marketPrice = row.marketPrice;
  } else {
    if (Number(projectStore?.taxMade) === 1) {
      target.priceMarket = row.marketPrice;
      target.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(
            target.priceMarket,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    } else {
      //  简易----含税
      target.priceMarketTax = row.marketPrice;
      target.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(
            target.priceMarketTax,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    }
  }
  target.sourcePrice = row.sourcePrice;
  target.isExecuteLoadPrice = true;
  target.isChange = true; //标识编辑行
  getSameUnit();
  let upDateList = getPropData();
  if (upDateList && upDateList.length > 0) {
    setProUpdate(upDateList);
  }
};
const isEditPrice = (row, column) => {
  //判断市场价-含税/不含税市场价  税率是否可以编辑
  console.log(isPartEdit.value, isOtherMaterial.value);
  let isFlag =
    row.ifLockStandardPrice !== 1 &&
    isPartEdit.value &&
    !(row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) &&
    Number(row.edit) !== 1 &&
    !isChangeAva(row) &&
    !isOtherMaterial.value;
  let isEdit =
    (['marketPrice', 'priceMarket', 'priceMarketTax'].includes(column) &&
      isFlag) ||
    (column === 'taxRate' &&
      isFlag &&
      !row.de2012In2022 &&
      !isDeType('12', row.deStandardReleaseYear) &&
      row.kind !== 1);
  console.log('isEditPrice', isEdit);
  return isEdit;
};
const editClosedEvent = async e => {
  const { $table, row, column } = e;
  let field = column.field;
  // 选择重复调用处理
  if (
    [
      'markSum',
      'ifDonorMaterial',
      'ifProvisionalEstimate',
      'ifLockStandardPrice',
    ].includes(field)
  ) {
    return;
  }

  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (isMarketPriceField(row, column.field)) {
    field = 'marketPrice';
    row[field] = +row[field];
  }

  let value = row[field];
  if (
    ['marketPrice', 'priceMarket', 'priceMarketTax', 'taxRate'].includes(
      field
    ) &&
    (value < 0 || value == '')
  ) {
    $table.revertData(row, field);
    return;
  }
  // // 判断单元格值没有修改
  if (['kindSc'].includes(field) && row.kindSc === row.oldKindSC) {
    return;
  }
  if (
    !$table.isUpdateByRow(row, field) &&
    !(
      ['priceMarket', 'priceMarketTax', 'marketPrice', 'taxRate'].includes(
        field
      ) &&
      isEditPrice(row, field) &&
      projectStore.currentTreeInfo.levelType < 3 &&
      row[`${field}lastEdit`] !== value
    )
  ) {
    //含税-不含税市场价需要恢复，所以不做是否变化限制
    return;
  }
  if (
    field === 'transferFactor' &&
    (value < 0 || value > 1000 || value === '')
  ) {
    message.info('三材系数可输入范围[0-1000]');
    $table.revertData(row, field);
    return;
  }
  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  if (field === 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field === 'marketPrice' &&
    value > 0 &&
    row.marketPrice.length > 20
  ) {
    row.marketPrice = value.slice(0, 20);
  }
  if (
    field === 'marketPrice' &&
    value > 0 &&
    projectStore.currentTreeInfo.levelType < 3
  ) {
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (field === 'donorMaterialNumber') {
    if (row.ifDonorMaterial === 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (
      row.ifDonorMaterial !== 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = 1;
    } else if (row.ifDonorMaterial === 1 && (value <= 0 || value === '')) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
    }
    let same = oldData.value.find(l => l.sequenceNbr === row.sequenceNbr);
    console.log(same, row.donorMaterialNumber, same.donorMaterialNumber);
    if (
      ['0', ''].includes(row.donorMaterialNumber) &&
      ['0', ''].includes(same.donorMaterialNumber)
    ) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
      return;
    }
  }
  if (!(await isEditRenGongMarketPrice(field, row))) {
    $table.revertData(row, field);
    return;
  }
  if (field === 'kindSc') {
    row['kindSc'] = [null, '', undefined].includes(value) ? '' : value;
    row.oldKindSC = row['kindSc'];
    if (projectStore.currentTreeInfo.levelType < 3) {
      row['transferFactor'] = ![null, '', undefined].includes(value) ? '1' : '';
    }
  }
  if (
    field === 'unit' &&
    [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key) &&
    row.hasAssociation
  ) {
    $table.revertData(row, field);
    message.warn('已关联暂估数据请取消暂估设置后再调整');
    return;
    // 已关联暂估数据请取消暂估设置后再调整
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    console.log('🚀 ~ filnt ~ upDate:', row);
    upDate(row, field);
  } else {
    linkageRevisePrice(row, field, value);
    if (
      ['priceMarket', 'priceMarketTax', 'taxRate', 'marketPrice'].includes(
        field
      ) &&
      row?.isExecuteLoadPrice
    ) {
      row.sourcePrice = row.oldSourcePrice;
      row.isExecuteLoadPrice = false;
    }
    if (['marketPrice', 'priceMarket', 'priceMarketTax'].includes(field)) {
      //isImportDataType为true，是导入excel市场价手动设置调用的线下数据导入，不可修改为自行询价
      if (!row.isImportDataType) {
        //bug20364   工程市场价修改-价格来源更改为自行询价
        row.sourcePrice = '自行询价';
      }
    }
    if (
      ['priceMarket', 'priceMarketTax', 'marketPrice', 'taxRate'].includes(
        field
      )
    ) {
      row[`${field}lastEdit`] = value;
    }
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateListFirst = getPropData() || [];
    let upDateList = [];
    upDateListFirst.map((item, idx) => {
      if (Object.keys(item).length > 2) {
        upDateList.push(item);
      }
    });
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    } else {
      //没有更改项-判断存储变化值有没有别的数据，如果没有将humanUpdataData设置为空并统一应用按钮置灰
      console.log(projectStore.humanUpdataData);
      if (
        !projectStore.humanUpdataData ||
        (projectStore.humanUpdataData?.isEdit &&
          (!projectStore.humanUpdataData.rcjwjcList ||
            projectStore.humanUpdataData.rcjwjcList?.length === 0) &&
          (!projectStore.humanUpdataData.unitIdList ||
            projectStore.humanUpdataData.unitIdList?.length === 0) &&
          !projectStore.humanUpdataData.adjustFactor)
      ) {
        projectStore.SET_HUMAN_UPDATA_DATA(null);
      }
    }
  }
};
//市场价-税率之间的联动修改
const linkageRevisePrice = (row, field, value) => {
  if (['priceMarket', 'priceMarketTax', 'taxRate'].includes(field)) {
    if (field === 'priceMarket' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }
    //含税市场价
    if (field === 'priceMarketTax' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }

    //税率
    if (field === 'taxRate' && !ObjectUtils.is_Undefined(value)) {
      //含税市场价
      if (+projectStore.taxMade === 1) {
        row.priceMarketTax = NumberUtil.numberScale2(
          NumberUtil.multiply(
            0.01,
            NumberUtil.multiply(row.priceMarket, NumberUtil.add(100, value))
          )
        );
      } else {
        row.priceMarket = NumberUtil.numberScale2(
          NumberUtil.multiply(
            100,
            NumberUtil.divide(row.priceMarketTax, NumberUtil.add(100, value))
          )
        );
      }
    }
  }
};
// 是否修改人工1、2、3类工市场价
const isEditRenGongMarketPrice = (field, row) => {
  return new Promise(resolve => {
    if (
      ['marketPrice'].includes(field) &&
      isSelectFeePolicyDoc.value &&
      renGongCodeList.includes(row.materialCode) &&
      !row.isImportDataType
    ) {
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText: '该市场价已受政策文件调整，是否确认修改',
        isFunction: false,
        confirm: () => {
          resolve(true);
          infoMode.hide();
        },
        close: () => {
          resolve(false);
          infoMode.hide();
        },
      });
    } else {
      resolve(true);
    }
  });
};
const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter(item => item.isChange === true);
  const compareFeild = [
    'marketPrice',
    'ifProvisionalEstimate',
    'ifLockStandardPrice',
    'kindSc',
    'transferFactor',
    'markSum',
    'taxRemoval',
    'priceMarket',
    'priceMarketTax',
    'taxRate',
    'sourcePrice',
  ];
  upDateList.map(item => {
    let obj = {};
    let same = oldData.value.find(l => l.sequenceNbr === item.sequenceNbr);
    if (item.mergeMaterialIds && item.mergeMaterialIds.length) {
      // 如果当前材料是合并之后的材料，则给上传参数添加被合并的材料id集合
      obj.mergeMaterialIds = item.mergeMaterialIds;
    }
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial === 1) {
        obj.donorMaterialNumber = item.totalNumber;
      } else {
        obj.donorMaterialNumber = '';
      }
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('donorMaterialNumber')
    ) {
      obj.donorMaterialNumber =
        obj.ifDonorMaterial === 1 ? item.totalNumber : '';
    }
    compareFeild.map(key => {
      //比较item 和same中的compareFeild字段，不一样的话记住
      if (item[key] != same[key]) {
        obj[key] = item[key];
      }
    });
    if (item.isExecuteLoadPrice) {
      obj.isExecuteLoadPrice = true;
    }
    obj.sequenceNbr = item.sequenceNbr;
    obj.libraryCode = item.libraryCode;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = async (isRefresh = true) => {
  //点击统一应用按钮
  if (!projectStore.humanUpdataData) {
    return;
  }
  let type = '';
  // let tar = apiData.constructProjectRcjList[0];
  // if (
  //   Object.keys(tar).length === 1 &&
  //   apiData.constructProjectRcjList.length === 1
  // ) {
  //   apiData.constructProjectRcjList = [];
  // }

  //只是清除载价就传空值，清除载价+改市场价传修改数据
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (!projectStore.humanUpdataData?.unitIdList)
    projectStore.humanUpdataData.unitIdList = [];
  let postData = getParamsData({});
  if (projectStore.humanUpdataData.adjustFactor?.isEdit) {
    let apiData = {
      ...postData,
      coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
      rcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
      ),
      unitIdList: [...projectStore.humanUpdataData.unitIdList],
    };
    console.log('constructAdjustmentCoefficient', apiData);
    await csProject.constructAdjustmentCoefficient(apiData).then(res => {
      console.log('统一应用系数', res);
    });
  }
  if (projectStore.humanUpdataData.updataData) {
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.updataData)
      ),
      unitIdList: [...projectStore.humanUpdataData.unitIdList],
    };
    let apiFunName =
      projectStore.currentTreeInfo.levelType === 1
        ? 'changeRcjConstructProject'
        : 'changeRcjSingleProject';
    console.log('统一应用接口参数', apiData, apiFunName);
    // debugger;
    await csProject[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (projectStore.humanUpdataData.sourcePriceData) {
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.sourcePriceData)
      ),
      unitIdList: [...projectStore.humanUpdataData.unitIdList],
    };
    console.log('统一应用接口参数', apiData);
    await feePro.rcjFromUnitUpdate(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (projectStore.humanUpdataData.rcjwjcList?.length > 0) {
    type = 4;
    let apiData = {
      ...postData,
      excludeRcjIdList: [...projectStore.humanUpdataData.rcjwjcList],
    };
    let apiFunName =
      projectStore.currentTreeInfo.levelType === 2
        ? 'singleRcjWjc'
        : 'constructRcjWjc';
    console.log('统一应用接口参数', apiData);
    await feePro[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  message.success('应用成功!');
  // setAggreUnitList.value = [];
  projectStore.SET_HUMAN_UPDATA_DATA(null);
  if (isRefresh) getHumanMachineData(type);
  unifyData.disabled = true;
  setGlobalLoading(false);
};
const activeRowMethod = ({ row, rowIndex }) => {
  if (
    !cellBeforeEditMethod() ||
    ([8].includes(+projectStore.asideMenuCurrentInfo?.key) && row.lock)
  )
    return false;
  return true;
};
const upDate = (row, field) => {
  let apiData = {
    type: projectStore.currentTreeInfo.levelType,
  };
  const upDateField = [
    'transferFactor',
    'kindSc',
    'marketPrice',
    'materialName',
    'specification',
    'unit',
    'ifProvisionalEstimate',
    'ifLockStandardPrice',
    'markSum',
    'donorMaterialNumber',
    'producer',
    'manufactor',
    'brand',
    'deliveryLocation',
    'qualityGrade',
    'output',
    'taxRemoval',
    'priceMarket',
    'priceMarketTax',
    'taxRate',
    'dispNo',
    //新增加暂估表和承包人表编辑字段
    'relevancyMaterialCodeList',
    'materialCodeCBR',
    'materialCode',
    'totalNumber',
    'riskCoefficient',
    'benchmarkUnitPrice',
    'tbdj', //投标单价
    'zdje', //暂定金额
    'lock',
    'remark',
    //批注-字段+显示隐藏设置
    'unitPostil',
    'singlePostil',
    'constructPostil',
    'constructPostilState',
    'singlePostilState',
    'unitPostilState',
  ];
  let constructProjectRcj = {};
  if (upDateField.includes(field)) {
    if (['zdje', 'tbdj'].includes(field)) {
      // const { scjField, hjfield } = getShowFeild();
      constructProjectRcj['marketPrice'] = row[field];
    } else {
      constructProjectRcj[field] = row[field];
    }
  } else if (field === 'type') {
    constructProjectRcj.kind = getKind(row.type);
  } else if (field === 'ifDonorMaterial') {
    constructProjectRcj.ifDonorMaterial = row[field];
  }
  apiData.libraryCode = row.libraryCode;
  apiData = getParamsData(apiData);
  console.log('修改人材机数据', apiData);
  setGlobalLoading(true);
  if ([8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)) {
    apiData[apiNameObj[+projectStore.asideMenuCurrentInfo?.key]['key']] = {
      ...constructProjectRcj,
      sequenceNbr: row.sequenceNbr,
    };
  } else {
    apiData.sequenceNbr = row.sequenceNbr;
    apiData.constructProjectRcj = { ...constructProjectRcj };
  }
  let apiFunName = [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)
    ? apiNameObj[+projectStore.asideMenuCurrentInfo?.key]['update']
    : 'changeRcj';
  csProject[apiFunName](apiData)
    .then(res => {
      if (res.status === 200) {
        console.log('修改人材机数据返回结果', apiFunName, res);
        isCurrent.value = row;
        getHumanMachineData();
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};

// =====================查找逻辑
let lookupVisible = ref(false);
const openLookup = event => {
  // console.log(event);
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};
let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: 'materialName',
      label: '名称',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'specification',
      label: '规格',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'materialCode',
      label: '编码',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketPrice',
      label: '市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'totalNumber',
      label: '数量',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: '&&',
  tableData: tableData,
});
const lookupCallback = rows => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData, true);
  } else {
    tableData.value = rows;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    changeCurrentInfo(info);
  });
};

const changeCurrentInfo = row => {
  if (row) {
    humanTable.value?.setCurrentRow(row);
    currentInfo.value = row;
    setMoveInfo();
  }
};

const exportBeforeColumnHandler = () => {
  // 导出前将必须导出得列添加进去，进行隐藏处理
  const $table = humanTable.value;
  let necessaryColumns = [
    {
      title: '材料编码',
      field: 'materialCode',
      fixed: 'left',
    },
    {
      title: '名称',
      field: 'materialName',
      fixed: 'left',
    },
    {
      title: '规格型号',
      field: 'specification',
    },
    {
      title: '类型',
      field: 'type',
      fixed: 'left',
    },
    {
      title: '单位',
      field: 'unit',
    },
  ];
  if (isDeType('12')) {
    necessaryColumns = [
      ...necessaryColumns,
      { title: '定额价', field: 'dePrice' },
      { title: '市场价', field: 'marketPrice' },
      { title: '除税系数(%)', field: 'taxRemoval' },
    ];
  } else {
    necessaryColumns = [
      ...necessaryColumns,
      {
        title:
          Number(projectStore.taxMade) === 1 ? '不含税基期价' : '含税基期价',
        field: 'dePrice',
      },
      { title: '不含税市场价', field: 'priceMarket' },
      { title: '含税市场价', field: 'priceMarketTax' },
      { title: '税率', field: 'taxRate' },
    ];
  }
  let currentHideColumns = [];
  for (let item of necessaryColumns) {
    if (!showColumns.value.find(i => i.field === item.field)) {
      showColumns.value.push(item);
      currentHideColumns.push(item.field);
      nextTick(() => {
        $table.hideColumn(item.field);
      });
    }
  }
  return currentHideColumns;
};
const exportExcel = (dataType = '') => {
  const $table = humanTable.value;
  if (dataType !== 'all' && $table.getCheckboxRecords().length === 0) {
    message.info('请选择导出数据');
    return;
  }
  const hideColumns = exportBeforeColumnHandler();
  setTimeout(() => {
    $table.exportData({
      filename: '人材机汇总导出报表',
      sheetName: 'Sheet1',
      type: 'xlsx',
      // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
      // sheetMethod: sheetMethod, // 配置导出样式
      useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
      isFooter: true, //是否导出表尾（比如合计）
      columns: $table.getTableColumn().fullColumn,
      data: dataType === 'all' ? tableData.value : $table.getCheckboxRecords(),
      columnFilterMethod({ column, $columnIndex }) {
        return !($columnIndex === 0);
      },
      afterExportMethod: () => {
        // 还原列
        for (let item of hideColumns) {
          const index = showColumns.value.findIndex(
            i => i.field === item.field
          );
          if (index >= 0) {
            showColumns.value.splice(index, 1);
            nextTick(() => {
              $table.showColumn(field);
            });
          }
        }
      },
    });
  }, 500);
};
const loadPrice = type => {
  reportModel.value = false;
  switch (type) {
    case 'batch-loadprice':
      typeModal.value = '批量载价';
      break;
    case 'loadprice-report':
      typeModal.value = '载价报告';
      break;
  }
  reportModel.value = true;
  console.log('执行loadPrice', reportModel.value);
};
const close = bol => {
  reportModel.value = false;
  console.log('执行close', reportModel.value);
};
let zjUse = ref(false); //工程项目载价后需要统一应用--未载价默认false
let zjData = ref([]); //载价数据
let saveChangeRow = ref([]); //原表格修改数据
const nextEdit = async data => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value === '载价报告') {
    //工程项目载价成功后需要统一应用
    if (projectStore.currentTreeInfo.levelType < 3) {
      let formData = {
        type: projectStore.currentTreeInfo.levelType,
      };
      formData = getParamsData(formData);
      let apiFun = loadApi.loadPriceList;
      apiFun(formData).then(res => {
        let list = tableData.value.filter(a => a.isChange);
        if (res.status === 200 && res.result) {
          zjData.value = res.result;
          if (zjData.value?.length > 0) {
            //如果载价成功且有数据-需要重新调用接口并将当前数据保存
            zjUse.value = true;
            list = list.filter(
              a => !zjData.value.find(b => b.sequenceNbr === a.sequenceNbr)
            );
            saveChangeRow.value = JSON.parse(JSON.stringify(list));
            getHumanMachineData();
          }
        }
      });
    } else {
      getHumanMachineData();
    }
  }
  console.log('执行nextEdit', reportModel.value);
};
provide('nextStep', nextEdit);
let adjustFactor = ref(false); //调整市场价系数
let marcketFactor = ref('1');
const selfCheck = (value, length, min, max) => {
  // length-小数点长度   min-最小值  max-最大值
  let newValue = value * 1 + '';
  if (newValue === '') return oldMarketFactor.value;
  if (newValue <= min || newValue > max) {
    newValue = oldMarketFactor.value;
    message.info('市场价系数输入范围为(0,1000])');
  }
  let after = newValue.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newValue = parseFloat(newValue).toFixed(length);
  }
  oldMarketFactor.value = newValue;
  return newValue;
};
const changeMarketFactor = () => {
  //调整市场价系数
  let apiData = {
    coefficient: marcketFactor.value * 1,
    rcjList: JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords())),
  };
  apiData = getParamsData(apiData);
  console.log('调整市场价系数', apiData);
  let apiName =
    projectStore.currentTreeInfo.levelType === 3
      ? 'unitAdjustmentCoefficient'
      : '';
  csProject[apiName](apiData).then(res => {
    if (res.status === 200) {
      console.log('调整市场价系数返回结果', res);
      getHumanMachineData();
    }
  });
};
const clickOutside = () => {
  if (adjustFactor.value) {
    console.log('点击人材机汇总表格外部'); //暂时不需要设置-后续人材机汇总左侧树可能需要
    return;
  }
};
onClickOutside(humanTable, clickOutside); //打开调整市场价系数表格可编辑
const sureOrCancel = () => {
  let hasCheck =
    humanTable.value.getCheckboxRecords().length === 0 ? false : true;
  if (!hasCheck) {
    message.warning('请选中要调整的人材机数据行');
    return;
  }

  if (projectStore.currentTreeInfo.levelType === 3 && hasCheck) {
    changeMarketFactor();
  } else {
    let selectRows = humanTable.value.getCheckboxRecords();
    selectRows.map(row => {
      if (
        !(
          (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) ||
          row.isFyrcj === 0 ||
          [
            'QTCLFBFB',
            '34000001-2',
            'J00004',
            'J00031',
            'J00031',
            'C11384',
            'C00007',
            'C000200',
            'C11408',
          ].includes(row.materialCode)
        )
      ) {
        //1. 注1：勾选了二次解析的配比材料、机械台班不受调整系数影响，但其市场价会因子级数据变更而联动变更（当前勾选了二次解析的父级材料、机械不可修改市场价，其值仅会通过子级数据是市场价变更而联动计算）
        //2. 注2：其他材料费、费用人材机即使勾选，也不受人材机调整系数影响；
        if (
          projectStore.deStandardReleaseYear === '12' ||
          (row.deStandardReleaseYear === '12' &&
            projectStore.deStandardReleaseYear === '22')
        ) {
          row.marketPrice = (row.marketPrice * marcketFactor.value).toFixed(2);
        } else {
          if (Number(projectStore.taxMade) === 1) {
            //一般计税: 不含税市场价影响含税
            row.priceMarket = (row.marketPrice * marcketFactor.value).toFixed(
              2
            );
            linkageRevisePrice(row, 'priceMarket', row.priceMarket);
          } else {
            // 简易计税 : 含税市场价影响不含税

            row.priceMarketTax = (
              row.marketPrice * marcketFactor.value
            ).toFixed(2);
            linkageRevisePrice(row, 'priceMarketTax', row.priceMarketTax);
          }
        }
        row.isChange = true; //标识编辑行
        row.isUpdateNum = true; //调整市场价系数
        row.sourcePrice = '自行询价';
      }
    });
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    }
  }
  adjustFactor.value = false;
};
let oldMarketFactor = ref('1'); //调整市场价系数旧值
const jsz = () => {
  marcketFactor.value = '1';
  oldMarketFactor.value = '1';
  adjustFactor.value = true;
};

// 初始统一修改操作按钮属性
const initSetOperateBtnDisabled = () => {
  const list = [
    {
      // 设置导入Excel市场价是否可操作
      name: 'importExcel',
      callback: item => {
        item.disabled = Number(projectStore.asideMenuCurrentInfo.key) !== 0;
      },
    },
    {
      // 设置主要材料显示隐藏
      name: 'set-main-materials',
      callback: item => {
        item.levelType =
          Number(projectStore.asideMenuCurrentInfo.key) === 7 ? [3] : [];
      },
    },
  ];
  list.forEach(item => {
    updateOperateByName(item.name, item.callback);
  });
};
let sortList = reactive([]);
const setSortList = () => {
  //设置表头可升序降序字段列表
  if (projectStore.tabSelectName !== '人材机汇总') return;
  let num = +projectStore.asideMenuCurrentInfo?.key;
  if (![8, 10].includes(num)) {
    sortList = [
      'dispNo',
      'materialCode',
      'type',
      'materialName',
      'specification',
      'unit',
      'totalNumber',
      'dePrice',
      'marketPrice',
      'priceMarket',
      'priceMarketTax',
      'total',
      'priceMarketTotal',
      'priceMarketTaxTotal',
      'taxRate',
      'priceDifferenc',
      'priceDifferencSum',
    ];
  } else {
    sortList = num === 8 ? ['dispNo'] : ['materialCode'];
  }
  console.log('sortList', sortList);
};
let isChangePage = ref([]);
watch(
  () => projectStore.asideMenuCurrentInfo,
  (val, old) => {
    //目前非以及单项外的单项没有人材机汇总
    if (projectStore.tabSelectName === '人材机汇总') {
      isChangePage.value = [];
      //侧边栏数据变化重新更新
      setTypeList();
      setSortList();
      initSort();
      initColumns({
        columns: getTableColumns(),
        pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
      });
      // machuneSpreadsBtn.disabled = true;
      getHumanMachineData();
      getLoadStatus();
      initSetOperateBtnDisabled();
    }
    setScopeBtn();
  }
);
const setScopeBtn = () => {
  //设置汇总范围功能按钮置灰状态
  if (projectStore.currentTreeInfo.levelType === 3) return;
  let settingScope = operateList.value.find(
    item => item.name === 'setting-aggregate-scope'
  );
  // if (projectStore.asideMenuCurrentInfo?.name === '所有人材机') {
  settingScope.disabled = false;
  if (isChangeAggre.value) {
    //按钮功能颜色设置
    settingScope.labelStyle.color = 'rgb(212, 12, 12)';
  } else {
    settingScope.labelStyle.color = '';
  }
  // } else {
  //   settingScope.disabled = true;
  // }
};
let isFirstOpen = ref(true); //是否确认设置设置汇总范围弹框
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    } else if (!projectStore.humanUpdataData) {
      unifyData.disabled = true;
    }
  }
);

const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
      'C11408',
      'C11388',
      'J00006',
      'J00008',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

onMounted(() => {
  if (
    projectStore.tabSelectName === '人材机汇总' &&
    projectStore.asideMenuCurrentInfo?.key === '0'
  ) {
    initColumns({
      columns: getTableColumns(),
      pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
    });
    getHumanMachineData();
  }
  setSortList();
  setAggreUnitList.value = [];
  isFirstOpen.value = true;
  getLoadStatus();
  setScopeBtn();
  setUse();
  // window.addEventListener('resize', openLookup);
});
const setUse = () => {
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
};
// 费用汇总上移下移
const moveDeData = ({ state, type }) => {
  console.log('人材机汇总', state, type);
  let apiData = getParamsData({
    direction: state === 1 ? 'up' : 'down',
  });
  apiData[apiNameObj[+projectStore.asideMenuCurrentInfo?.key]['key']] =
    JSON.parse(JSON.stringify(currentInfo.value));
  let apiFunName = apiNameObj[+projectStore.asideMenuCurrentInfo?.key]['move'];
  console.log('人材机汇总移动', apiData, apiFunName);
  csProject[apiFunName](apiData).then(res => {
    console.log('res移动', res);
    if (res.status === 200) {
      message.success('移动成功');
      getHumanMachineData();
    }
  });
};
onActivated(() => {
  console.log('onActivated');
  insetBus(bus, projectStore.componentId, 'humanMachineSummary', async data => {
    if (data.name === 'batch-loadprice')
      console.log('执行载价'), loadPrice(data.name);
    if (data.name === 'loadprice-report')
      console.log('载价报告'), loadPrice(data.name);
    if (data.name === 'market-price') console.log('调整市场价系数'), jsz();
    if (data.name === 'unify-humanMachineSummary') {
      isUse();
    }
    if (data.name === 'export-table') console.log('导出报表'), exportExcel();
    if (data.name === 'set-main-materials') openSetMainMaterial();
    if (data.name === 'lookup') openLookup();
    if (data.name === 'importExcel') {
      importExcelHandle();
    }
    if (data.name === 'mergeMaterials') openMergeMaterials();
    if (data.name === 'setting-aggregate-scope') {
      setAggreScopeFun();
    }
    if (data.name === 'select-from-machineSummary') {
      //从人材机汇总中选择
      selectFromMachine();
    }
    if (data.name === 'insert') {
      //插入
      operate('add');
    }
    if (data.name === 'machine-no-spreads') {
      machineSpreadsFun();
    }
  });
  getFeePolicyDocData();
  window.addEventListener('keydown', openLookup);
});
let setAggreScopeRef = ref();
const setAggreScopeFun = () => {
  setAggreScopeRef.value.open(true);
};
let mergeMaterialsRef = ref();
const openMergeMaterials = () => {
  mergeMaterialsRef.value.open(true);
};
let importExcelRef = ref();
const importExcelHandle = () => {
  importExcelRef.value.open();
};
let selectFromMachineRef = ref();
const selectFromMachine = () => {
  selectFromMachineRef.value.open();
};
/**
 * 工程级别导入数据前端更新处理
 */
const updateImportData = updateRcj => {
  for (let rcj of updateRcj) {
    let data = tableData.value.find(
      item => item.sequenceNbr === rcj.sequenceNbr
    );
    data[rcj.updatePrice] = rcj[rcj.updatePrice];
    data.sourcePrice = '线下数据导入';
    data.isImportDataType = true;
    editClosedEvent({
      $table: humanTable.value,
      row: data,
      column: { field: rcj.updatePrice },
    });
  }
};
let spreadModal = ref({
  //人材机无价差弹框绑定值
  visible: false,
  value: 1, //1-选中范围  2-工料机
});
const machineSpreadsFun = async () => {
  console.log('人材机汇总无价差处理');
  initSpreadValue();
  await operateWjcfun('query');
  spreadModal.value.visible = true;
};
const initSpreadValue = () => {
  spreadModal.value.value = 1;
  spreadModal.value.visible = false;
};
let checkBoxSelectList = ref({
  list: [],
  kind: null,
});
//人材机无价差记住上次选择并回显
const operateWjcfun = async type => {
  let apiData = getParamsData({
    type: projectStore.currentTreeInfo.levelType,
  });
  if (type === 'update') {
    apiData.state = spreadModal.value.value - 1;
  }
  let apiFunName = type === 'update' ? 'updateRcjWjcState' : 'queryRcjWjcState';
  await feePro[apiFunName](apiData).then(res => {
    console.log('apiFunName', apiFunName, res, apiData);
    if (res.status === 200 && type === 'query') {
      spreadModal.value.value = res.result ? res.result + 1 : 1;
    }
  });
};
const spreadBtnFun = async type => {
  checkBoxSelectList.value = [];
  if (!type) {
    initSpreadValue();
    return;
  }
  console.log('spreadBtnFun', spreadModal.value);
  let apiData = getParamsData({});
  let selectRows = humanTable.value.getCheckboxRecords(true);
  if (spreadModal.value.value === 1 && selectRows?.length === 0) {
    message.warning('请选择数据进行操作');
    return;
  }
  let list = spreadModal.value.value === 1 ? selectRows : tableData.value;
  checkBoxSelectList.value.list = selectRows.map(a => {
    return a.sequenceNbr;
  });
  checkBoxSelectList.value.kind = +projectStore.asideMenuCurrentInfo?.key;
  let upDateList = list.filter(a => !a.ifLockStandardPrice);
  await operateWjcfun('update');
  if (projectStore.currentTreeInfo.levelType === 3) {
    if (spreadModal.value.value === 1) {
      apiData.rcjIdList = [...checkBoxSelectList.value.list];
    } else {
      apiData.allRcj = 1;
    }
    console.log('更改数据列表', apiData);
    await feePro
      .unitRcjWjc(apiData)
      .then(res => {
        console.log('unitRcjWjc------', res);
        getHumanMachineData(4);
      })
      .finally(() => {
        initSpreadValue();
      });
  } else {
    //1-2 需要前端修改  统一应用（过滤市场价锁定的数据）
    //选中范围  或者全部数据进行更改   22-简易含税市场价=>含税基期价    一般不含税=>不含税基期价   12：市场价=>定额价
    // const { scjField } = getShowFeild();//
    if (
      spreadModal.value.value === 2 &&
      +projectStore.asideMenuCurrentInfo?.key !== 0
    ) {
      let list = tableData.value.map(a => {
        return a.sequenceNbr;
      });
      setProUpdate(list, 'rcjwjc');
    } else {
      setProUpdate([], 'rcjwjc');
    }
    upDateList &&
      upDateList.map(a => {
        let scjField =
          +a.deStandardReleaseYear === 12
            ? 'marketPrice'
            : +projectStore.taxMade === 1
            ? 'priceMarket'
            : 'priceMarketTax'; //按照每行数据的deStandardReleaseYear判断
        if (+a[scjField] != +a.dePrice) {
          a[scjField] = a.dePrice;
          linkageRevisePrice(a, scjField, a[scjField]); //联动计算市场价
          a['sourcePrice'] = ''; //重置价格来源
          a.isChange = true; //标识编辑行
        }
      });
    let useUpdateList = getPropData();
    if (useUpdateList && useUpdateList.length > 0) {
      //存储store中统一应用全局设置
      setProUpdate(useUpdateList);
    }
    console.log(
      '更改数据列表',
      upDateList,
      useUpdateList,
      projectStore.humanUpdataData
    );
  }
  spreadModal.value.visible = false;
};
let selectData = ref([]);

const toggleAllCheckboxEvent = () => {
  const $table = humanTable.value;
  if ($table) {
    $table.toggleAllCheckboxRow();
  }
};
onDeactivated(() => {
  console.log('onDeactivated');
  lookupVisible.value = false;
  window.removeEventListener('keydown', openLookup);
});
const getLoadStatus = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    type: projectStore.currentTreeInfo.levelType,
  };
  apiData = getParamsData(apiData);
  console.log('loadPriceStatus', apiData);
  loadApi.loadPriceStatus(apiData).then(res => {
    console.log('++++++++++++++', res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum === 1 && [1, 2].includes(Number(item.levelMark))) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const getKind = type => {
  let value;
  switch (type) {
    case '其他费':
      value = 0;
      break;
    case '人工费':
      value = 1;
      break;
    case '材料费':
      value = 2;
      break;
    case '机械费':
      value = 3;
      break;
    case '设备费':
      value = 4;
      break;
    case '主材费':
      value = 5;
      break;
    case '商砼':
      value = 6;
      break;
    case '砼':
      value = 7;
      break;
    case '浆':
      value = 8;
      break;
    case '商浆':
      value = 9;
      break;
    case '配比':
      value = 10;
      break;
    case '无':
      value = -1;
      break;
  }
  return value;
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
    case -1:
      value = '无';
      break;
  }
  return value;
};
const getOldData = () => {
  tableData.value &&
    tableData.value.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
        kindSc: item.kindSc,
        transferFactor: item.transferFactor,
        taxRemoval: item.taxRemoval,
        priceMarket: item.priceMarket,
        priceMarketTax: item.priceMarketTax,
        taxRate: item.taxRate,
        sourcePrice: item.sourcePrice,
        markSum: item.markSum ? 1 : 0,
      });
    });
  console.log('getOldData', oldData.value);
};
const setCurrentInfo = () => {
  //设置当前选中行
  let idx = -1;
  if (
    isCurrent.value?.sequenceNbr &&
    currentInfo.value?.sequenceNbr &&
    isCurrent.value.sequenceNbr === currentInfo.value.sequenceNbr
  ) {
    //如果选中行和当前行一致-重新选中
    idx = tableData.value.findIndex(
      a => a.sequenceNbr === isCurrent.value.sequenceNbr
    );
  }

  if (idx === -1) {
    //设置新的选中行
    let isCurrentRow;
    if (isCurrent.value) {
      isCurrentRow = tableData.value.find(
        item => isCurrent.value.sequenceNbr === item.sequenceNbr
      );
    } else {
      isCurrent.value = tableData.value[0];
    }
    changeCurrentInfo(isCurrentRow || tableData.value[0]);
  } else {
    changeCurrentInfo(tableData.value[idx]);
  }
  //设置复选框的置灰状态
  checkBoxIsShow();
};
let setAggreUnitList = ref([]);
let isChangeAggre = ref(false); //设置汇总颜色标识
const setAggreScopeOk = (list, isChange) => {
  //设置汇总范围点击确定
  //先进行判断是否有需要汇总的内容---有的话弹框并执行统一应用，没有的话下一步
  if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
    infoMode.show({
      isSureModal: false,
      iconType: 'icon-querenshanchu',
      infoText: '人材机数据已修改，是否应用？',
      confirm: async () => {
        await isUse(false);
        setNewUnitList(list);
        infoMode.hide();
      },
      close: () => {
        projectStore.SET_HUMAN_UPDATA_DATA(null);
        setNewUnitList(list);
        infoMode.hide();
      },
    });
  } else {
    setNewUnitList(list);
  }
  isChangeAggre.value = isChange;
  setScopeBtn();
};
const setNewUnitList = list => {
  setAggreUnitList.value = list; //设置新的setAggreUnitList.value
  isFirstOpen.value = false;
  setAggreScopeRef.value.cancel(); //关闭汇总弹框
  getHumanMachineData(); //获取表格数据
};
let mergeFormData = ref({});
const getSortStatus = () => {
  //获取排序状态
  let formData = getParamsData({
    type: projectStore.currentTreeInfo.levelType,
    kind: +projectStore.asideMenuCurrentInfo?.key,
  });
  csProject.querySortStatus(formData).then(res => {
    //回显人材机汇总字段排序设置sortVal-true升序   false-降序
    console.log('querySortStatus', res, formData);
    if (res.result) {
      // debugger;
      sortFiled.value = res.result?.field;
      sortVal.value = res.result.order === 'desc' ? false : true;
    }
  });
};
const annotationsObj = {
  noteFeild: {
    1: 'constructPostil',
    2: 'singlePostil',
    3: 'unitPostil',
  },
  showFeild: {
    1: 'constructPostilState',
    2: 'singlePostilState',
    3: 'unitPostilState',
  },
};
const getHumanMachineData = async (type = '') => {
  selectData.value = [];
  setGlobalLoading(true);
  changeMergeMaterialsDisabled();
  const kind = Number(projectStore.asideMenuCurrentInfo?.key);
  let levelType = projectStore.currentTreeInfo.levelType;
  let formData = {
    type: levelType,
    kind,
  };
  if (levelType < 3) {
    formData.unitIdList = [...setAggreUnitList.value];
  }
  formData = getParamsData(formData);
  if (!isFirstOpen.value && setAggreUnitList.value.length === 0) {
    tableData.value = [];
    setGlobalLoading(false);
    setCurrentInfo();
    return;
  }
  if (type == 3) {
    formData.sort = {
      field: sortFiled.value,
      order: sortVal.value ? 'asc' : 'desc',
    };
  } else {
    //查询状态
    getSortStatus();
  }
  const needSave = ['priceMarket', 'priceMarketTax', 'marketPrice', 'taxRate']; //非单位工程行数据需要另存的字段值-为了编辑判断是否改变
  mergeFormData.value = formData;
  console.log('queryConstructRcjByDeId', formData);
  console.time('页面时间');
  await feePro
    .queryConstructRcjByDeId(formData)
    .then(res => {
      // debugger;
      console.log('isChangePage.value', isChangePage.value);
      if (res.status === 200 && res.result && res.result.length > 0) {
        // let num = 1;
        res.result &&
          res.result.map((item, index) => {
            // item.dispNo = num++;
            item.type = getType(item.kind);
            item.donorMaterialNumber =
              Number(item.donorMaterialNumber) === 0
                ? ''
                : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
            if ([undefined, null].includes(item.kindSc)) {
              item.kindSc = '';
            }
            item.oldKindSC = item.kindSc;
            item.oldSourcePrice = item.sourcePrice;
            if ([8, 10].includes(+projectStore.asideMenuCurrentInfo?.key)) {
              // const { scjField, hjfield } = getShowFeild();
              if (+projectStore.asideMenuCurrentInfo?.key === 8) {
                if (
                  otherCodeList.includes(item.materialCode) &&
                  !item.isBfh &&
                  item.unit === '%'
                )
                  //其他材料其他机械且isBfh为否，且单位为%，则单位为元
                  item.unit = '元';
                item.relevancyMaterialCodeList = item?.relevancyMaterialCodeList
                  ? [...item?.relevancyMaterialCodeList].join(',')
                  : '';
                item.zdje = item?.marketPrice;
                item.hjje = item?.total;
              } else {
                item.tbdj = item?.marketPrice;
                item.hjje = item?.total;
              }
            }
            //批注-暂时
            item.annotations = item[annotationsObj['noteFeild'][levelType]]; //批注重新赋值
            if (levelType === 3) {
              item.isShowAnnotations =
                isChangePage.value.includes(item.sequenceNbr) &&
                item[annotationsObj['showFeild'][levelType]] &&
                item?.annotations?.length > 0;
              item.annotationsVisible =
                isChangePage.value.includes(item.sequenceNbr) &&
                item?.isShowAnnotations
                  ? true
                  : false;
            } else {
              needSave.map(key => {
                item[`${key}lastEdit`] = item[key];
              });
            }
          });
        // isChangePage.value = false;
        // tableData.value = res.result;
        nextTick(() => {
          if (!zjUse.value) {
            tableData.value = disposeDeTypeData(res.result, true, true);
            setCurrentInfo();
            oldData.value = [];
            getOldData();
          } else if (zjUse.value && levelType < 3) {
            console.log(' zjData.value', zjData.value);
            res.result.map((a, index) => {
              if (
                saveChangeRow.value?.find(c => c.sequenceNbr === a.sequenceNbr)
              ) {
                let row = saveChangeRow.value.find(
                  c => c.sequenceNbr === a.sequenceNbr
                );
                res.result.splice(index, 1, row);
              }
              if (zjData.value.find(b => b.sequenceNbr === a.sequenceNbr))
                a.isChange = true;
            });
            tableData.value = disposeDeTypeData(res.result, true, true);
            setCurrentInfo();
            if (saveChangeRow.value?.length > 0) {
              //获取变更行便于统一应用
              let upDateList = getPropData();
              // debugger;
              if (upDateList && upDateList.length > 0) {
                setProUpdate(upDateList);
              }
            }
            zjUse.value = false;
          }
          if (levelType < 3) {
            getSameUnit();
          }
          originalTableData = xeUtils.clone(tableData.value, true);
          if (kind === 0) {
            // 记录所有人材机数据
            allRCJTableData.value = xeUtils.clone(tableData.value, true);
          }
          console.log('人材机汇总数据---排序', sortFiled.value);
          setMoveInfo();
          console.log('人材机汇总数据', tableData.value);
        });
        nextTick(() => {
          console.timeEnd('页面时间');
        });
      } else {
        tableData.value = [];
        currentInfo.value = '';
      }
    })
    .finally(() => {
      setGlobalLoading(false);
      projectStore.isAutoPosition = false;
    });
  if (type === 4 && kind === checkBoxSelectList.value.kind) {
    //回显上次选中内容，并设置右上角合价
    let selectRows = tableData.value.filter(a =>
      checkBoxSelectList.value.list.includes(a.sequenceNbr)
    );
    humanTable.value.setCheckboxRow(selectRows, true);
    console.log('selectRows', selectRows);
  }
};
const initSort = () => {
  //初始化取消排序状态
  sortFiled.value = '';
  sortVal.value = false;
};
const rcjCancelSort = () => {
  //取消排序
  let apiData = {
    kind: projectStore.asideMenuCurrentInfo?.key,
  };
  apiData = getParamsData(apiData);
  let { levelType } = projectStore.currentTreeInfo;
  let apiName =
    levelType === 1
      ? 'constructRcjCancelSort'
      : levelType === 2
      ? 'singleRcjCancelSort'
      : 'unitRcjCancelSort';
  console.log('rcjCancelSort', apiData, apiName);
  feePro[apiName](apiData).then(res => {
    if (res.status === 200) {
      initSort();
      getHumanMachineData();
    }
  });
};

const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map(item => {
      let otherSameUnit = tableData.value.filter(
        unit =>
          unit.materialCode === item.materialCode &&
          unit.materialName === item.materialName &&
          // unit.unitId === item.unitId &&
          unit.unit === item.unit &&
          unit.specification === item.specification &&
          Number(unit.dePrice) === Number(item.dePrice) &&
          unit.ifDonorMaterial == item.ifDonorMaterial &&
          unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
          unit.ifLockStandardPrice == item.ifLockStandardPrice &&
          unit.sequenceNbr !== item.sequenceNbr &&
          Number(unit.marketPrice) !== Number(item.marketPrice)
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  tableData.value &&
    tableData.value.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
  console.log('addColorList', addColorList);
};
const cellTableStyle = ({ row, column }) => {
  if (
    isMarketPriceField(row, column.field) ||
    (projectStore.deType === '22' && ['priceMarket'].includes(column.field))
  ) {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      return {
        color: '#059421',
        backgroundColor: '#fae4b2',
      };
    }
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
    //22定额不含税市场价和定额价比较标识颜色
    if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
/**
 * 是否在含税/不含税市场价格字段操作市场价字段，只在工程级别得12定额数据下存在
 */
const isPriceMarketOperateMarketPrice = (row, field) => {
  const taxMade = 'priceMarket' === field ? 1 : 0;
  return (
    isDeType('12', row.deStandardReleaseYear) &&
    ['priceMarketTax', 'priceMarket'].includes(field) &&
    Number(projectStore.taxMade) === taxMade
  );
};

/**
 * 是否市场价字段
 * 工程项目级别，12定额数据市场价显示和操作是在含税市场价或者不含税市场价字段上操作得
 * @param row
 * @param field
 */
const isMarketPriceField = (row, field) => {
  return 'marketPrice' === field || isPriceMarketOperateMarketPrice(row, field);
};
const cellStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
  if (column.field === 'priceMarket' && projectStore.taxMade === 1) {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
  if (column.field === 'priceMarketTax' && projectStore.taxMade === 0) {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.priceMarketTax >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarketTax > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarketTax >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarketTax < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
const rowStyle = ({ row }) => {
  if (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) {
    if (row.field !== 'dispNo') {
      return {
        backgroundColor: '#FFFEC9',
        color: '#ACACAC',
        'font-weight': 'bold',
      };
    }
  }
  if (row.highlight) {
    return {
      backgroundColor: '#FCF8EF',
    };
  }
  if (row.sourcePrice) {
    return {
      backgroundColor: '#FFFEC9',
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case 'markSum':
      row.checkIsShow = row.markSum === 1 ? false : true;
      break;
    case 'ifDonorMaterial':
      break;
    case 'ifProvisionalEstimate':
      // if (
      //   row.ifProvisionalEstimate === 1 &&
      //   projectStore.currentTreeInfo.levelType < 3
      // )
      //   row.ifLockStandardPrice = 1;

      //是否暂估 勾选和不勾选都需要联动市场价锁定
      if (projectStore.currentTreeInfo.levelType < 3) {
        row.ifLockStandardPrice = row.ifProvisionalEstimate;
      }
      break;
    case 'ifLockStandardPrice':
      if (
        row.ifLockStandardPrice === 0 &&
        projectStore.currentTreeInfo.levelType < 3
      )
        row.ifProvisionalEstimate = 0; //1. 设置暂估价时需要同时关联勾选锁定市场价，取消锁定市场价时同步会将暂估状态取消；
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      break;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(row, type);
  } else {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    }
    if (type === 'ifDonorMaterial') {
      row.donorMaterialNumber =
        row.ifDonorMaterial === 1 ? row.totalNumber : '';
      humanTable.value.reloadRow(row, {});
    }
  }
};

const getCurrentIndex = (item, type) => {
  console.log('item', item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  console.log('==============', isCurrent);
};

const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
  isCurrent.value = row;
  if (projectStore.currentTreeInfo.levelType < 3) {
    getCurrentIndex(row);
  }
  setMoveInfo();
};
const setMoveInfo = () => {
  //根据当前选中行判断上下移动限制
  let idx = tableData.value.findIndex(
    a => a.sequenceNbr === currentInfo.value?.sequenceNbr
  );
  let obj = {
    isCurrent: sortFiled.value ? 0 : idx,
    isLast: sortFiled.value ? true : tableData.value.length - 1 === idx,
  };
  emits('getMoveInfo', obj);
};
const sortFiled = ref('');
const sortVal = ref(false);
// 点击排序
const sortClick = filedId => {
  if (sortFiled.value === filedId) {
    sortVal.value = !sortVal.value;
  } else {
    sortVal.value = true;
  }
  if (
    (filedId === 'dispNo' &&
      [7, 8].includes(+projectStore.asideMenuCurrentInfo?.key)) ||
    filedId !== 'dispNo'
  ) {
    sortFiled.value = filedId;
  }
  let status = tableData.value.every(x => !x.dispNo);
  if (status) return;
  getHumanMachineData(3);
};
// 定位方法
const posRow = sequenceNbr => {
  console.log('人材机汇总定位', sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getHumanMachineData();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
};

const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['priceMarketTax', 'priceMarket'].includes(field)) {
      const taxMade = 'priceMarket' === field ? 1 : 0;
      if (Number(projectStore.taxMade) === taxMade) {
        //   if (projectStore.currentTreeInfo.levelType === 1) {
        //   return isChangeAva(row) ? '-' : row.marketPrice;
        // }
        return isChangeAva(row) ? '-' : row.marketPrice;
      }
    }
    return '/';
  }
  return isChangeAva(row) ? '-' : row[field];
};
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'ys',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: getTableColumns(),
      pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
    });
  },
});
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

const changeMergeMaterialsDisabled = () => {
  updateOperateByName('mergeMaterials', item => {
    item.disabled = ![0, 1, 2, 3, 4, 5, 6].includes(
      Number(projectStore.asideMenuCurrentInfo?.key)
    );
  });
};

const selfTestLocateTable = row => {
  let data = tableData.value.find(item => item.sequenceNbr === row.sequenceNbr);
  humanTable.value.setCurrentRow(data);
  humanTable.value.scrollToRow(data);
};

// 批注样式处理
const customCell = ({ rowIndex, column, row }) => {
  let className = '';
  // let style = {}
  // // 批注提示
  if (column.field === 'materialName' && row?.annotations) {
    className += ' note-tips';
  }
  // if (column.field === 'materialName') {
  //   className += ' addNote';
  // }
  // return { style: style,class: className};
  return className;
  //return projectStore.currentTreeInfo.levelType === 3 ? cellStyle({  column , row }) : cellTableStyle({ column , row });
};

const cellClassName = info => {
  let className = selectedClassName(info);

  // 处理 下拉填充时，样式问题
  const { row: infoRow, $columnIndex, column } = info;
  const { columnIndex: cIndex, row } = currentCellDragFill.value;
  if (
    displayedFields.value.includes(column.field) &&
    !className &&
    row &&
    'sequenceNbr' in row &&
    row.sequenceNbr === infoRow.sequenceNbr &&
    $columnIndex === cIndex
  ) {
    // 处理 非编辑状态，选中的样式效果
    // const { columnIndex, rowId } = currentCellData.value;
    // if((!column?.editRender || column.editRender?.notCanEdit) && infoRow['sequenceNbr'] === rowId && $columnIndex === columnIndex){
    //   className = 'cell-selected';
    // }else {
    //   className = '';
    // }
    className = '';
  }

  className += dragFillCellClassName(info) + customCell(info);
  return className;
};

// 回调
const useDragCb = async dragFillInfo => {
  const { rows, startRow, column, rowIds } = dragFillInfo;
  const idList = [];

  // 定义字段处理逻辑
  // const fieldHandlers = {
  //   // 供货方式
  //   'ifDonorMaterial': (item) => {
  //     if (item.row.checkIsShow) {
  //       item.row[column.field] = startRow[column.field];
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   },
  //   //是否暂估
  //   'ifProvisionalEstimate': (item) => {
  //     if (item.row.checkIsShow && !['人工费', '机械费'].includes(item.row.type) &&
  //         !(otherCodeList.includes(item.row.materialCode) && item.row.isBfh)) {
  //       item.row[column.field] = startRow[column.field] == null ? 0 : startRow[column.field];// 首次创建的数据 为undefined
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   },
  //   //市场价锁定
  //   'ifLockStandardPrice': (item) => {
  //     if (item.row.checkIsShow) {
  //       item.row[column.field] = startRow[column.field] == null ? 0 : startRow[column.field];// 首次创建的数据 为undefined
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   }
  // };
  const commonFieldHandler = (item, field, defaultValue = 0) => {
    if (item.checkIsShow) {
      idList.push(item.sequenceNbr);
    }
  };
  // 定义字段处理逻辑
  const fieldHandlers = {
    // 供货方式
    ifDonorMaterial: item => commonFieldHandler(item, column.field),
    // 是否暂估
    ifProvisionalEstimate: item => {
      if (
        item.checkIsShow &&
        !['人工费', '机械费'].includes(item.type) &&
        !(otherCodeList.includes(item.materialCode) && item.isBfh)
      ) {
        commonFieldHandler(item, column.field);
      }
    },
    // 市场价锁定
    ifLockStandardPrice: item => commonFieldHandler(item, column.field),
  };
  // 遍历 rows 并根据字段类型调用相应的处理逻辑
  rows.forEach(item => {
    const handler = fieldHandlers[column.field];
    if (handler) {
      handler(item.row);
    }
    // 根据 levelType 决定是否调用 CheckboxChange
    if (projectStore.currentTreeInfo.levelType < 3) {
      CheckboxChange(item.row, column.field);
    }
  });

  // 如果 levelType 为 3，发送 API 请求
  if (projectStore.currentTreeInfo.levelType === 3) {
    try {
      // 构造 API 请求数据
      const apiData = getParamsData({
        column: column.field,
        value: startRow[column.field],
        idList: idList,
      });

      console.log('feePro.unitRcjBatchUpdate 下拉填充参数=》', apiData);
      const res = await feePro.unitRcjBatchUpdate(apiData);
      console.log('feePro.unitRcjBatchUpdate 下拉填充返回值=》', res);

      if (res.status === 200 && res.code === 200) {
        console.log('feePro.unitRcjBatchUpdate 下拉填充完成');
        await getHumanMachineData();
      }
    } catch (error) {
      console.error('feePro.unitRcjBatchUpdate 错误', error);
    }
  }
};
// 处理如果鼠标移出，在移入时，出现短连的情况
const processingData = dragFillInfo => {
  const { rows, startRow, startRowIndex, column, endRowIndex } = dragFillInfo;
  const idList = [];
  const updatedRows = [];

  const commonFieldHandler = (item, field, defaultValue = 0) => {
    if (item.checkIsShow) {
      item[field] = startRow[field] == null ? 0 : startRow[field]; // 使用空值合并运算符
      idList.push(item.sequenceNbr);
    }
  };
  // 定义字段处理逻辑
  const fieldHandlers = {
    // 供货方式
    ifDonorMaterial: item => commonFieldHandler(item, column.field),
    // 是否暂估
    ifProvisionalEstimate: item => commonFieldHandler(item, column.field),
    // 市场价锁定
    ifLockStandardPrice: item => commonFieldHandler(item, column.field),
  };

  // 获取需要处理的数据范围
  console.log(startRowIndex, endRowIndex);
  let arrList = [];
  if (startRowIndex > endRowIndex) {
    arrList = tableData.value.slice(endRowIndex, startRowIndex + 1).reverse();
  } else {
    arrList = tableData.value.slice(startRowIndex, endRowIndex + 1);
  }

  // 遍历处理数据
  arrList.forEach((item, index) => {
    const handler = fieldHandlers[column.field];
    if (handler) {
      handler(item);
    }

    // 更新 dragFillInfo.rows
    updatedRows.push({
      rowIndex: startRowIndex + index, // 动态计算 rowIndex
      row: item,
    });
  });

  // 更新 dragFillInfo
  dragFillInfo.rows = updatedRows;
  dragFillInfo.rowIds = idList;
};
let delNoteMol = ref({
  visible: false,
  dataStatus: null,
});
const operateNote = (menu, row) => {
  console.log('operateNote', menu, row);
  let levelType = projectStore.currentTreeInfo?.levelType;
  let noteField = annotationsObj['noteFeild'][levelType];
  let showField = annotationsObj['showFeild'][levelType];
  let formData;
  switch (menu.code) {
    case 'add-note':
      row.annotations = '';
      row.annotationsVisible = true;
      row.noteEditVisible = true;
      break;
    case 'edit-note':
      row.noteEditVisible = true;
      row.annotationsVisible = true;
      break;
    case 'del-note':
      row.annotations = null;
      row.annotationsVisible = false;
      row[noteField] = null;
      if (levelType === 3) {
        upDate(row, noteField);
      } else {
        formData = getParamsData({
          type: levelType,
          sequenceNbr: row.sequenceNbr,
          postil: row[noteField],
        });
        console.log('updatePostil-formData', formData);
        csProject.updatePostil(formData).then(res => {
          console.log('updatePostil', res, formData);
        });
      }
      break;
    case 'show-note': //尝显示批注
    case 'hide-note': //隐藏批注
      row.isShowAnnotations = menu.code === 'show-note';
      row.annotationsVisible = menu.code === 'show-note';
      row[showField] = menu.code === 'show-note' ? 1 : 0;
      if (levelType === 3) {
        if (menu.code === 'show-note') {
          isChangePage.value.push(row.sequenceNbr);
        } else {
          let idx = isChangePage.value.findIndex(a => a === row.sequenceNbr);
          isChangePage.value.splice(idx, 1);
        }
        upDate(row, showField);
      } else {
        //工程和单项不设置
        // formData = getParamsData({
        //   sequenceNbr: row.sequenceNbr,
        //   state: row[showField],
        //   type: levelType,
        // });
        // console.log('updatePostilState-formData', formData);
        // csProject.updatePostilState(formData).then(res => {
        //   console.log('updatePostilState', res, formData);
        // });
      }
      break;
    case 'del-all-note':
      if (levelType === 1) {
        infoMode.show({
          isSureModal: false,
          iconType: 'icon-querenshanchu',
          infoText: '是否删除所有批注？',
          confirm: async () => {
            delAllNote(1);
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
          },
        });
      } else {
        delNoteMol.value.visible = true;
        delNoteMol.value.dataStatus = levelType + '';
      }
      break;
  }
};
const delAllNote = levelType => {
  let levelTypeTrue = projectStore.currentTreeInfo?.levelType;
  let noteField = annotationsObj['noteFeild'][levelTypeTrue];
  let showField = annotationsObj['showFeild'][levelTypeTrue];
  if (levelTypeTrue < 3) {
    //单项+工程项目将所有数据的批注信息清空
    tableData.value.map(a => {
      a[showField] = null;
      a[noteField] = null;
      a.annotations = null;
      a.isShowAnnotations = null;
      a.annotationsVisible = false;
    });
  }
  // debugger;
  let formData = {
    type: levelType,
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (levelType === 3) {
    formData = getParamsData(formData);
  } else if (levelType === 2) {
    formData.singleId = projectStore.currentTreeInfo?.id;
  }
  console.log('deletePostilState-formData', formData);
  csProject.deletePostilState(formData).then(res => {
    console.log('deletePostilState', res, formData);
    if (levelTypeTrue === 3) getHumanMachineData();
    delNoteMol.value.visible = false;
  });
};
const cellMouseEnterEvent = ({ row, column }) => {
  // console.log('cellMouseEnterEvent', row, column);
  row.annotationsVisible = row.annotations?.length > 0;
  // tableData.value = [...tableData.value];
  // row.annotations = true;
  // row.annotationsVisible = true;
};
const cellMouseLeaveEvent = ({ row, column }) => {
  // console.log('cellMouseLeaveEvent', row, column);
  if (row.isShowAnnotations) return;
  row.annotationsVisible = false;
  console.log('cellMouseLeaveEvent', row, column);
};
// 关闭编辑的
const closeAnnotations = (v, row) => {
  if (!row.isShowAnnotations) {
    // row.noteViewVisible = false;
    row.annotationsVisible = false; //非常显状态 编辑之后关闭
  }
  row.noteEditVisible = false;
  if (v == row?.annotations) {
    return;
  }
  row.annotations = v;
  let levelType = projectStore.currentTreeInfo?.levelType;
  let field = annotationsObj['noteFeild'][levelType];
  row[field] = v;
  if (levelType === 3) {
    // debugger;
    upDate(row, field);
  } else {
    let formData = getParamsData({
      type: levelType,
      sequenceNbr: row.sequenceNbr,
      postil: row.annotations,
    });
    console.log('updatePostil-formData', formData);
    csProject.updatePostil(formData).then(res => {
      console.log('updatePostil', res, formData);
    });
  }
};
const onFocusNode = async (row, el) => {
  // if (AnnotationsCurrent.value != row.sequenceNbr) {
  //   console.log('🚀 ~手动选中');
  //   //editAnnotations(row);
  // }
  // await nextTick();
  // const element = el.target.parentNode.parentNode.parentNode.parentNode.parentNode;
  // setTimeout(()=>{
  //
  // })
  // element.style.setProperty('z-index', '101', 'important');
  // element.style.setProperty('display', 'block');
  // element.style['z-index ']= '101 important';
  // annotationClick(el);
};
const getAnnotationsRef = (el, row) => {
  // if (el) {
  //   AnnotationsRefList.value[row.sequenceNbr] = el;
  // } else {
  //   AnnotationsRefList.value[row.sequenceNbr] = null;
  // }
};
const annotationClick = el => {
  console.log('annotationClick', el);
  const element = el.target.parentNode.parentNode.parentNode.parentNode;
  element.style.setProperty('z-index', '101', 'important');
  // debugger
};

const visibleChange = (val, row) => {
  console.log(val, row);
  // if (
  //     !val &&
  //     (row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible)
  // ) {
  //   row.annotationsVisible =
  //       row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible;
  // }
  row.annotationsVisible = true;
};

const popoverDisplay = ({ $columnIndex, row, column }) => {
  if (['materialName'].includes(column.field) && row.annotationsVisible) {
    return true;
  }
  return false;
};

const deNameRef = triggerNode => {
  // console.log(
  //   'deNameRef',
  //   triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
  // );
  return triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
    .parentNode.parentNode.parentNode.parentNode;
  //气泡放在外面的情况
  // return document.querySelector('.table-edit-common .vxe-body--row');
};

const {
  isDragFill,
  cellMouseenterEvent,
  handleMouseDown,
  dragFillCellClassName,
  resetDragFillInfo,
  currentCellDragFill,
  displayedFields,
  handLabelDisplay,
} = useDragFillCell({ currentCellData, processingData, humanTable });
defineExpose({
  getHumanMachineData,
  posRow,
});
</script>
<style lang="scss" scoped>
.vxe-table--render-wrapper {
  transform: translateZ(0);
  will-change: transform;
}
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}
.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
  :deep(.background-red) {
    color: #2a2a2a;
    background: #de3f3f;
  }
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
  .row--current {
    background-color: var(--vxe-table-row-current-background-color) !important;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}
.selectTab {
  background-color: #e7e7e7;
  height: 32px;
  line-height: 30px;
  // padding-left: 20px;
  position: relative;
  border-bottom: 2px solid #e7e7e7;
  margin: 3px 0;
  .label {
    color: grey;
    font-size: 12px;
  }
  .showTitle {
    position: absolute;
    right: 0px;
    top: 0px;
    line-height: 30px;
    height: 30px;
    padding: 0 20px;
    font-size: 12px;
    // background-color: #e7e7e7;
    border-radius: 5px;
  }
  .ant-radio-button-wrapper {
    font-size: 12px;
    background-color: #e7e7e7;
    border: none;
    box-shadow: none;
    // border-radius: 5px;
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
  .ant-radio-button-wrapper-checked {
    // border-color: none;
    background-color: white;
    border: none;
    border-top: 3px solid #4786ff;
    color: black;
    &:hover {
      color: black;
    }
  }
}
.fill-handle {
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 5px;
  height: 5px;
  background: #419fff;
  cursor: crosshair;
  opacity: 0.8;
  z-index: 11;
}

.fill-handle:hover {
  opacity: 1;
}
.apreadModal {
  .single {
    border: 1px solid #d9d9d9;
    font-size: 14px;
    padding: 15px 0 15px 15px;
    margin-bottom: 12px;
    position: relative;
    .title {
      position: absolute;
      font-size: 12px;
      color: #287cfa;
      top: 5px;
      background: white;
      .icon-font {
        margin-right: 6px;
      }
    }
    .vxe-checkbox {
      color: black;
    }
  }
  .ant-radio-wrapper {
    font-size: 12px;
  }
  .footor {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0px;
    line-height: 30px;
  }
}
.area-modal {
  .vxe-modal--box {
    position: absolute;
  }
  .tree-content-wrap {
    width: 100%;
  }
  .footer-btn-list {
    margin-top: 25px;
  }
}
</style>
