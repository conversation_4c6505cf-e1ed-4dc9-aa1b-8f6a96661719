<!--
 * @Descripttion:加权规则弹框
-->
<template>
    <vxe-table  ref="tableEdit"  align="center" :column-config="{ resizable: true, isCurrent: false }"
                :row-config="{ isHover: false, isCurrent: true }" :mouse-config="{ selected: true }"
                :checkbox-config="{checkField: 'checked'}" :loading="loading"
                :data="tableData" cell-dblclick="dblclickItem" @checkbox-all="updateLoadNum" @checkbox-change="updateLoadNum"
                keep-source max-height="300" :min-height="300">
      <vxe-column type="checkbox" width="50" title=""/>
      <vxe-column field="date" min-width="75" title="期数"></vxe-column>
      <vxe-column field="up" min-width="100" title="加权比例" align='center'>
        <template #default ="{row,rowIndex }">
          <div v-if='row.checked' style="display: flex;align-items: center;justify-content: center;">
            <!-- @blur="() => { row.up = /^-?[0-9]+(\.[0-9]+)?$/.test(row.up) ? row.up: ''; if(row.up.trim() == ''){  row.up = 1; } } "-->
            <vxe-input :controls="false" :min="1" type="number" @change="updateLoadNum" v-model.value.number="row.up"
                       :clearable="false" style="width:60px;text-align: center;height:22px;line-height:22px;"
                       @input="(e)=>{
                       row.up = e.value.replace(/[^\d]/g, '');
                     }"
                       @blur="(e) => {
                       if(String(e.value)?.trim() === ''){  row.up = 1; }
                     }"/>
            <div :title="row.down" style="text-align: left;">/{{row.down}}</div>
          </div>
          <div v-else style="display: flex;align-items: center;justify-content: center;">
            <span style="width:60px;height:22px;line-height:22px;text-align: center;background: #FFFFFF;border-radius: 2px 2px 2px 2px;border: 1px solid var(--vxe-input-border-color)">比例</span>
          </div>
        </template>
      </vxe-column>
    </vxe-table>
    <div v-if="isShow" style="padding: 8px 8px; cursor: pointer;display: flex;justify-content: space-between;align-items: center;">
      <slot name="default"></slot>
      <!-- :disabled="disabled" -->
      <a-button size='small'  :disabled="disabled" type="primary" @click="okHandle()">确定</a-button>
    </div>
</template>
<script setup>
import {ref, defineProps, watch} from 'vue';
const props = defineProps({
  showDateList: {
    type: Array,
    default() {return []}
  } ,
  item: {
    type: Object,
    default() {return {
      isOpenShow: false,
      selectedList:[]
    }}
  },
  isShow:{
    type: Boolean,
    default: true
  }
});
const loading = ref(false);
const emit = defineEmits(['selectedRow']);
// let tableData = ref([{checked: false, materialCode:'2024年10月',materialName:''}]); //编辑弹框数据

let tableData = ref([]);

//因为不同的地方使用，数据基本的格式不同（历史原因），在传入时，虽然已经做了基本类型的统一，但是对面表格的展示不够，在这做二次处理
const setShowDateList = (showDateList)=>{
  tableData.value = showDateList.map((item) => {
    return { ...item, down:1, up:1, checked: item.checked || false }
  });
  return tableData;
}

setShowDateList(props.showDateList);
let sum = ref(0);
let disabled = ref(true);
//计算表格分母
const updateLoadNum =() => {
  let sumOfUp = tableData.value.reduce((accumulator, currentValue,index,list) => {
    if (currentValue.checked) {
      if(Number.isNaN(parseInt(currentValue.up))){
        currentValue.up = '1';
      }
      return accumulator + parseInt(currentValue.up);
    }
    return accumulator; // 如果checked不为true，则不改变累加器的值
  }, 0);
  tableData.value.forEach(item => {
    if (item.checked) {
      item.down = sumOfUp; // 注意：这将覆盖原有的down值
    }
  });
  disabled.value = !tableEdit.value.getCheckboxRecords().length;//解决确定是否可点
};
let tableEdit = ref();
const okHandle =() => {
  let selectedRowIds = tableEdit.value.getCheckboxRecords()||[];
  props.item.selectedList = selectedRowIds;

  props.item.dateVal = '加权平均载价';

  emit('selectedRow', selectedRowIds);
  props.item.isOpenShow = false;
};
// 数据回填勾选
const feedbackDateList = (resultList=[] )=>{
  loading.value = true;
  tableData.value.forEach((item) => {
    let isObj =  resultList.find(it=> it.yearMonths === item.date);
    if(isObj){
      item.checked = true;
      item.down = isObj.down;
      item.up = isObj.up;
    }
  });
  disabled.value = !tableEdit.value.getCheckboxRecords().length;//解决确定是否可点
  loading.value = false;
}
defineExpose({ setShowDateList,feedbackDateList,tableEdit });
</script>
<style lang="scss" scoped>
</style>
