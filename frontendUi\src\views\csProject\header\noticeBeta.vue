<template>
  <common-modal
    className="notice-beta modal betaHeader"
    title=" "
    width="963"
    height="590"
    v-model:modelValue="props.noticeBetaModal"
    :mask="false"
  >
    <div class="header">
      <span class="title">平台公告</span>
      <!-- <icon-font
        class="icon-font"
        type="icon-guanbi"
        @click="cancel"
      ></icon-font> -->
    </div>
    <div class="content">
      <p>亲爱的用户朋友们：</p>
      <div class="text">
        自云算房平台上线以来，我们开展了为期一段时间的免费公测，收获了大量宝贵的建议与支持。首先，衷心感谢每一位用户在公测期间的使用、反馈与陪伴，正是有了你们的参与，平台才能不断完善与成长。
      </div>
      <div class="detail">
        <div class="text">
          随着平台功能逐步稳定、产品体验持续优化，免费公测于2025年4月30日结束，平台将进入正式运营阶段。同时，为保障新老用户的平稳过渡，我们将在正式运营阶段执行如下使用规则:<br />
        </div>
        <!-- <div class="text">公测结束时间：<strong>2025年4月30日</strong></div> -->
      </div>
      <div class="detail">
        <!-- <div class="text">
          随着平台功能逐步稳定、产品体验持续优化，免费公测于2025年4月30日结束，平台将进入正式运营阶段。同时，为保障新老用户的平稳过渡，我们将在正式运营阶段执行如下使用规则: <br />
        </div> -->
        <div class="text">
          自正式运营起，所有新老用户可下载【云算房计价软件-学习版】享7天免费试用，完整体验平台各项功能；试用期结束后，用户可根据业务需求选择购买正式版服务。
        </div>
      </div>
      <div class="detail">
        <div class="text">
          我们将继续秉持“以客户为中心”的理念，努力打造更加专业、高效的平台服务体验。如果您在使用过程中有任何问题或建议，欢迎随时与我们联系。
          客服热线：<span class="blue">400-005-8008</span> <br />
        </div>
      </div>
      <div class="detail">
        <div class="text">
          再次感谢您在公测期间的信任与支持，未来，我们希望与您一同前行，携手共创价值！
          祝您工作顺利，万事顺意！
        </div>
      </div>
      <div class="foot-text">
        <div>平台运营团队</div>
        <div>2025年5月6日</div>
      </div>
    </div>
    <div class="footer">
      <!-- <a-button @click="openDialog">公测推荐</a-button> -->
      <a-button
        type="primary"
        @click="cancel"
      >知道了</a-button>
    </div>
  </common-modal>
</template>

<script setup>
const props = defineProps(['noticeBetaModal']);
const emits = defineEmits(['cancel', 'openDialog']);

const { shell } = require('electron');
// 假设你有一个链接地址
// 使用 shell 模块的 openExternal 方法打开链接

const openExternal = link => {
  shell.openExternal(link);
};

const openDialog = () => {
  emits('openDialog');
};

const cancel = () => {
  emits('cancel');
};
</script>

<style lang="scss" scoped>
.notice-beta {
  background: red;
  :deep(.vxe-modal--body .vxe-modal--content) {
    padding: 0;
  }
}
.header {
  background: url('@/assets/img/notice-img.png') no-repeat;
  width: 100%;
  height: 150px;
  position: relative;
  .title {
    display: block;
    font-size: 30px;
    color: #287cfa;
    text-align: center;
    line-height: 150px;
  }
  .icon-font {
    position: absolute;
    top: 24px;
    right: 24px;
  }
}
.content {
  font-size: 14px;
  color: #000000;
  padding: 0 54px 26px 54px;
  p {
    margin-bottom: 7px;
  }
  .text {
    line-height: 1.5;
    text-indent: 2em;
  }
  .foot-text {
    margin-top: 25px;
    text-align: right;
    line-height: 2;
  }
  .blue {
    color: #287cfa;
  }
  .red {
    color: #de3f3f;
  }
  .detail {
    margin-top: 7px;
  }
}
.qr-code-imgs {
  display: flex;
  justify-content: center;
  padding-top: 22px;
  .img {
    margin: 0 2px;
  }
  img {
    width: 85px;
    height: 85px;
  }
  .name {
    font-size: 12px;
    text-align: center;
    color: #333333;
  }
}
.footer {
  border-top: 1px solid #b9b9b9;
  padding: 15px 54px 0 0;
  text-align: right;
  button + button {
    margin-left: 10px;
  }
}
</style>
