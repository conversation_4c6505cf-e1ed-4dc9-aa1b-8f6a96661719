<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-23 17:58:36
-->
<template>
  <tab-menu @menuClick="menuClick"> </tab-menu>
  <div class="main-content">
    <sub-aside-tree
      ref="subAsideTreeRef"
      @onPreview="Preview"
      @onSelectReport="onSelectReport"
    ></sub-aside-tree>
    <div class="content" ref="content" @contextmenu.prevent>
      <div class="iframe-bar-list">
        <div
          class="bar-items"
          v-if="
            !(!globalData.headLine || !globalData.excelDataTemplate?.id) &&
            PreviewData
          "
          @click="editLandScape"
        >
          <icon-font type="icon-geshitiaozheng" class="icon" />
          <span v-if="isRapidDesign()" class="add-name">横版/竖版</span>
          <!-- v-if="isRapidDesign()" -->
          <span class="add-name" v-else>格式调整</span>
        </div>
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
          @click="editTable"
        >
          <icon-font type="icon-linshibianji" class="icon" />
          <span class="add-name">临时编辑</span>
        </div>
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
          @click="editReport"
        >
          <icon-font type="icon-biaogesheji" class="icon" />
          <span class="add-name">表格设计</span>
        </div>
        <!-- <div
          class="bar-items"
          @click="saveReportTemplate"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
        >
          <icon-font type="icon-baocunbaobiao" class="icon" />
          <span class="add-name">保存报表</span>
        </div>
        <div class="bar-items" @click="loadReportTemplate">
          <icon-font type="icon-daorubaobiao" class="icon" />
          <span class="add-name">导入报表</span>
        </div> -->
        <div class="bar-items" v-if="false" @click="print">
          <icon-font type="icon-dayin" class="icon" />
          <span class="add-name">打印</span>
        </div>
        <!-- <div
          class="bar-items"
          @click="setWeater"
          v-if="!(!globalData.headLine && !lanMuName.value)"
        >
          <icon-font type="icon-shuiyin" class="icon" />
          <span class="add-name">水印</span>
        </div> -->
        <div
          class="bar-items"
          @click="outFile('pdf')"
          v-if="!(!globalData.headLine && !lanMuName.value)"
        >
          <icon-font type="icon-daochuPDF" class="icon" />
          <span class="add-name">导出当前PDF</span>
        </div>
        <div
          class="bar-items"
          @click="outFile('excel')"
          v-if="!(!globalData.headLine && !lanMuName.value)"
        >
          <icon-font type="icon-daochuExcel" class="icon" />
          <span class="add-name">导出当前Excel</span>
        </div>
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
        >
          <icon-font type="icon-a-fangdasuoxiao" @click="zoomClick(0.25)" />
          <icon-font
            type="icon-suoxiao"
            @click="zoomClick(-0.25)"
            style="margin: 0 5px"
          />
          <a-select
            size="small"
            style="font-size: 12px"
            v-model:value="pdfScale.value"
            @change="zoomHandleChange"
          >
            <a-select-option
              :value="item.value"
              v-for="item of pdfScale.options"
              >{{ item.label }}</a-select-option
            >
          </a-select>
        </div>
      </div>
      <div class="iframe-content">
        <img
          src="@/assets/img/data-null.png"
          alt=""
          class="noData"
          v-if="!PreviewData"
        />
        <iframe
          v-if="fileUrl && PreviewData"
          id="myIframe"
          ref="iframeRef"
          @load="passDataToIframe"
          :src="fileUrl"
          style="width: 100%; height: 100%; border: 2px solid #e8e8e7"
        />
      </div>
    </div>
  </div>
  <export-file
    ref="exportFileRef"
    @handleOk="start"
    @refreshTree="refreshForm(globalData.headLine)"
  ></export-file>
  <schedule-file
    ref="scheduleFileRef"
    v-model:dialogVisible="showSchedule"
    strokeColor="#54a1f3"
    :percent="percent"
    :desc="showSchedule ? '正在导出，请稍后' : ''"
  ></schedule-file>

  <!-- 选择模板： -->
  <selectReport
    ref="selectReportRef"
    @handleOk="handleSelectReport"
  ></selectReport>

  <!-- 临时编辑的窗口 -->
  <common-modal
    v-model:modelValue="editExcelaStatus"
    className="dialog-comm dialog-EditExcelDialog"
    title="临时编辑"
    :width="800"
    :height="600"
    show-zoom="true"
    @close="closeEditExcelDialog"
  >
    <div style="display: none">临时编辑</div>
    <editExcel
      :data="excelData"
      v-if="editExcelDomStatus"
      teleportTo=".dialog-EditExcelDialog .ant-spin-container"
      :lanMuName="lanMuName"
    ></editExcel>
  </common-modal>

  <editExcel
    :data="excelData"
    :lanMuName="lanMuName"
    v-if="editExcelStatus"
    @getData="getData"
    @onClose="onClose"
  ></editExcel>

  <SystemReportForm
    ref="systemReportFormRef"
    @refreshForm="refreshForm"
  ></SystemReportForm>
  <RapidDesign
    ref="rapidDesignRef"
    @refresh="refreshForm(globalData.headLine)"
  ></RapidDesign>
  <export-xml-tip ref="tipsModal"></export-xml-tip>
  <ExportXMLMain></ExportXMLMain>
</template>

<script setup>
import editExcel from '@/components/editExcel.vue';
import selectReport from './selectReport.vue';

import ExportXMLMain from '@/components/ExportXMLMain/index.vue';
import { ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons-vue';
import {
  defineAsyncComponent,
  watch,
  ref,
  markRaw,
  onMounted,
  watchEffect,
  reactive,
  nextTick,
} from 'vue';
import tabMenu from './tabMenu.vue';
import csProject from '@/api/csProject';
import exportXmlTip from '@/components/exportXmlTip.vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import { downloadFileByUrl, delay } from '@/utils/index';
import { workBookData } from '@/components/reportTemplate/index.js';
import { globalData, getLocalStorage } from './reportFrom';
import xeUtils from 'xe-utils';
import Decimal from 'decimal.js';
import { projectDetailStore } from '@/store/projectDetail';

import shApiProject from '@/api/shApi';
import SystemReportForm from './components/SystemReportForm.vue';

const store = projectDetailStore();
const route = useRoute();
let exportFileRef = ref(null); // 导出文件
let showSchedule = ref(false); // 进度文件
let percent = ref(0); // 进度
let PreviewData = ref(false); // 预览数据
const fileUrl = ref();
const useType = ref(); // 弹窗里面选择导出的类型
let timer = ref(null);
let exportType = ref('excel'); // 点击导出类型

let pdfScale = reactive({
  value: 1,
  options: [
    { label: '200%', value: 2 },
    { label: '175%', value: 1.75 },
    { label: '150%', value: 1.5 },
    { label: '125%', value: 1.25 },
    { label: '100%', value: 1 },
    { label: '75%', value: 0.75 },
    { label: '50%', value: 0.5 },
  ],
});
const zoomClick = val => {
  const num = Decimal(pdfScale.value).add(val).toNumber();
  if (val > 0) {
    pdfScale.value = num >= 2 ? 2 : num;
  } else {
    pdfScale.value = num <= 0.5 ? 0.5 : num;
  }
  zoomHandleChange(pdfScale.value);
};
const zoomHandleChange = val => {
  iframeRef.value?.contentWindow.zoomHandle(val);
};
const props = defineProps({
  biddingType: {
    type: String,
    default: '',
  },
});
const ExportXmlLoading = ref(false);

const subAsideTree = defineAsyncComponent(() => import('./subAsideTree.vue'));
const exportFile = defineAsyncComponent(() => import('./exportFile.vue'));
const scheduleFile = defineAsyncComponent(() =>
  import('@/components/schedule/schedule.vue')
);

let excelData = ref([]);
let lanMuName = ref('');

onMounted(() => {});
// 选择报表
const handleSelectReport = ({ label, data }) => {
  // let [name,levelType,lanMuName,deType] = selectName.split('|')
  editExcelStatus.value = true;
  globalData.openEdit = true;
  let workData = xeUtils.clone(data, true);
  excelData.value = { ...workData, headLine: '', updateName: label };
};

/**
 * 开启导出进度条
 */
const start = ({ use_type, export_type }) => {
  useType.value = use_type;
  exportType.value = export_type;
  exportFileRef.value?.cancel();
  ProjectBar();
  timer.value = setInterval(() => {
    ProjectBar();
  }, 4000);
  showSchedule.value = true;
};

/**
 * 点击预览
 * @param {*} hasData 是否有报表数据
 */
const Preview = hasData => {
  fileUrl.value = null;
  PreviewData.value = hasData;
  pdfScale.value = 1;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html`;
  });
};
const refreshForm = headLine => {
  subAsideTreeRef.value.getTreeList(headLine || '');
};

let systemReportFormRef = ref();

/**
 * 点击打开导出文件弹窗
 * @param {*} type
 */
const menuClick = type => {
  if (type === 'xml') {
    exportXml();
    return;
  }
  if (['excel', 'pdf'].includes(type)) {
    exportFileRef.value?.open(type);
  }
  if (type === 'import') {
    loadReportTemplate();
  }
  if (type === 'save') {
    if (!globalData.headLine || !globalData.excelDataTemplate?.id) {
      return;
    }
    saveReportTemplate();
  }
  if (type === 'systemReportForm') {
    if (!globalData.headLine || !globalData.excelDataTemplate?.id) {
      return;
    }
    systemReportFormRef.value?.open();
  }
};

let tipsModal = ref(null);
// 点击了导出xml
const exportXml = async () => {
  let flag = await tipsModal.value.getFlag();
  console.log('store.projectType', store.projectType);
  if (!flag && store.projectType !== 'DW') {
    tipsModal.value.open();
    return;
  }
  infoMode.show({
    iconType: 'icon-tishineirong',
    infoText: '是否进行项目自检？',
    descText: '请注意：导出xml建议进行项目自检，避免出现不必要的错误',
    descTextStyle: { color: 'red' },
    confirm: () => {
      infoMode.hide();
      store.SET_CHECK_VISIBLE(true);
    },
    close: () => {
      infoMode.hide();
      store.SET_REPORT_NEXT_OPTION(true);
      // saveExportXml();
    },
  });
};

/**
 * 进度条
 */
const ProjectBar = async () => {
  try {
    const res = await csProject.exportProjectBar(
      route.query.constructSequenceNbr,
      useType.value,
      exportType.value
    );
    const { cloudStoragePath, progressBar, progressBarUnit, responseCode } =
      res.result;
    if (responseCode !== 200) {
      showToast();
      clearTimer();
      return;
    }

    percent.value = progressBar;

    if (progressBar === 100) {
      message.success('导出成功！');
      downloadFileByUrl(cloudStoragePath);
      delay(1000).then(() => {
        clearTimer();
      });
    }
  } catch (error) {
    clearTimer();
  }
};

const clearTimer = () => {
  clearInterval(timer.value);
  timer.value = null;
  percent.value = 0;
  showSchedule.value = false;
};

/**
 * 导出报表，报错，提示信息
 */
const showToast = () => {
  infoMode.show({
    infoText: '导出生成报表异常 请重新选择后导出',
    isSureModal: true,
    iconType: 'icon-qiangtixing',
    confirm: () => {
      console.log('确认事件触发');
      showSchedule.value = false;
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

let editExcelStatus = ref(false);

let selectReportRef = ref(null);
const onSelectReport = ({ isOpen, lanMuName: name }) => {
  lanMuName.value = name;
  excelData.value.headLine = '';
  if (isOpen) {
    selectReportRef.value.open({
      lanMuName: lanMuName.value,
    });
  }
};

let subAsideTreeRef = ref(null);
const getData = data => {
  console.log('data', data);
  onClose();
  subAsideTreeRef.value.getTreeList(data.headLine || 'last');
};

const onClose = () => {
  editExcelStatus.value = false;
  globalData.openEdit = false;
};

// 编辑表格模板
const editReport = () => {
  editExcelStatus.value = true;
  excelData.value = {
    ...xeUtils.clone(globalData.excelDataTemplate, true),
    headLine: globalData.headLine,
  };
  globalData.openEdit = true;
};

const generateRandomString = () => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

let editExcelaStatus = ref(false); // 临时编辑的窗口
let editExcelDomStatus = ref(false);
const editTable = () => {
  let data = getLocalStorage();
  let screenPPI = 110;
  let A4Height = (29.6 / 2.54) * 72;
  let rowCount = data?.rows.length;
  let rowData = {
    ...data?.rows.map(i => {
      i.h = 11.69 * screenPPI * i.ratio;
      // i.ia = true
      return i;
    }),
  };
  let columnData = {
    ...data?.columns.map(i => {
      i.w = i.width;
      return i;
    }),
  };
  let mergeData = data?.merges.map(i => {
    i.startRow = i.firstRow;
    i.endRow = i.lastRow;
    i.startColumn = i.firstCol;
    i.endColumn = i.lastCol;
    return i;
  });

  // 处理样式集合，默认加上自动换行
  Object.values(data.styles).forEach(i => {
    i.tb = 3;
  });

  let cellDataMap = new Map();
  data?.rows.forEach((item, k) => {
    let rowData = data?.cells
      .filter(i => {
        return i.rowIndex == k;
      })
      .map(j => {
        return {
          custom: { ...j },
          v: j.content?.richText ? j.content.richText : j.content,
          t: 1,
          s: j.styleId,
        };
      });

    // rowData.forEach(i=>{
    //   let styleId = generateRandomString()
    //   i.s = styleId
    //   styles[styleId] = {
    //     ff:'宋体'
    //   }
    // })
    cellDataMap.set(k, { ...rowData });
  });
  let cellData = {};
  Array.from(cellDataMap).forEach(e => {
    cellData[e[0]] = e[1];
  });
  let workdata = {
    name: '表结构转换',
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      openType: 'editTable',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: { ...data.styles },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData,
          name: 'Sheet1',
          hidden: 0,
          rowCount,
          columnCount: data?.columns.length,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 24,
          mergeData,
          rowData,
          columnData,
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
    },
  };

  // editExcelStatus.value = true;
  excelData.value = { ...workdata.data, headLine: globalData.headLine };
  // globalData.openEdit = true
  editExcelaStatus.value = true;

  nextTick(() => {
    editExcelDomStatus.value = true;
  });
};
const closeEditExcelDialog = () => {
  editExcelaStatus.value = false;
  editExcelDomStatus.value = false;
};
const RapidDesign = defineAsyncComponent(() =>
  import('./components/RapidDesign.vue')
);
const rapidDesignRef = ref();
const isRapidDesign = () => {
  const headLine = globalData.headLine;
  const list = ['扉页', '封面', '填表须知', '表1-1 ', '表1-2 '];
  return list.some(item => headLine.indexOf(item) > -1);
};
const editLandScape = () => {
  console.log('🚀 ~ editLandScape ~ postData.globalData:', globalData);

  if (!isRapidDesign()) {
    rapidDesignRef.value.open({ lanMuName: lanMuName.value });
    return;
  }
  let landSpace = getLocalStorage()?.print?.landSpace;
  let postData = {
    ...globalData.treeParams,
    ...globalData.treeParams.constructObj,
    headLine: globalData.headLine,
    lanMuName: lanMuName.value,
    landScape: !landSpace,
  };
  delete postData.constructObj;
  console.log('🚀 ~ editLandScape ~ postData:', postData);

  csProject.landScapeChange(postData).then(res => {
    subAsideTreeRef.value.getTreeList();
  });
};

let iframeRef = ref(null);
let setWeaterStatus = ref(false);
const setWeater = () => {
  setWeaterStatus.value = !setWeaterStatus.value;
  iframePostMessage(setWeaterStatus.value);
};

// 导出PDF
const outFile = async (type = 'pdf') => {
  const list = await csProject.exportProjectTree(
    route.query.constructSequenceNbr,
    lanMuName.value,
    'pdf'
  );
  removeExcelDataTemplate([list.result]);
  const postList = flattenTree([list.result])[0];
  let params = {
    lanMuName: lanMuName.value,
    params: JSON.parse(JSON.stringify(postList)),
    projectName: globalData.headLine,
  };
  let apiName = 'exportPdfFile';
  if (type === 'excel') {
    Object.assign(params, { startPage: '', totalPage: '' });
    apiName = 'exportSingleSheetExcel';
  }
  console.log('🚀 ~ outPdf ~ params:', params);
  const res = await csProject[apiName](params);
  console.log('🚀 ~ outPdf ~ res:', res);
  if (res?.result) {
    message.success('导出成功！');
  }
};

const getOutList = () => {
  csProject
    .exportProjectTreeSH(
      route.query.constructSequenceNbr,
      // useType.value,
      'pdf'
    )
    .then(res => {
      console.log('数数据', res.result);
      if (res.status === 200 && res.result) {
        treeData.value = [res.result];
        expandedKeys.value.push(treeData.value[0].id);

        // for (const item of treeData.value[0].childrenList) {
        //   expandedKeys.value.push(item.id)
        // }
      }
    });
};

/**
 * 将树结构里面的某个字段与选中的数据进行关联
 * @param {*} treeList
 */
const flattenTree = treeList => {
  const result = [];
  let id = store.currentTreeInfo?.id;
  if (props.biddingType === 2) {
    id = store.currentTreeInfo?.parentId;
  }
  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中
    if (node.childrenList && node.childrenList.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.childrenList.length; i++) {
        const data = node.childrenList[i];
        data.selected = data.headLine == globalData.headLine && id == node?.id;
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected = root.headLine == globalData.headLine;
    traverse(root);
  }

  return result;
};

const removeExcelDataTemplate = tree => {
  // 遍历树的每一个节点
  tree.forEach(node => {
    // 删除当前节点的 excelDataTemplate 属性
    delete node.excelDataTemplate;
    // 如果当前节点有子节点，则递归调用此函数处理子节点
    if (node.childrenList && Array.isArray(node.childrenList)) {
      removeExcelDataTemplate(node.childrenList);
    }
  });
};

// js 清除报表数据里面的
const handlePdfList = newList => {};

let doms = ref(null);
let iframeWindow = ref();
//向子页面iframe传参
const passDataToIframe = event => {
  nextTick(() => {
    const iframeWindow = event.target.contentWindow;
    iframeWindow.value = iframeWindow;
    console.log('🌶mainContent.vue|747====>', iframeWindow);
    doms.value = iframeWindow.document.querySelectorAll('.watermark');
    iframePostMessage();
    if (isPrint.value) {
      iframeWindow.printPage();
      isPrint.value = false;
    }
  });
};

// 根据状态，设置dom显示隐藏
const iframePostMessage = () => {
  doms.value.forEach(i => {
    i.style.display = setWeaterStatus.value ? 'block' : 'none';
  });
};

let isPrint = ref(false);
const print = () => {
  isPrint.value = true;
  fileUrl.value = null;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html`;
  });
};

// 保存报表
const saveReportTemplate = () => {
  let itemLevel = '';
  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    itemLevel = itemLevelList[levelType - 1];
  }
  const postData = {
    jsonData: {
      ...xeUtils.clone(globalData.excelDataTemplate, true),
      headLine: globalData.headLine,
      itemLevel,
    },
    constructName: store.projectName,
  };
  console.log('保存报表参数', postData);
  csProject.saveReportTemplateToLocal(postData).then(res => {
    if (res?.result) {
      message.success('导出成功！');
    }
  });
};

const itemLevelList = markRaw(['project', 'single', 'unit']);

// 导入报表
const loadReportTemplate = () => {
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: null,
    unitId: null,
    lanMuName: lanMuName.value,
    itemLevel: '',
    constructName: store.projectName,
  };
  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    postData.itemLevel = itemLevelList[levelType - 1];

    switch (levelType) {
      case 1:
        postData.constructId = route.query?.constructSequenceNbr;
        postData.singleId = null;
        break;
      case 2:
        postData.singleId = store.currentTreeInfo.id;
        postData.unitId = null;
        break;
      case 3:
        postData.singleId = store.currentTreeInfo?.parentId;
        postData.unitId = store.currentTreeInfo.id;
        break;
    }
  }
  console.log('导入报表参数', postData);
  csProject.loadReportTemplateFromLocal(postData).then(res => {
    console.log('🚀 ~ csProject.landScapeChange ~ res:', res);
    if (res.status == 200) {
      if (res?.result) {
        message.success('导入成功！');
        subAsideTreeRef.value.getTreeList();
      }
    } else {
      message.error('导入失败！');
    }
  });
};
</script>
<style lang="scss" scoped>
.main-content {
  display: flex;
  height: calc(100vh - 139px);
}
.content {
  position: relative;
  flex: 1;
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(214, 214, 214, 1);
  .iframe-bar-list {
    padding: 8px 6px;
    width: 100%;
    height: 37px;
    background: #f8fbff;
    border-bottom: 1px solid rgba(214, 214, 214, 1);
    display: flex;
    align-items: center;
    .bar-items {
      display: flex;
      margin-left: 14px;
      align-items: center;
      cursor: pointer;
      .icon {
        font-size: 16px;
      }
      .add-name {
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        margin-left: 7px;
        &:hover {
          color: #409eff;
        }
      }
    }
  }
  .iframe-content {
    flex: 1;
    flex-shrink: 0;
  }
  .noData {
    position: absolute;
    width: 274px;
    height: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
